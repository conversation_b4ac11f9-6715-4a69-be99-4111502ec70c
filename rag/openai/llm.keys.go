package openai

import (
	"strings"
	"sync"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

var DefaultModel = "qwen2.5-7b"

var FastAPI = w.MapStrStr{
	"url": "http://43.153.222.223:8810/v1/chat/completions",
	"key": "sk-5f5c21f24622b17a4c17c39fdf49b5e7",
}

// var FastAPI = w.MapStrStr{
// 	"url": "https://apichat.rhecs.com/v1/chat/completions",
// 	"key": "sk-yZt9smqbDtVsCSpL307d37E5A31d4503Bf147dB187C2Be30",
// }

var LLM_Keys = w.Map{
	// 免费模型系列
	"qwen2.5-7b": w.Map{
		"model": "Qwen/Qwen2.5-7B-Instruct",
		"url":   "https://api.siliconflow.cn/v1/chat/completions",
		"token": w.<PERSON>liceStr{
			"sk-zjtdzijmuylwbhufsvguqasrtqqdzxvdpfekzcnrswtnoher", // 17701058496
			"sk-zgimbicibzmbunblwjcpizzbycjjidyssttdnfwglashzmmv", // 13700343692
			"sk-uroafwfvqkjntjmmmvfopeppupqjxtzfhyzuktiadvurhsul", // 15632627397
		},
	},
	// 很适合长上下文的模型
	"minimax-text-01": w.Map{
		"model": "MiniMax-Text-01",
		"url":   "https://api.minimaxi.com/v1/text/chatcompletion_v2",
		"token": w.SliceStr{
			"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
		},
	},
	// 硅基deepseek-v3 免费版
	"deepseek-v3-siliconflow": w.Map{
		"model": "deepseek-ai/DeepSeek-V3",
		"url":   "https://api.siliconflow.cn/v1/chat/completions",
		"token": w.SliceStr{
			"sk-zgimbicibzmbunblwjcpizzbycjjidyssttdnfwglashzmmv", // 硅基1年企业key
		},
	},
	// 火山方舟
	"deepseek-v3-vol": w.Map{
		"model": "deepseek-v3-250324",
		"url":   "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
		"token": w.SliceStr{
			"b9da5177-a7d4-41c9-a4ae-cf4b7498a9f6", // parkhansung
		},
	},
	// 火山方舟
	"deepseek-v3-vol-search": w.Map{
		"model": "bot-20250217112501-pffww",
		"url":   "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions",
		"token": w.SliceStr{
			"b9da5177-a7d4-41c9-a4ae-cf4b7498a9f6", // parkhansung
		},
	},
	// 高速sop
	"deepseek-v3-sop": w.Map{ //
		"model": "DeepSeek-V3-Fast",
		"url":   "https://www.sophnet.com/api/open-apis/v1/chat/completions",
		"token": w.SliceStr{
			"Tc5xzxCiyVwmXzgjO-a6edZ4umzBQA_c3PO8jvLjiGQdAmo3Q85YxUMIA-9dr0L6o2hcE_22POUtKQvAqrQMIw",
		},
	},
	// 高速sop
	"deepseek-v3-sam": w.Map{ //
		"model": "DeepSeek-V3-0324",
		"url":   "https://api.sambanova.ai/v1/chat/completions",
		"token": w.SliceStr{
			"f46fee69-00c7-40d9-aaf1-d9cd28ccf0a1",
		},
	},
	"spark": w.Map{ // 星火 主要查天气什么的方便
		"model": "generalv3.5",
		"url":   "https://spark-api-open.xf-yun.com/v1/chat/completions",
		"token": w.SliceStr{
			"cbnCuBABHFugreDnkasS:QuZLANXdTKoSXVOZmahP",
		},
	},
	// 超高速模型系列
	"qwen3-235b-a22b-fast": w.Map{
		"model": "qwen3-235b-a22b-fast",
		"url":   FastAPI["url"],
		"token": w.SliceStr{FastAPI["key"]},
	},
	"qwen3-32b-fast": w.Map{
		"model": "qwen3-32b-fast",
		"url":   FastAPI["url"],
		"token": w.SliceStr{FastAPI["key"]},
	},
	"kimi-k2-fast": w.Map{
		"model": "kimi-k2-fast",
		"url":   FastAPI["url"],
		"token": w.SliceStr{FastAPI["key"]},
	},
}

// Key轮询管理器的全局变量
var (
	keyIndex int        // 全局索引
	keyMutex sync.Mutex // 保证并发安全
)

// 获取模型配置
func LLMKey(model ...string) LLMConfig {
	// _defaultModel := vant2.LodashGetMap(LLM_Keys, DefaultModel)
	_defaultModel := gconv.Map(LLM_Keys[DefaultModel])
	if len(model) == 0 || model[0] == "" {
		return LLMConfig{
			DefaultModel: _defaultModel["model"].(string),
			BaseURL:      _defaultModel["url"].(string),
			ApiKey:       GetKeyFromList(_defaultModel["token"].([]string)),
		}
	}

	_model := vant2.LodashGetMap(LLM_Keys, strings.Trim(model[0], " "))
	if _model != nil {
		return LLMConfig{
			DefaultModel: _model["model"].(string),
			BaseURL:      _model["url"].(string),
			ApiKey:       GetKeyFromList(_model["token"].([]string)),
		}
	} else {
		return LLMConfig{
			DefaultModel: _defaultModel["model"].(string),
			BaseURL:      _defaultModel["url"].(string),
			ApiKey:       GetKeyFromList(_defaultModel["token"].([]string)),
		}
	}

}

// 从列表中获取key (支持轮询)
func GetKeyFromList(keys []string) string {
	if len(keys) == 0 {
		return ""
	}
	if len(keys) == 1 {
		return keys[0]
	}

	keyMutex.Lock()
	defer keyMutex.Unlock()

	// 获取当前key
	key := keys[keyIndex]

	// 移动到下一个索引（轮询）
	keyIndex = (keyIndex + 1) % len(keys)

	return key
}
