package openaiplus

import (
	"assistant/rag/openai"
	"fmt"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/robertkrimen/otto"
)

// 算术运算
func ToolMath(messages []g.Map) (string, string) {
	_prompt := vant2.GetLocalFile("prompt/prompt/math_input.md")
	llm := openai.New(openai.LLMKey("deepseek-v3-sop")).System(_prompt)
	result := llm.Chat(openai.GenerateDialogContent(messages))
	if result.HasError() {
		return "", ""
	}
	_tools := result.GetTools()
	_math := vant2.LodashGetString(_tools, "0.output")

	return _math, Math(_math) // 直接抽取公式开干
}

func Math(js string) string {
	// 创建一个otto VM
	vm := otto.New()

	// 执行JavaScript代码
	value, err := vm.Run(js)
	if err != nil {
		fmt.Println("执行出错:", err)
		return ""
	}

	// 将结果转换为Go的值
	result, err := value.ToString()
	if err != nil {
		fmt.Println("结果转换出错:", err)
		return ""
	}
	return result
}
