package openaiplus

import (
	"io/ioutil"
	"net/http"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/util/gconv"
)

// 从最新的对话内提取链接
func ToolOpenUrl(content string) string {
	_urls, _ := vant2.ExtractHttpUrls(content)
	if len(_urls) > 0 {
		_webInfo := ""
		for i, v := range _urls {
			_info := OpenUrl(v)
			_webInfo += "【" + gconv.String(i+1) + "】链接【" + v + "】\n网页内容：" + _info + "\n"
		}
		return "当前调用网页访问工具，访问用户提供的网址链接，得到的内容是：" + _webInfo + "\n\n 请根据网页内容，回答用户的问题"
	}
	return ""
}

// 打开访问页面
func OpenUrl(url string) string {
	// 对url进行处理和判断
	_uri := url
	if _uri == "" {
		return ""
	}
	// 如果url不包含http:// 或者https:// 则补上
	if !strings.HasPrefix(_uri, "http://") && !strings.HasPrefix(_uri, "https://") {
		_uri = "http://" + _uri
	}

	client := &http.Client{
		Timeout: 3 * time.Second, // 超时设置
	}
	vant2.Error(_uri)
	// 发起GET请求
	resp, err := client.Get(_uri)
	if err != nil {
		return "" // 处理错误，可能是超时或其他网络问题
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "" // 读取响应的内容
	}

	htmlContent := string(body)
	//vantplus.Error(htmlContent)
	return vant2.RemoveHtml(htmlContent)
}
