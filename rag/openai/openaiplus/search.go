package openaiplus

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"sync"
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

type WebSearch struct {
}

type SearchAttr struct {
	Keyword   string
	Count     int
	Freshness string
	Site      w.SliceStr // 指定网站
}

var BING_KEYS = w.SliceStr{
	"********************************",
	"1ae5b55c1a3749859e0ea6987ee2450a",
}

// Bing Key轮询管理器的全局变量
var (
	bingKeyIndex int        // 全局索引
	bingKeyMutex sync.Mutex // 保证并发安全
)

// 从Bing keys列表中获取key (支持轮询)
func GetBingKeyFromList(keys w.SliceStr) string {
	if len(keys) == 0 {
		return ""
	}
	if len(keys) == 1 {
		return keys[0]
	}

	bingKeyMutex.Lock()
	defer bingKeyMutex.Unlock()

	// 获取当前key
	key := keys[bingKeyIndex]

	// 移动到下一个索引（轮询）
	bingKeyIndex = (bingKeyIndex + 1) % len(keys)

	return key
}

// 搜索api
func (d *WebSearch) WebApi(attr *SearchAttr) w.SliceMap {
	if attr.Keyword == "" {
		return nil
	}
	const endpoint = "https://api.bing.microsoft.com/v7.0/search"
	token := GetBingKeyFromList(BING_KEYS)
	vant2.Error(token, "token")

	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		panic(err)
	}

	// Add the payload to the request.
	param := req.URL.Query()
	_key := attr.Keyword
	if attr.Site != nil && len(attr.Site) > 0 {
		for _index, _site := range attr.Site {
			if _site != "" {
				if _index == 0 {
					_key = _key + " site:" + _site
				} else {
					_key = _key + " | site:" + _site
				}
			}
		}
	}
	vant2.Error(_key, "searchKey")
	param.Add("q", _key) // 关键词
	param.Add("mkt", "zh-CN")
	if attr.Freshness != "" {
		param.Add("freshness", "Month")
	}
	_count := 8
	if attr.Count > 0 {
		_count = attr.Count
	}

	param.Add("count", gconv.String(_count))
	//param.Add("responseFilter", "webpages,news")
	req.URL.RawQuery = param.Encode()

	// Insert the request header.
	req.Header.Add("Ocp-Apim-Subscription-Key", token)

	// Instantiate a client.
	client := new(http.Client)
	// Send the request to Bing.
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}

	// Close the connection.
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}

	ans := new(BingAnswer)
	json.Unmarshal(body, &ans)
	var _list w.SliceMap
	if len(ans.WebPages.Value) > 0 {
		for _, result := range ans.WebPages.Value {
			_list = append(_list, w.Map{
				"title":       result.Name,
				"webUrl":      result.URL,
				"description": result.Snippet,
			})
		}
	}
	return _list
}

// 结构体
type BingAnswer struct {
	Type         string `json:"_type"`
	QueryContext struct {
		OriginalQuery string `json:"originalQuery"`
	} `json:"queryContext"`
	WebPages struct {
		WebSearchURL          string `json:"webSearchUrl"`
		TotalEstimatedMatches int    `json:"totalEstimatedMatches"`
		Value                 []struct {
			ID               string    `json:"id"`
			Name             string    `json:"name"`
			URL              string    `json:"url"`
			IsFamilyFriendly bool      `json:"isFamilyFriendly"`
			DisplayURL       string    `json:"displayUrl"`
			Snippet          string    `json:"snippet"`
			DateLastCrawled  time.Time `json:"dateLastCrawled"`
			SearchTags       []struct {
				Name    string `json:"name"`
				Content string `json:"content"`
			} `json:"searchTags,omitempty"`
			About []struct {
				Name string `json:"name"`
			} `json:"about,omitempty"`
		} `json:"value"`
	} `json:"webPages"`
	RelatedSearches struct {
		ID    string `json:"id"`
		Value []struct {
			Text         string `json:"text"`
			DisplayText  string `json:"displayText"`
			WebSearchURL string `json:"webSearchUrl"`
		} `json:"value"`
	} `json:"relatedSearches"`
	RankingResponse struct {
		Mainline struct {
			Items []struct {
				AnswerType  string `json:"answerType"`
				ResultIndex int    `json:"resultIndex"`
				Value       struct {
					ID string `json:"id"`
				} `json:"value"`
			} `json:"items"`
		} `json:"mainline"`
		Sidebar struct {
			Items []struct {
				AnswerType string `json:"answerType"`
				Value      struct {
					ID string `json:"id"`
				} `json:"value"`
			} `json:"items"`
		} `json:"sidebar"`
	} `json:"rankingResponse"`
}
