package openaiplus

import (
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Controller Web搜索控制器
type Controller struct{}

// SearchDemo 搜索演示接口
func (c *Controller) SearchDemo(r *ghttp.Request) {
	// 获取查询参数
	keyword := r.Get("keyword").String()
	if keyword == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    400,
			"message": "请提供搜索关键词",
			"example": "/search/demo?keyword=人工智能",
		})
		return
	}

	// 可选参数
	count := r.Get("count").Int()
	if count <= 0 {
		count = 5 // 默认返回5条结果
	}

	freshness := r.Get("freshness").String() // Month, Week, Day
	site := r.Get("site").String()           // 指定网站搜索

	// 构建搜索参数
	searchAttr := &SearchAttr{
		Keyword:   keyword,
		Count:     count,
		Freshness: freshness,
	}

	// 处理指定网站参数
	if site != "" {
		searchAttr.Site = w.SliceStr{site}
	}

	// 创建搜索实例并执行搜索
	webSearch := &WebSearch{}
	results := webSearch.WebApi(searchAttr)

	// 返回结果
	r.Response.WriteJsonExit(g.Map{
		"code":    200,
		"message": "搜索成功",
		"data": g.Map{
			"keyword":     keyword,
			"count":       len(results),
			"results":     results,
			"search_url":  "https://api.bing.microsoft.com/v7.0/search",
			"key_rotated": true, // 表示使用了轮询key
		},
	})
}

// SearchIndex 搜索首页
func (c *Controller) SearchIndex(r *ghttp.Request) {
	html := `
<!DOCTYPE html>
<html>
<head>
    <title>Bing Web Search Demo</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #eee; border-radius: 4px; }
        .result h3 { margin: 0 0 10px 0; color: #1a0dab; }
        .result .url { color: #006621; font-size: 14px; }
        .result .description { margin: 10px 0; color: #545454; }
        .loading { color: #666; font-style: italic; }
        .error { color: #d93025; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Bing Web Search Demo</h1>
        <p>演示 Bing API 轮询调用功能</p>
        
        <form id="searchForm">
            <div class="form-group">
                <label for="keyword">搜索关键词:</label>
                <input type="text" id="keyword" name="keyword" placeholder="请输入搜索关键词" required>
            </div>
            
            <div class="form-group">
                <label for="count">结果数量:</label>
                <select id="count" name="count">
                    <option value="5">5条</option>
                    <option value="8">8条</option>
                    <option value="10">10条</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="freshness">时间范围:</label>
                <select id="freshness" name="freshness">
                    <option value="">不限制</option>
                    <option value="Day">最近一天</option>
                    <option value="Week">最近一周</option>
                    <option value="Month">最近一月</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="site">指定网站:</label>
                <input type="text" id="site" name="site" placeholder="例如: github.com (可选)">
            </div>
            
            <button type="submit">🚀 开始搜索</button>
        </form>
        
        <div id="results"></div>
    </div>

    <script>
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const keyword = document.getElementById('keyword').value;
            const count = document.getElementById('count').value;
            const freshness = document.getElementById('freshness').value;
            const site = document.getElementById('site').value;
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">🔄 搜索中...</div>';
            
            // 构建查询参数
            const params = new URLSearchParams({
                keyword: keyword,
                count: count
            });
            
            if (freshness) params.append('freshness', freshness);
            if (site) params.append('site', site);
            
            // 发送请求
            fetch('/search/demo?' + params.toString())
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        displayResults(data.data);
                    } else {
                        resultsDiv.innerHTML = '<div class="error">❌ ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<div class="error">❌ 请求失败: ' + error.message + '</div>';
                });
        });
        
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            if (!data.results || data.results.length === 0) {
                resultsDiv.innerHTML = '<div class="error">😔 没有找到相关结果</div>';
                return;
            }
            
            let html = '<h2>🎯 搜索结果 (' + data.count + '条)</h2>';
            html += '<p><strong>关键词:</strong> ' + data.keyword + ' | <strong>Key轮询:</strong> ✅</p>';
            
            data.results.forEach(function(result, index) {
                html += '<div class="result">';
                html += '<h3>' + (index + 1) + '. ' + result.title + '</h3>';
                html += '<div class="url">' + result.webUrl + '</div>';
                html += '<div class="description">' + result.description + '</div>';
                html += '</div>';
            });
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
    `;

	r.Response.Write(html)
}
