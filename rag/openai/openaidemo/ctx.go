package openaidemo

import (
	"assistant/rag/openai"
	"fmt"
	"strings"
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// TestIndex LLM测试首页
func TestIndex(r *ghttp.Request) {
	html := `<!DOCTYPE html><html><head><meta charset="utf-8"><title>LLM 大模型对话测试</title><style>body{font:14px Arial;margin:20px;background:#f8f9fa}h1{color:#333}button{background:#007cba;color:#fff;border:0;padding:8px 16px;margin:4px;border-radius:4px;cursor:pointer;font-size:13px}button:hover{background:#005a87}.result{margin:10px 0;padding:15px;background:#e7f3ff;border-radius:4px;font-family:monospace;white-space:pre-wrap;max-height:500px;overflow-y:auto;border:1px solid #ddd}.section{margin:15px 0;padding:10px;background:#fff;border-radius:4px;border:1px solid #eee}.section h3{margin:0 0 10px 0;color:#555}</style></head><body><h1>🤖 LLM 大模型对话测试工具</h1>

<div class="section">
<h3>📝 基础功能测试</h3>
<button onclick="test('/test/basic-chat')">基础对话</button>
<button onclick="test('/test/system-message')">系统消息</button>
<button onclick="test('/test/context-chat')">上下文对话</button>
</div>

<div class="section">
<h3>⚙️ 高级功能测试</h3>
<button onclick="test('/test/chained-call')">链式调用</button>
<button onclick="test('/test/advanced-config')">高级配置</button>
<button onclick="test('/test/clone-reset')">克隆重置</button>
</div>

<div class="section">
<h3>🚀 特殊功能测试</h3>
<button onclick="testStream('/test/stream-chat')">流式对话 (SSE)</button>
<button onclick="test('/test/tools-call')">工具调用</button>
<button onclick="testStream('/test/sse-tools-detection')">SSE工具检测</button>
<button onclick="testStream('/test/real-tools-workflow')">真实工具调用流程(SSE)</button>
<button onclick="test('/test/real-tools-workflow-api')">真实工具调用流程(API)</button>
<button onclick="test('/test/custom-config')">自定义配置</button>
<button onclick="test('/test/format-json')">FormatJson测试</button>
<button onclick="test('/test/performance-analysis')">性能分析</button>
<button onclick="testStream('/test/smart-chat-context')">智能对话上下文(SSE)</button>
<button onclick="test('/test/smart-chat-context-api')">智能对话上下文(API)</button>
</div>

<div class="section">
<h3>🖼️ 多模态测试</h3>
<button onclick="test('/test/multimodal-api')">多模态对话(API)</button>
<button onclick="testStream('/test/multimodal-sse')">多模态对话(SSE)</button>
</div>

<div class="section">
<h3>📊 测试说明</h3>
<p>• <strong>基础对话</strong>: 测试简单问答功能</p>
<p>• <strong>系统消息</strong>: 测试角色设定功能</p>
<p>• <strong>上下文对话</strong>: 测试多轮对话记忆</p>
<p>• <strong>链式调用</strong>: 测试参数链式设置</p>
<p>• <strong>流式对话</strong>: 测试实时流式输出</p>
<p>• <strong>工具调用</strong>: 测试Function Calling</p>
<p>• <strong>SSE工具检测</strong>: 专门检测SSE模式下的工具调用功能</p>
<p>• <strong>真实工具调用流程(SSE)</strong>: 严格OpenAI SSE格式，动态检测工具→执行API→继续对话</p>
<p>• <strong>真实工具调用流程(API)</strong>: API版本，与SSE功能完全一致，结果完全相同</p>
<p>• <strong>高级配置</strong>: 测试所有参数组合</p>
<p>• <strong>克隆重置</strong>: 测试实例复用功能</p>
<p>• <strong>自定义配置</strong>: 测试传入配置对象</p>
<p>• <strong>FormatJson测试</strong>: 测试JSON格式化和getTools对象提取</p>
<p>• <strong>性能分析</strong>: 详细的时间统计和TPS性能分析</p>
<p>• <strong>智能对话上下文(SSE)</strong>: 智能问题分类→路由处理→上下文回答(流式版本)</p>
<p>• <strong>智能对话上下文(API)</strong>: 智能问题分类→路由处理→上下文回答(API版本)</p>
</div>

<div id="result"></div>

<script>
// 普通测试
async function test(url) {
    const r = document.getElementById('result');
    r.innerHTML = '<div class="result">⏳ 执行中: ' + url + '</div>';
    try {
        const res = await fetch(url);
        const data = await res.json();
        r.innerHTML = '<div class="result"><strong>✅ 请求:</strong> ' + url + '\n\n<strong>📊 结果:</strong>\n' + JSON.stringify(data, null, 2) + '</div>';
    } catch (e) {
        r.innerHTML = '<div class="result"><strong>❌ 错误:</strong> ' + url + '\n\n' + e.message + '</div>';
    }
}

// 流式测试
async function testStream(url) {
    const r = document.getElementById('result');
    r.innerHTML = '<div class="result">⏳ 流式测试中: ' + url + '\n\n</div>';

    try {
        const response = await fetch(url);
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        let streamContent = '';

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                    try {
                        const data = JSON.parse(line.substring(6));
                        streamContent += JSON.stringify(data, null, 2) + '\n';
                    } catch (e) {
                        streamContent += line + '\n';
                    }
                }
            }

            r.innerHTML = '<div class="result"><strong>🚀 流式输出:</strong> ' + url + '\n\n' + streamContent + '</div>';
        }

        r.innerHTML += '\n<strong>✅ 流式输出完成</strong>';
    } catch (e) {
        r.innerHTML = '<div class="result"><strong>❌ 流式错误:</strong> ' + url + '\n\n' + e.message + '</div>';
    }
}
</script>

</body></html>`
	r.Response.Header().Set("Content-Type", "text/html; charset=utf-8")
	r.Response.Write(html)
}

// TestBasicChat 基础对话测试
func TestBasicChat(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 基础对话测试
	llm := openai.New()
	result := llm.Chat("你是谁？")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("基础对话测试成功", g.Map{
		"content": result.GetContent(),
		"tokens":  result.GetTokenUsage(),
	})
}

// TestSystemMessage 系统消息测试
func TestSystemMessage(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 系统消息测试
	llm := openai.New()
	result := llm.System("你是一个专业的Go语言专家").System("你也精通Vue").AddSystemTime().Chat("Go支持三元表达式吗？直接回复支持还是不支持 不要扯其他的！")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("系统消息测试成功", g.Map{
		"content": result.GetContent(),
		"tokens":  result.GetTokenUsage(),
	})
}

// TestContextChat 上下文对话测试
func TestContextChat(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 上下文对话测试
	history := []g.Map{
		{"role": "user", "content": "我叫张三"},
		{"role": "assistant", "content": "你好张三，很高兴认识你"},
		{"role": "user", "content": "我是一名程序员"},
		{"role": "assistant", "content": "太棒了！作为程序员，你主要使用什么编程语言呢？"},
	}

	llm := openai.New()
	result := llm.AddSystemTime().Messages(history).Chat("你还记得我的名字吗？回答我，我叫什么名字，言简意赅，不要扯其他东西")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("上下文对话测试成功", g.Map{
		"content": result.GetContent(),
		"tokens":  result.GetTokenUsage(),
	})
}

// TestChainedCall 链式调用测试
func TestChainedCall(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 链式调用测试
	llm := openai.New()
	result := llm.
		MaxTokens(1000).
		Temperature(0.7).
		System("你是一个创意写作助手").
		AddSystemTime().
		ReturnJson().
		Chat("写一个关于AI的短故事")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("链式调用测试成功", g.Map{
		"content": result.GetContent(),
		"tokens":  result.GetTokenUsage(),
	})
}

// TestStreamChat 流式对话测试
func TestStreamChat(r *ghttp.Request) {
	// 设置SSE响应头
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")

	// 流式对话测试
	llm := openai.New()

	// 设置流式回调
	streamFunc := func(sseResult *openai.SSEResult) {
		// 构建响应数据
		responseData := g.Map{
			"raw_data":        sseResult.Raw(),
			"current_content": sseResult.Content(),
			"total_content":   sseResult.ContentTotal(),
			"token_usage":     sseResult.GetTokenUsage(),
			"tools":           sseResult.GetTools(),
			"tools_count":     len(sseResult.GetTools()),
			"is_complete":     sseResult.IsComplete(),
			"content_length":  len(sseResult.ContentTotal()),
		}

		// 发送到客户端
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(responseData) + "\n\n"))
		r.Response.Flush()
	}

	result := llm.
		Stream(streamFunc).
		System("你是一个详细解释专家").
		Chat("请详细解释什么是机器学习")

	if result.HasError() {
		errorData := g.Map{
			"type":  "error",
			"error": result.GetError(),
		}
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(errorData) + "\n\n"))
	}

	// 发送结束信号
	r.Response.Write([]byte("data: [DONE]\n\n"))
	r.Response.Flush()
}

// TestToolsCall 工具调用测试
func TestToolsCall(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 定义测试工具
	tools := []g.Map{
		{
			"type": "function",
			"function": g.Map{
				"name":        "get_weather",
				"description": "获取指定城市的天气信息",
				"parameters": g.Map{
					"type": "object",
					"properties": g.Map{
						"city": g.Map{
							"type":        "string",
							"description": "城市名称",
						},
					},
					"required": []string{"city"},
				},
			},
		},
	}

	llm := openai.New()
	result := llm.
		Tools(tools, "auto").
		System("你可以查询天气信息").
		Chat("北京今天天气怎么样？")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("工具调用测试成功", g.Map{
		"content": result.GetContent(),
		"tools":   result.GetTools(),
		"tokens":  result.GetTokenUsage(),
	})
}

// TestAdvancedConfig 高级配置测试
func TestAdvancedConfig(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 高级配置测试
	llm := openai.New()
	result := llm.
		// Model("gpt-4").
		MaxTokens(2000).
		Temperature(0.3).
		Stop("结束", "完毕").
		EnableThinking().
		System("你是一个数学专家").
		System("请用简洁明了的方式回答").
		AddSystemTime().
		Chat("解释什么是微积分")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("高级配置测试成功", g.Map{
		"content": result.GetContent(),
		"tokens":  result.GetTokenUsage(),
	})
}

// TestCloneAndReset 克隆和重置测试
func TestCloneAndReset(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 创建基础配置
	baseLLM := openai.New().
		// Model("gpt-3.5-turbo").
		MaxTokens(500).
		System("你是一个友好的助手")

	// 克隆配置用于不同任务
	mathLLM := baseLLM.Clone().System("专门回答数学问题")
	codeResult := baseLLM.Clone().System("专门回答编程问题").Chat("如何用Go语言实现斐波那契数列？")

	// 使用克隆的实例
	mathResult := mathLLM.Chat("什么是斐波那契数列？")

	if mathResult.HasError() || codeResult.HasError() {
		_back.ApiError(-1, "测试失败")
		return
	}

	// 重置测试
	baseLLM.Reset().System("你是一个全能助手")
	generalResult := baseLLM.Chat("你好")

	_back.ApiSuccess("克隆和重置测试成功", g.Map{
		"math_result":    mathResult.GetContent(),
		"code_result":    codeResult.GetContent(),
		"general_result": generalResult.GetContent(),
	})
}

// TestCustomConfig 自定义配置测试
func TestCustomConfig(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 自定义配置测试
	config := openai.LLMConfig{
		BaseURL:      "https://api.siliconflow.cn/v1/chat/completions",
		ApiKey:       "sk-your-custom-key",
		DefaultModel: "Qwen/Qwen2-7B-Instruct",
	}

	llm := openai.New(config)
	result := llm.
		System("你是一个专业的助手").
		Chat("请介绍一下你的模型和配置")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	_back.ApiSuccess("自定义配置测试成功", g.Map{
		"content": result.GetContent(),
		"tokens":  result.GetTokenUsage(),
		"config": g.Map{
			"base_url":     config.BaseURL,
			"model":        config.DefaultModel,
			"api_key_hint": config.ApiKey[:10] + "...", // 只显示前10位
		},
	})
}

// TestFormatJson 格式化JSON测试
func TestFormatJson(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 测试FormatJson功能
	llm := openai.New()
	result := llm.
		System("你是一个数据处理专家，请严格按JSON格式返回").
		FormatJson().
		Chat("请返回一个包含用户信息的JSON对象，包含name、age、city字段")

	if result.HasError() {
		_back.ApiError(-1, result.GetError())
		return
	}

	// 演示通过getTools获取对象
	tools := result.GetTools()

	_back.ApiSuccess("FormatJson测试成功", g.Map{
		"content":           result.GetContent(),
		"tokens":            result.GetTokenUsage(),
		"tools_count":       len(tools),
		"tools":             tools,
		"extracted_objects": tools, // 通过getTools获取的对象
		"demo_note":         "无论是tools、formatJson还是普通对话，都可以通过getTools获取object对象",
	})
}

// TestSSEToolsDetection SSE工具检测测试
func TestSSEToolsDetection(r *ghttp.Request) {
	// 设置SSE响应头
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")

	// 定义测试工具
	tools := []g.Map{
		{
			"type": "function",
			"function": g.Map{
				"name":        "get_user_info",
				"description": "获取用户信息",
				"parameters": g.Map{
					"type": "object",
					"properties": g.Map{
						"user_id": g.Map{
							"type":        "string",
							"description": "用户ID",
						},
					},
					"required": []string{"user_id"},
				},
			},
		},
	}

	// 创建LLM实例
	llm := openai.New()

	// 设置流式回调
	streamFunc := func(sseResult *openai.SSEResult) {
		// 构建检测结果
		detectionData := g.Map{
			"检测类型":    "SSE工具检测",
			"原始数据":    sseResult.Raw(),
			"当前内容":    sseResult.Content(),
			"累积内容":    sseResult.ContentTotal(),
			"内容长度":    len(sseResult.ContentTotal()),
			"Token使用": sseResult.GetTokenUsage(),
			"工具调用数量":  len(sseResult.GetTools()),
			"工具调用详情":  sseResult.GetTools(),
			"是否完成":    sseResult.IsComplete(),
		}

		// 特别检查工具调用
		tools := sseResult.GetTools()
		if len(tools) > 0 {
			detectionData["🎯工具检测成功"] = true
			detectionData["工具类型分析"] = g.Map{}

			for i, tool := range tools {
				toolType := "未知类型"
				if functionName := gconv.String(tool["function"]); functionName != "" {
					toolType = "原生工具调用"
				} else if gconv.String(tool["name"]) != "" || gconv.String(tool["user_id"]) != "" {
					toolType = "JSON解析工具"
				}

				key := fmt.Sprintf("工具%d", i+1)
				detectionData["工具类型分析"].(g.Map)[key] = g.Map{
					"类型":   toolType,
					"内容":   tool,
					"JSON": vant2.JsonEncoder(tool),
				}
			}
		} else {
			detectionData["🎯工具检测成功"] = false
		}

		// 发送检测结果
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(detectionData) + "\n\n"))
		r.Response.Flush()

		// 控制台详细日志
		vant2.Error(g.Map{
			"当前内容片段":   sseResult.Content(),
			"累积内容长度":   len(sseResult.ContentTotal()),
			"工具数量":     len(sseResult.GetTools()),
			"工具详情":     sseResult.GetTools(),
			"Token详情":  sseResult.GetTokenUsage(),
			"是否包含JSON": strings.Contains(sseResult.ContentTotal(), "{"),
			"是否完成":     sseResult.IsComplete(),
		}, "=== SSE 工具检测详细日志 ===")
	}

	// 执行带工具的流式对话
	result := llm.
		Tools(tools, "auto").
		Stream(streamFunc).
		System("你是一个用户信息查询助手，可以调用工具获取用户信息").
		Chat("请帮我查询用户ID为12345的用户信息")

	if result.HasError() {
		errorData := g.Map{
			"类型":   "错误",
			"错误信息": result.GetError(),
		}
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(errorData) + "\n\n"))
	}

	// 发送最终检测报告
	finalReport := g.Map{
		"类型":   "最终报告",
		"测试说明": "此接口专门检测SSE模式下的工具调用功能",
		"检测项目": []string{
			"原生tools工具调用检测",
			"JSON格式解析工具检测",
			"内容累积功能检测",
			"Token统计功能检测",
		},
		"完成时间": vant2.Time(),
	}
	r.Response.Write([]byte("data: " + vant2.JsonEncoder(finalReport) + "\n\n"))

	// 发送结束信号
	r.Response.Write([]byte("data: [DONE]\n\n"))
	r.Response.Flush()
}

// TestPerformanceAnalysis 性能分析测试
func TestPerformanceAnalysis(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 执行多个对话以比较性能
	results := make([]g.Map, 0)

	// 测试1: 基础对话性能
	llm1 := openai.New()
	result1 := llm1.
		System("你是一个简洁的助手").
		Chat("请用一句话解释什么是AI")

	if !result1.HasError() {
		results = append(results, g.Map{
			"测试类型":    "基础对话",
			"对话内容":    result1.GetContent(),
			"内容长度":    len(result1.GetContent()),
			"Token统计": result1.GetTokenUsage(),
			"性能报告":    (&result1.TokenUsage).GetPerformanceReport(),
		})
	}

	// 测试2: JSON格式化性能
	llm2 := openai.New()
	result2 := llm2.
		System("你是一个数据处理专家").
		FormatJson().
		Chat("请返回一个包含姓名、年龄、城市的用户信息JSON")

	if !result2.HasError() {
		results = append(results, g.Map{
			"测试类型":    "JSON格式化",
			"对话内容":    result2.GetContent(),
			"内容长度":    len(result2.GetContent()),
			"Token统计": result2.GetTokenUsage(),
			"性能报告":    (&result2.TokenUsage).GetPerformanceReport(),
			"解析的工具":   result2.GetTools(),
		})
	}

	// 测试3: 复杂对话性能
	llm3 := openai.New()
	result3 := llm3.
		System("你是一个详细的技术解释专家").
		MaxTokens(500).
		Temperature(0.7).
		Chat("请详细解释机器学习的三个主要类型并给出实际应用例子")

	if !result3.HasError() {
		results = append(results, g.Map{
			"测试类型":    "复杂对话",
			"对话内容":    result3.GetContent(),
			"内容长度":    len(result3.GetContent()),
			"Token统计": result3.GetTokenUsage(),
			"性能报告":    (&result3.TokenUsage).GetPerformanceReport(),
		})
	}

	// 计算性能对比
	performanceComparison := g.Map{
		"测试数量":   len(results),
		"性能指标对比": g.Map{},
	}

	if len(results) > 0 {
		var totalTPS float64
		var totalApiDuration int64
		var totalTotalDuration int64

		for i, result := range results {
			tokenUsage := result["Token统计"].(openai.TokenUsage)
			key := fmt.Sprintf("测试%d", i+1)

			performanceComparison["性能指标对比"].(g.Map)[key] = g.Map{
				"类型":       result["测试类型"],
				"TPS":      tokenUsage.TPS,
				"API用时毫秒":  tokenUsage.ApiCallDuration,
				"总用时毫秒":    tokenUsage.TotalDuration,
				"完成tokens": tokenUsage.CompletionTokens,
				"单token耗时": func() float64 {
					if tokenUsage.CompletionTokens > 0 && tokenUsage.ApiCallDuration > 0 {
						return float64(tokenUsage.ApiCallDuration) / float64(tokenUsage.CompletionTokens)
					}
					return 0
				}(),
			}

			totalTPS += tokenUsage.TPS
			totalApiDuration += tokenUsage.ApiCallDuration
			totalTotalDuration += tokenUsage.TotalDuration
		}

		// 平均性能指标
		performanceComparison["平均性能"] = g.Map{
			"平均TPS":     totalTPS / float64(len(results)),
			"平均API用时毫秒": totalApiDuration / int64(len(results)),
			"平均总用时毫秒":   totalTotalDuration / int64(len(results)),
		}
	}

	_back.ApiSuccess("性能分析测试完成", g.Map{
		"测试结果": results,
		"性能对比": performanceComparison,
		"说明": g.Map{
			"时间统计":  "所有时间精确到毫秒",
			"TPS计算": "每秒token数 = CompletionTokens / (API用时毫秒/1000)",
			"包含指标": []string{
				"请求开始时间",
				"API调用开始时间",
				"响应完成时间",
				"总用时",
				"API调用用时",
				"TPS性能指标",
			},
		},
		"测试时间": time.Now().Format("2006-01-02 15:04:05.000"),
	})
}

// TestMultimodalAPI 多模态对话API测试
func TestMultimodalAPI(r *ghttp.Request) {
	// 多模态内容：文本+图片
	content := w.SliceMap{
		{"type": "text", "text": "描述这张图片"},
		{"type": "image_url", "image_url": w.Map{"url": "https://www.baidu.com/img/flexible/logo/pc/result.png"}},
	}

	// 发送给AI
	llm := openai.New().Model("Pro/Qwen/Qwen2.5-VL-7B-Instruct").System("图片分析助手")
	result := llm.Chat(content)

	// 保存到数据库 (解除注释即可使用)
	// ChatHistoryDB().SaveMessage(1001, 100, time.Now().Unix(), "user", content)
	// ChatHistoryDB().SaveMessage(1001, 100, time.Now().Unix(), "assistant", result.GetContent())

	r.Response.WriteJsonExit(g.Map{
		"success": !result.HasError(),
		"content": result.GetContent(),
		"note":    "多模态内容已发送并保存",
	})
}

// TestMultimodalSSE 多模态对话SSE测试
func TestMultimodalSSE(r *ghttp.Request) {
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")

	// 多模态内容
	content := w.SliceMap{
		{"type": "text", "text": "用诗意语言描述这张图片"},
		{"type": "image_url", "image_url": w.Map{"url": "https://www.baidu.com/img/flexible/logo/pc/result.png"}},
	}

	// 流式对话
	llm := openai.New().Model("Pro/Qwen/Qwen2.5-VL-7B-Instruct").Stream(func(result *openai.SSEResult) {
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(g.Map{
			"content": result.Content(),
			"done":    result.IsComplete(),
		}) + "\n\n"))
		r.Response.Flush()
	})

	result := llm.Chat(content)

	// 保存到数据库 (解除注释即可使用)
	// ChatHistoryDB().SaveMessage(1001, 100, time.Now().Unix(), "user", content)
	// ChatHistoryDB().SaveMessage(1001, 100, time.Now().Unix(), "assistant", result.GetContent())

	// 确保result被使用
	_ = result

	r.Response.Write([]byte("data: [DONE]\n\n"))
}
