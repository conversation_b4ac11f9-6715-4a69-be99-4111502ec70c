# LLM 大模型对话工具链

## 📖 简介

LLM是专为大模型对话设计的Go工具链，提供智能对话管理、流式回调、工具调用、性能统计等功能。支持链式调用，配置灵活，性能监控完整。

## 📋 API概览

### 🔧 配置方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `New(config...)` | LLMConfig | 创建实例 | `mc.New()` |
| `Model(model)` | string | 设置模型名称 | `llm.Model("gpt-4")` |
| `MaxTokens(tokens)` | int64 | 设置最大token | `llm.MaxTokens(4096)` |
| `Temperature(temp)` | float32 | 设置温度参数 | `llm.Temperature(0.7)` |
| `ApiKey(key)` | string | 设置API密钥 | `llm.ApiKey("sk-...")` |
| `BaseURL(url)` | string | 设置API地址 | `llm.BaseURL("https://api.xxx.com")` |

### 🎯 对话配置方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `System(content)` | string | 添加系统消息 | `llm.System("你是AI助手")` |
| `AddSystemTime()` | - | 添加系统时间 | `llm.AddSystemTime()` |
| `Messages(messages)` | []g.Map | 设置对话消息 | `llm.Messages(dialogHistory)` |
| `Tools(tools, choice...)` | []g.Map, string | 设置工具列表 | `llm.Tools(toolList, "auto")` |
| `Stream(callback)` | func(*SSEResult) | 设置流式回调 | `llm.Stream(onStream)` |

### 🚀 高级功能方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `ReturnJson()` | - | 返回JSON格式 | `llm.ReturnJson()` |
| `FormatJson()` | - | 强制JSON格式 | `llm.FormatJson()` |
| `EnableThinking()` | - | 启用思维链 | `llm.EnableThinking()` |
| `Stop(words...)` | ...string | 设置停止词 | `llm.Stop("结束", "完成")` |
| `Reset()` | - | 重置实例 | `llm.Reset()` |
| `Clone()` | - | 克隆实例 | `newLLM := llm.Clone()` |

### 📊 结果获取方法
| 方法 | 返回值 | 说明 | 示例 |
|------|--------|------|------|
| `GetContent()` | string | 获取对话结果 | `result.GetContent()` |
| `GetTools()` | []g.Map | 获取工具调用 | `result.GetTools()` |
| `GetTokenUsage()` | TokenUsage | 获取性能统计 | `result.GetTokenUsage()` |
| `HasError()` | bool | 检查是否有错误 | `result.HasError()` |
| `GetError()` | string | 获取错误信息 | `result.GetError()` |

## 🚀 快速开始

### 基础对话
```go
import "openai/rag/openai"

// 创建实例（使用默认配置）
llm := mc.New()

// 配置基本参数
llm.Model("Qwen/Qwen2.5-7B-Instruct").
    Temperature(0.7).
    MaxTokens(2048)

// 设置系统消息
llm.System("你是一个有用的AI助手").
    AddSystemTime()

// 设置对话历史
messages := []g.Map{
    {"role": "user", "content": "什么是人工智能？"},
}
llm.Messages(messages)

// 执行对话并获取结果
result := llm.Chat() // 这里需要实际的Chat方法
content := result.GetContent()
tokenUsage := result.GetTokenUsage()
```

### 流式对话
```go
// 设置流式回调
llm.Stream(func(result *mc.SSEResult) {
    if result.Content() != "" {
        fmt.Print(result.Content()) // 实时输出内容片段
    }

    if result.IsComplete() {
        fmt.Println("\n对话完成!")
        fmt.Printf("总内容: %s\n", result.ContentTotal())
        fmt.Printf("性能统计: %+v\n", result.GetTokenUsage())
    }
})

// 执行流式对话
llm.StreamChat() // 这里需要实际的StreamChat方法
```

### 工具调用
```go
// 定义工具
tools := []g.Map{
    {
        "type": "function",
        "function": g.Map{
            "name": "get_weather",
            "description": "获取天气信息",
            "parameters": g.Map{
                "type": "object",
                "properties": g.Map{
                    "city": g.Map{
                        "type": "string",
                        "description": "城市名称",
                    },
                },
                "required": []string{"city"},
            },
        },
    },
}

// 配置工具调用
llm.Tools(tools, "auto").
    System("你可以使用工具获取天气信息")

// 执行对话
result := llm.Chat()
if len(result.GetTools()) > 0 {
    fmt.Printf("工具调用: %+v\n", result.GetTools())
}
```

## 📚 完整API参考

### 🔧 实例创建和配置

#### New(config ...LLMConfig) *LLM
创建新的LLM实例
```go
// 使用默认配置
llm := mc.New()

// 使用自定义配置
config := mc.LLMConfig{
    BaseURL:      "https://api.openai.com/v1/chat/completions",
    ApiKey:       "sk-your-api-key",
    DefaultModel: "gpt-4",
}
llm := mc.New(config)
```

#### Model(model string) *LLM
设置模型名称
```go
llm.Model("gpt-4")                           // OpenAI模型
llm.Model("Qwen/Qwen2.5-7B-Instruct")      // SiliconFlow模型
llm.Model("claude-3-sonnet")                 // Anthropic模型
```

#### MaxTokens(tokens int64) *LLM
设置最大token数
```go
llm.MaxTokens(4096)  // 设置最大4096 tokens
llm.MaxTokens(8192)  // 设置最大8192 tokens
```

#### Temperature(temp float32) *LLM
设置温度参数（0.0-2.0）
```go
llm.Temperature(0.0)  // 最保守，输出确定性强
llm.Temperature(0.7)  // 平衡创造性和确定性
llm.Temperature(1.5)  // 高创造性，输出更随机
```

#### ApiKey(key string) *LLM
设置API密钥
```go
llm.ApiKey("sk-your-api-key-here")
```

#### BaseURL(url string) *LLM
设置API基础URL
```go
llm.BaseURL("https://api.openai.com/v1/chat/completions")      // OpenAI
llm.BaseURL("https://api.siliconflow.cn/v1/chat/completions") // SiliconFlow
```

### 🎯 对话配置方法

#### System(content string) *LLM
添加系统消息
```go
llm.System("你是一个专业的编程助手")
llm.System("请用中文回答所有问题")
```

#### AddSystemTime() *LLM
自动添加当前系统时间
```go
llm.AddSystemTime() // 添加: "当前系统时间是： 2025-01-23 17:30:00"
```

#### Messages(messages []g.Map) *LLM
设置对话消息列表
```go
messages := []g.Map{
    {"role": "user", "content": "你好"},
    {"role": "assistant", "content": "您好！有什么可以帮您的吗？"},
    {"role": "user", "content": "介绍一下Go语言"},
}
llm.Messages(messages)
```

#### Tools(tools []g.Map, choice ...string) *LLM
设置工具列表和选择策略
```go
// 自动选择工具（默认）
llm.Tools(toolList)
llm.Tools(toolList, "auto")

// 强制使用工具
llm.Tools(toolList, "required")

// 禁用工具
llm.Tools(toolList, "none")
```

#### Stream(callback func(*SSEResult)) *LLM
设置流式回调函数
```go
llm.Stream(func(result *mc.SSEResult) {
    // 处理内容片段
    if content := result.Content(); content != "" {
        fmt.Print(content)
    }

    // 处理工具调用
    if tools := result.GetTools(); len(tools) > 0 {
        fmt.Printf("工具调用: %+v\n", tools)
    }

    // 对话完成
    if result.IsComplete() {
        fmt.Printf("\n总用时: %dms\n", result.GetTokenUsage().TotalDuration)
    }
})
```

### 🚀 高级功能方法

#### ReturnJson() *LLM / FormatJson() *LLM
强制返回JSON格式
```go
llm.ReturnJson()  // 要求模型返回JSON
llm.FormatJson()  // 强制JSON格式并解析为Tools
```

#### EnableThinking() *LLM
启用思维链模式
```go
llm.EnableThinking() // 启用CoT（Chain of Thought）
```

#### Stop(stopWords ...string) *LLM
设置停止词
```go
llm.Stop("结束", "完成", "END")
```

#### Reset() *LLM
重置LLM实例，清除所有配置
```go
llm.Reset() // 清除所有消息和工具配置
```

#### Clone() *LLM
克隆LLM实例
```go
newLLM := llm.Clone() // 创建相同配置的新实例
```

### 📊 结果处理方法

#### ToolResult 核心方法
```go
// 获取对话内容
content := result.GetContent()

// 获取工具调用
tools := result.GetTools()
for _, tool := range tools {
    name := tool["name"]
    args := tool["arguments"]
}

// 获取性能统计
usage := result.GetTokenUsage()
fmt.Printf("Token使用: %+v\n", usage)

// 错误处理
if result.HasError() {
    fmt.Printf("错误: %s\n", result.GetError())
}
```

#### SSEResult 流式方法
```go
// 在流式回调中使用
llm.Stream(func(result *mc.SSEResult) {
    // 当前片段内容
    current := result.Content()

    // 累积总内容
    total := result.ContentTotal()

    // 获取工具调用
    tools := result.GetTools()

    // 获取性能统计
    usage := result.GetTokenUsage()

    // 检查是否完成
    if result.IsComplete() {
        fmt.Println("对话完成")
    }
})
```

## 🎪 核心功能详解

### 性能统计系统
LLM内置完整的性能监控系统：

```go
usage := result.GetTokenUsage()

// Token统计
fmt.Printf("提示tokens: %d\n", usage.PromptTokens)
fmt.Printf("完成tokens: %d\n", usage.CompletionTokens)
fmt.Printf("总tokens: %d\n", usage.TotalTokens)

// 时间统计（格式化显示）
fmt.Printf("请求开始: %s\n", usage.RequestStartTime)     // "2025-01-23 17:30:00.123"
fmt.Printf("API调用开始: %s\n", usage.ApiCallStartTime)   // "2025-01-23 17:30:00.456"
fmt.Printf("响应完成: %s\n", usage.ResponseCompleteTime) // "2025-01-23 17:30:03.789"

// 性能指标
fmt.Printf("总用时: %dms\n", usage.TotalDuration)
fmt.Printf("API用时: %dms\n", usage.ApiCallDuration)
fmt.Printf("TPS: %s tokens/秒\n", usage.TPS) // 格式化为2位小数
```

### 工具调用系统
支持OpenAI标准的Function Calling：

```go
// 1. 定义工具
tools := []g.Map{
    {
        "type": "function",
        "function": g.Map{
            "name": "calculator",
            "description": "执行数学计算",
            "parameters": g.Map{
                "type": "object",
                "properties": g.Map{
                    "expression": g.Map{
                        "type": "string",
                        "description": "数学表达式",
                    },
                },
                "required": []string{"expression"},
            },
        },
    },
}

// 2. 配置工具
llm.Tools(tools, "auto")

// 3. 执行对话
result := llm.Chat()

// 4. 处理工具调用
for _, tool := range result.GetTools() {
    name := gconv.String(tool["name"])
    args := gconv.String(tool["arguments"])

    if name == "calculator" {
        // 执行计算逻辑
        calculatorResult := executeCalculator(args)

        // 返回工具结果继续对话
        // ...
    }
}
```

### 流式对话处理
实时获取对话片段和状态：

```go
llm.Stream(func(result *mc.SSEResult) {
    // 获取原始SSE数据
    rawData := result.Raw()

    // 实时内容处理
    if content := result.Content(); content != "" {
        // 实时显示
        fmt.Print(content)

        // 可以进行实时处理，如敏感词过滤
        if containsSensitiveWords(content) {
            // 处理敏感内容
        }
    }

    // 工具调用处理
    if tools := result.GetTools(); len(tools) > 0 {
        // 实时处理工具调用
        for _, tool := range tools {
            processToolCall(tool)
        }
    }

    // 完成处理
    if result.IsComplete() {
        finalContent := result.ContentTotal()
        finalUsage := result.GetTokenUsage()

        // 保存对话记录
        saveChatHistory(finalContent, finalUsage)
    }
})
```

## 📊 数据结构详解

### LLMConfig 配置结构
```go
type LLMConfig struct {
    BaseURL      string `json:"base_url"`      // API基础URL
    ApiKey       string `json:"api_key"`       // API密钥
    DefaultModel string `json:"default_model"` // 默认模型
}
```

### TokenUsage 性能统计
```go
type TokenUsage struct {
    // Token统计
    PromptTokens     int64   `json:"prompt_tokens"`     // 提示token数
    CompletionTokens int64   `json:"completion_tokens"` // 完成token数
    TotalTokens      int64   `json:"total_tokens"`      // 总token数

    // 时间统计（自动格式化为可读时间）
    RequestStartTime     string `json:"request_start_time"`     // "2025-01-23 17:30:00.123"
    ApiCallStartTime     string `json:"api_call_start_time"`    // "2025-01-23 17:30:00.456"
    ResponseCompleteTime string `json:"response_complete_time"` // "2025-01-23 17:30:03.789"

    // 用时统计（毫秒）
    TotalDuration   int64  `json:"total_duration"`    // 总用时
    ApiCallDuration int64  `json:"api_call_duration"` // API调用用时

    // 性能指标（格式化为2位小数）
    TPS string `json:"tps"` // "23.45" tokens/秒
}
```

### Message 消息结构
```go
type Message struct {
    Role    string      `json:"role"`    // "user" | "assistant" | "system"
    Content interface{} `json:"content"` // 消息内容
}
```

### ToolResult 结果结构
```go
type ToolResult struct {
    Content     string     `json:"content"`      // 对话结果
    Tools       []g.Map    `json:"tools"`        // 工具调用结果
    TokenUsage  TokenUsage `json:"token_usage"`  // token消耗信息
    RawResponse g.Map      `json:"raw_response"` // 原始响应
    Error       string     `json:"error"`        // 错误信息
}
```

## 🔧 实用工具函数

### SSE数据处理工具
```go
// 从SSE数据中提取内容
content := mc.GetContentFromSSE(sseData)

// 从SSE数据中提取token使用情况
usage := mc.GetTokenUsageFromSSE(sseData)

// 判断SSE数据类型
dataType := mc.GetSSEDataType(sseData)
// 返回: "content" | "tool_calls" | "usage" | "done" | "error" | "unknown"
```

## ⚡ 性能优化建议

### 1. 模型选择
- **快速响应**: 使用较小模型（7B-13B）
- **高质量**: 使用大模型（70B+）
- **平衡**: 使用中等模型（30B-70B）

### 2. Token管理
- 合理设置MaxTokens避免截断
- 使用System消息控制输出格式
- 定期清理历史消息

### 3. 流式处理
- 使用Stream获得更好的用户体验
- 在回调中进行实时处理
- 避免在回调中执行耗时操作

### 4. 工具调用优化
- 工具描述要清晰准确
- 参数定义要完整
- 合理控制工具数量（建议≤10个）

## 🚨 常见问题

### Q: 如何处理API限流？
**A**:
```go
llm.Stream(func(result *mc.SSEResult) {
    if result.HasError() {
        error := result.GetError()
        if strings.Contains(error, "rate limit") {
            // 处理限流，如延迟重试
            time.Sleep(time.Second * 5)
        }
    }
})
```

### Q: 如何确保工具调用格式正确？
**A**: 使用`FormatJson()`强制JSON格式，并在工具定义中明确参数类型：
```go
llm.FormatJson().Tools(tools)
```

### Q: 流式对话中如何处理网络中断？
**A**: 在Stream回调中检查错误状态：
```go
llm.Stream(func(result *mc.SSEResult) {
    if result.HasError() {
        // 处理网络错误
        handleNetworkError(result.GetError())
    }
})
```

### Q: 如何选择合适的Temperature？
**A**:
- **代码生成/逻辑推理**: 0.0-0.3
- **日常对话**: 0.5-0.8
- **创意写作**: 0.8-1.2
- **头脑风暴**: 1.2-2.0

### Q: 性能统计的时间精度是多少？
**A**: 毫秒级精度，包含完整的请求生命周期：
- `RequestStartTime`: 接收请求时间
- `ApiCallStartTime`: 开始调用API时间
- `ResponseCompleteTime`: 响应完成时间

## 🔗 相关链接

- **项目地址**: [openai/rag/openai](.)
- **模型配置**: [model.go](model.go)
- **性能监控**: TokenUsage结构体
- **流式处理**: SSEResult结构体

---

📝 **最后更新**: 2025年1月
🏷️ **版本**: v1.0.0
👨‍�� **维护者**: LLM工具链团队