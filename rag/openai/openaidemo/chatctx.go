package openaidemo

import (
	"assistant/rag/openai"
	"fmt"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// SimpleCategory 简单分类结果
type SimpleCategory struct {
	Category string `json:"category"` // 只返回分类：order_query, product_inquiry, normal_chat
}

// TestSmartChatContext 智能对话上下文演示（SSE版本）
func TestSmartChatContext(r *ghttp.Request) {
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")

	userInput := r.Get("user_input", "帮我查询订单123456").String()

	// SSE回调
	streamFunc := func(stage string, data interface{}) {
		sseData := g.Map{
			"id":      fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()),
			"object":  "chat.context.chunk",
			"created": time.Now().Unix(),
			"stage":   stage,
			"data":    data,
		}
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(sseData) + "\n\n"))
		r.Response.Flush()
	}

	result := _processSimpleChat(userInput, streamFunc)

	streamFunc("completed", result)
	r.Response.Write([]byte("data: [DONE]\n\n"))
	r.Response.Flush()
}

// TestSmartChatContextAPI 智能对话上下文演示（API版本）
func TestSmartChatContextAPI(r *ghttp.Request) {
	_back := vant2.InitBack(r)
	userInput := r.Get("user_input", "我想查询订单状态").String()
	result := _processSimpleChat(userInput, nil)
	_back.ApiSuccess("智能对话处理完成", result)
}

// _processSimpleChat 核心流程：分类 -> switch -> 处理
func _processSimpleChat(userInput string, streamCallback func(string, interface{})) g.Map {
	startTime := time.Now()

	// === 第一步：问题分类器（API模式） ===
	if streamCallback != nil {
		streamCallback("step1_classifying", g.Map{"stage": "正在分析问题类型..."})
	}

	category := _classifyUserInput(userInput)

	// === 第二步：根据分类switch处理 ===
	if streamCallback != nil {
		streamCallback("step2_processing", g.Map{"stage": "正在处理...", "category": category})
	}

	var response g.Map
	switch category {
	case "order_query":
		response = _handleOrderQuery(userInput, streamCallback)
	case "product_inquiry":
		response = _handleProductInquiry(userInput, streamCallback)
	case "websearch":
		response = _handleWebSearch(userInput, streamCallback)
	case "doc":
		response = _handleDocQuery(userInput, streamCallback)
	case "qa":
		response = _handleQASystem(userInput, streamCallback)
	case "math":
		response = _handleMathCalculation(userInput, streamCallback)
	case "weather":
		response = _handleWeatherQuery(userInput, streamCallback)
	default: // chat
		response = _handleNormalChat(userInput, streamCallback)
	}

	response["category"] = category
	response["process_time"] = time.Since(startTime).Milliseconds()
	return response
}

// _classifyUserInput 问题分类器：通过简单system提示词实现
func _classifyUserInput(userInput string) string {
	// 构建分类提示词
	systemPrompt := vant2.GetLocalFile("prompt/system/category.md")

	// 分类器LLM
	classifier := openai.New().
		System(systemPrompt).
		FormatJson()

	prompt := fmt.Sprintf(`当前输入对话： """%s"""
当前历史对话内容： 无
返回JSON：`, userInput)

	result := classifier.Chat(prompt)

	if result.HasError() {
		return "chat" // 默认分类
	}

	// 解析JSON结果
	content := result.GetContent()
	if jsonData := vant2.JsonDecoder(content); jsonData != nil {
		if jsonMap := gconv.Map(jsonData); jsonMap != nil {
			if action := gconv.String(jsonMap["action"]); action != "" {
				return action
			}
		}
	}

	return "chat" // 备选
}

// _handleOrderQuery 处理订单查询
func _handleOrderQuery(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("order_processing", g.Map{"stage": "正在查询订单..."})
	}

	// 模拟订单查询
	orderInfo := _mockOrderQuery(userInput)

	// 生成回答
	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.2).
		System("你是订单查询助手。根据订单信息友好回答用户。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	prompt := fmt.Sprintf("用户询问: %s\n订单信息: %s\n请友好回答", userInput, vant2.JsonEncoder(orderInfo))
	result := llm.Chat(prompt)

	return g.Map{
		"content":    result.GetContent(),
		"order_info": orderInfo,
		"action":     "订单查询",
	}
}

// _handleProductInquiry 处理产品咨询
func _handleProductInquiry(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("product_processing", g.Map{"stage": "正在分析产品需求..."})
	}

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.7).
		System("你是产品顾问。为用户提供专业的产品建议和推荐。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	result := llm.Chat(userInput)

	return g.Map{
		"content": result.GetContent(),
		"action":  "产品咨询",
	}
}

// _handleNormalChat 处理普通对话
func _handleNormalChat(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("chat_processing", g.Map{"stage": "正在生成回答..."})
	}

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.8).
		System("你是友好的AI助手。自然地回答用户问题。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	result := llm.Chat(userInput)

	return g.Map{
		"content": result.GetContent(),
		"action":  "普通对话",
	}
}

// _handleWebSearch 处理网络搜索
func _handleWebSearch(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("websearch_processing", g.Map{"stage": "正在搜索网络信息..."})
	}

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.6).
		System("你是网络搜索助手。为用户提供搜索建议和解答，可以模拟搜索结果。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	result := llm.Chat(userInput)

	return g.Map{
		"content": result.GetContent(),
		"action":  "网络搜索",
	}
}

// _handleDocQuery 处理文档查询
func _handleDocQuery(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("doc_processing", g.Map{"stage": "正在查询文档..."})
	}

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.3).
		System("你是文档查询助手。帮助用户查找和解释文档内容。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	result := llm.Chat(userInput)

	return g.Map{
		"content": result.GetContent(),
		"action":  "文档查询",
	}
}

// _handleQASystem 处理问答系统
func _handleQASystem(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("qa_processing", g.Map{"stage": "正在分析问题..."})
	}

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.4).
		System("你是专业问答助手。为用户提供准确详细的专业解答。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	result := llm.Chat(userInput)

	return g.Map{
		"content": result.GetContent(),
		"action":  "专业问答",
	}
}

// _handleMathCalculation 处理数学计算
func _handleMathCalculation(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("math_processing", g.Map{"stage": "正在计算..."})
	}

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.1).
		System("你是数学计算助手。为用户进行准确的数学运算和解题。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	result := llm.Chat(userInput)

	return g.Map{
		"content": result.GetContent(),
		"action":  "数学计算",
	}
}

// _handleWeatherQuery 处理天气查询
func _handleWeatherQuery(userInput string, streamCallback func(string, interface{})) g.Map {
	if streamCallback != nil {
		streamCallback("weather_processing", g.Map{"stage": "正在查询天气..."})
	}

	// 模拟天气数据
	weatherInfo := _mockWeatherQuery(userInput)

	llm := openai.New().
		Model("Qwen/Qwen2.5-7B-Instruct").
		Temperature(0.3).
		System("你是天气查询助手。根据天气信息为用户提供友好的天气解答。")

	if streamCallback != nil {
		llm.Stream(func(result *openai.SSEResult) {
			if content := result.Content(); content != "" {
				streamCallback("response_streaming", g.Map{"content": content})
			}
		})
	}

	prompt := fmt.Sprintf("用户询问: %s\n天气信息: %s\n请友好回答", userInput, vant2.JsonEncoder(weatherInfo))
	result := llm.Chat(prompt)

	return g.Map{
		"content":      result.GetContent(),
		"weather_info": weatherInfo,
		"action":       "天气查询",
	}
}

// _mockWeatherQuery 模拟天气查询API
func _mockWeatherQuery(userInput string) g.Map {
	// 简单提取城市名
	city := "北京"
	if strings.Contains(userInput, "上海") {
		city = "上海"
	} else if strings.Contains(userInput, "广州") {
		city = "广州"
	} else if strings.Contains(userInput, "深圳") {
		city = "深圳"
	}

	return g.Map{
		"city":        city,
		"temperature": "15°C",
		"weather":     "晴转多云",
		"humidity":    "65%",
		"wind":        "东风2级",
		"date":        time.Now().Format("2006-01-02"),
	}
}

// _mockOrderQuery 模拟订单查询API
func _mockOrderQuery(userInput string) g.Map {
	// 简单提取订单号
	orderID := "ORD20250123001"
	words := strings.Fields(userInput)
	for _, word := range words {
		if len(word) > 6 && (strings.Contains(strings.ToLower(word), "ord") || strings.Contains(word, "123")) {
			orderID = word
			break
		}
	}

	return g.Map{
		"order_id":     orderID,
		"status":       "已发货",
		"tracking_no":  "SF12345678901",
		"total_amount": 399.00,
		"items": []g.Map{
			{"product_name": "无线蓝牙耳机Pro", "quantity": 1},
		},
	}
}
