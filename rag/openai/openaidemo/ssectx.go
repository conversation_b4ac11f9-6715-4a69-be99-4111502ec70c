package openaidemo

import (
	"assistant/rag/openai"
	"fmt"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// TestRealToolsWorkflow SSE版本
func TestRealToolsWorkflow(r *ghttp.Request) {
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")

	// SSE回调
	streamFunc := func(sseResult *openai.SSEResult) {
		data := g.Map{
			"id":      fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()),
			"object":  "chat.completion.chunk",
			"created": time.Now().Unix(),
			"model":   "gpt-3.5-turbo",
			"choices": []g.Map{{"index": 0, "delta": g.Map{}, "finish_reason": nil}},
		}
		if content := sseResult.Content(); content != "" {
			data["choices"].([]g.Map)[0]["delta"].(g.Map)["content"] = content
		}
		if sseResult.IsComplete() {
			data["choices"].([]g.Map)[0]["finish_reason"] = "stop"
		}
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(data) + "\n\n"))
		r.Response.Flush()
	}

	_executeWorkflow(streamFunc, func(data g.Map) {
		r.Response.Write([]byte("data: " + vant2.JsonEncoder(data) + "\n\n"))
		r.Response.Flush()
	})

	r.Response.Write([]byte("data: [DONE]\n\n"))
	r.Response.Flush()
}

// TestRealToolsWorkflowAPI API版本
func TestRealToolsWorkflowAPI(r *ghttp.Request) {
	_back := vant2.InitBack(r)
	result := _executeWorkflow(nil, nil)
	_back.ApiSuccess("工具调用完成", result)
}

// _executeWorkflow 核心流程（50行搞定）
func _executeWorkflow(streamFunc func(*openai.SSEResult), sseOutput func(g.Map)) g.Map {
	tools := []g.Map{
		{"type": "function", "function": g.Map{"name": "getOrder", "description": "查询订单", "parameters": g.Map{"type": "object", "properties": g.Map{"order_id": g.Map{"type": "string"}}, "required": []string{"order_id"}}}},
		{"type": "function", "function": g.Map{"name": "getWeather", "description": "查询天气", "parameters": g.Map{"type": "object", "properties": g.Map{"city": g.Map{"type": "string"}}, "required": []string{"city"}}}},
	}

	// 第一次对话
	llm := openai.New().Tools(tools, "auto").System("你是智能助手，可以查询订单和天气")
	if streamFunc != nil {
		llm.Stream(streamFunc)
	}
	result := llm.Chat("帮我查询订单123456，顺便看看北京天气")

	if result.HasError() {
		return g.Map{"error": result.GetError()}
	}

	// 检查工具调用
	detectedTools := result.GetTools()
	if len(detectedTools) == 0 {
		return g.Map{"id": fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()), "object": "chat.completion", "choices": []g.Map{{"message": g.Map{"role": "assistant", "content": result.GetContent()}, "finish_reason": "stop"}}}
	}

	// 执行工具
	toolResults := ""
	for _, tool := range detectedTools {
		toolName, toolArgs := _extractToolInfo(tool)
		if toolName != "" {
			toolResult := _executeTool(toolName, toolArgs)
			toolResults += fmt.Sprintf("工具%s执行结果: %s\n", toolName, vant2.JsonEncoder(toolResult))
		}
	}

	// 继续对话
	llm2 := openai.New().System("根据工具结果为用户提供友好回答")
	if streamFunc != nil {
		llm2.Stream(streamFunc)
	}
	finalResult := llm2.Chat("工具执行结果：\n" + toolResults + "\n请根据以上结果友好回答用户")

	if finalResult.HasError() {
		return g.Map{"error": finalResult.GetError()}
	}

	return g.Map{"id": fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()), "object": "chat.completion", "choices": []g.Map{{"message": g.Map{"role": "assistant", "content": finalResult.GetContent()}, "finish_reason": "stop"}}}
}

// _extractToolInfo 提取工具信息
func _extractToolInfo(tool g.Map) (string, g.Map) {
	if functionData := tool["function"]; functionData != nil {
		if functionMap := gconv.Map(functionData); functionMap != nil {
			toolName := gconv.String(functionMap["name"])
			var toolArgs g.Map
			if argsStr := gconv.String(functionMap["arguments"]); argsStr != "" {
				if argsResult := vant2.JsonDecoder(argsStr); argsResult != nil {
					toolArgs = gconv.Map(argsResult)
				}
			}
			return toolName, toolArgs
		}
	}
	return gconv.String(tool["name"]), tool
}

// _executeTool 执行工具
func _executeTool(toolName string, toolArgs g.Map) g.Map {
	switch toolName {
	case "getOrder":
		orderId := gconv.String(toolArgs["order_id"])
		if orderId == "" {
			orderId = "123456"
		}
		return _executeGetOrder(orderId)
	case "getWeather":
		city := gconv.String(toolArgs["city"])
		if city == "" {
			city = "北京"
		}
		return _executeGetWeather(city)
	default:
		return g.Map{"error": "未知工具: " + toolName}
	}
}

// _executeGetOrder 模拟订单查询API
func _executeGetOrder(orderId string) g.Map {
	time.Sleep(300 * time.Millisecond)
	return g.Map{"order_id": orderId, "status": "已发货", "total_amount": 299.00, "items": []g.Map{{"product_name": "无线蓝牙耳机", "quantity": 1}}}
}

// _executeGetWeather 模拟天气查询API
func _executeGetWeather(city string) g.Map {
	time.Sleep(200 * time.Millisecond)
	return g.Map{"city": city, "temperature": "15°C", "weather": "晴转多云", "humidity": "65%"}
}
