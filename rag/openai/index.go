package openai

import (
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// ExtractTextFromMultimodalContent 从多模态内容中提取文本
func ExtractTextFromMultimodalContent(content interface{}) string {
	if content == nil {
		return ""
	}

	// 如果是字符串，直接返回
	if str, ok := content.(string); ok {
		return str
	}

	// 如果是数组（多模态格式）
	if arr, ok := content.([]interface{}); ok {
		var textParts []string
		for _, part := range arr {
			if partMap := gconv.Map(part); partMap != nil {
				if partType := gconv.String(partMap["type"]); partType == "text" {
					if text := gconv.String(partMap["text"]); text != "" {
						textParts = append(textParts, text)
					}
				}
			}
		}
		if len(textParts) > 0 {
			return strings.Join(textParts, " ")
		}
	}

	// 其他情况转换为字符串
	return gconv.String(content)
}

// Chat 执行对话（支持多模态内容）
func (l *LLM) Chat(content ...interface{}) *ToolResult {
	// 创建结果并记录请求开始时间
	result := &ToolResult{}
	result.TokenUsage.SetRequestStartTime()

	// 构建最终的消息列表
	finalMessages := make([]Message, 0)

	// 添加系统消息
	finalMessages = append(finalMessages, l.systemMessages...)

	// 添加历史对话消息
	finalMessages = append(finalMessages, l.messages...)

	// 添加当前用户消息（支持多模态）
	if len(content) > 0 {
		_content := content[0]
		finalMessages = append(finalMessages, Message{
			Role:    "user",
			Content: _content,
		})
	}

	// 验证消息列表不为空
	if len(finalMessages) == 0 {
		result.Error = "消息列表为空，无法进行对话"
		return result
	}

	// 构建请求参数
	params := l._buildParams(finalMessages)

	// 打印整体提交的包，方便调试
	vant2.ErrorMap(params)

	// 执行API调用并接收结果
	var finalResult *ToolResult
	if l.streamFunc != nil {
		finalResult = l._callStreamAPI(params, result)
	} else {
		finalResult = l._callAPI(params, result)
	}

	// 打印执行结果
	vant2.SuccessMap(g.Map{
		"content_length":  len(finalResult.GetContent()),
		"content_preview": finalResult.GetContent(),
		"tools_count":     len(finalResult.GetTools()),
		"has_error":       finalResult.HasError(),
		"error":           finalResult.GetError(),
		"token_usage":     finalResult.GetTokenUsage(),
		"performance":     (&finalResult.TokenUsage).GetPerformanceReport(),
	})

	// 特别打印tools信息
	if len(finalResult.GetTools()) > 0 {
		vant2.Warning(finalResult.GetTools(), "=== Chat 检测到工具调用 ===")
	}

	return finalResult
}

// _buildParams 构建API请求参数
func (l *LLM) _buildParams(messages []Message) g.Map {
	params := g.Map{
		"model":    l.model,
		"messages": messages,
	}

	// 设置温度参数
	if l.temperature == 0 {
		params["temperature"] = 0
	} else if l.temperature > 0 {
		params["temperature"] = l.temperature
	}

	// 设置最大token
	if l.maxTokens > 0 {
		params["max_tokens"] = l.maxTokens
	}

	// 设置JSON返回格式
	if l.returnJson {
		params["response_format"] = g.Map{"type": "json_object"}
	}

	// 设置停止词
	if len(l.stop) > 0 {
		params["stop"] = l.stop
	}

	// 设置思维链
	if l.enableThinking {
		params["enable_thinking"] = true
	}

	// 设置工具
	if len(l.tools) > 0 {
		params["tools"] = l.tools
		params["tool_choice"] = l.toolChoice
	}

	if l.model == "bot-20250217112501-pffww" { // 应用的机器人搜索
		params["stream_options"] = w.Map{"include_usage": true}
	}

	// 设置流式输出
	params["stream"] = (l.streamFunc != nil)

	return params
}

// _callAPI 调用普通API
func (l *LLM) _callAPI(params g.Map, result *ToolResult) *ToolResult {

	// 记录API调用开始时间
	result.TokenUsage.SetApiCallStartTime()

	// 调用真实API
	response := _callLLMAPI(l, params, false, &result.TokenUsage)

	if response == nil {
		result.Error = "API调用失败，返回空结果"
		result.RawResponse = g.Map{"error": "response is nil"}
		return result
	}

	// 转换为g.Map类型
	responseMap := gconv.Map(response)

	// 详细错误检查和处理
	if errorMsg := gconv.String(responseMap["error"]); errorMsg != "" {
		result.Error = errorMsg
		result.RawResponse = responseMap

		// 补充错误类型和详细信息
		if errorType := gconv.String(responseMap["error_type"]); errorType != "" {
			result.Error = errorMsg + " (类型: " + errorType + ")"
		}

		if errorCode := gconv.String(responseMap["error_code"]); errorCode != "" {
			result.Error = result.Error + " (错误码: " + errorCode + ")"
		}

		if apiErrorType := gconv.String(responseMap["api_error_type"]); apiErrorType != "" {
			result.Error = result.Error + " (API错误类型: " + apiErrorType + ")"
		}

		// 打印详细错误信息用于调试
		vant2.Error(g.Map{
			"error_message":  errorMsg,
			"error_type":     gconv.String(responseMap["error_type"]),
			"error_code":     gconv.String(responseMap["error_code"]),
			"api_error_type": gconv.String(responseMap["api_error_type"]),
			"raw_response":   responseMap["raw_response"],
		}, "=== Chat API 详细错误信息 ===")

		return result
	}

	// 解析响应
	parsedResult := l._parseResponse(responseMap, result)

	// 记录响应完成时间并计算各种用时
	parsedResult.TokenUsage.SetResponseCompleteTime()

	return parsedResult
}

// _callStreamAPI 调用流式API
func (l *LLM) _callStreamAPI(params g.Map, result *ToolResult) *ToolResult {

	// 记录API调用开始时间
	result.TokenUsage.SetApiCallStartTime()

	// 调用流式API，收集完整结果
	response := _callLLMAPI(l, params, true, &result.TokenUsage)

	if response == nil {
		result.Error = "流式API调用失败，返回空结果"
		result.RawResponse = g.Map{"error": "stream response is nil"}
		return result
	}

	// 转换为g.Map类型
	responseMap := gconv.Map(response)

	// 详细错误检查和处理
	if errorMsg := gconv.String(responseMap["error"]); errorMsg != "" {
		result.Error = errorMsg
		result.RawResponse = responseMap

		// 补充错误类型和详细信息
		if errorType := gconv.String(responseMap["error_type"]); errorType != "" {
			result.Error = errorMsg + " (类型: " + errorType + ")"
		}

		if errorCode := gconv.String(responseMap["error_code"]); errorCode != "" {
			result.Error = result.Error + " (错误码: " + errorCode + ")"
		}

		// 打印详细错误信息用于调试
		vant2.Error(g.Map{
			"error_message": errorMsg,
			"error_type":    gconv.String(responseMap["error_type"]),
			"error_code":    gconv.String(responseMap["error_code"]),
			"raw_response":  responseMap["raw_response"],
		}, "=== Stream API 详细错误信息 ===")

		return result
	}

	// 从responseMap中获取完整的流式结果
	if streamResult := responseMap["stream_result"]; streamResult != nil {
		if sseResult := streamResult.(*SSEResult); sseResult != nil {
			// 从SSEResult收集完整数据到ToolResult
			result.Content = sseResult.ContentTotal()
			result.Tools = sseResult.GetTools()
			result.TokenUsage = sseResult.GetTokenUsage()
			result.RawResponse = g.Map{
				"stream_complete": true,
				"final_content":   sseResult.ContentTotal(),
				"tools_count":     len(sseResult.GetTools()),
			}

			// 记录响应完成时间
			result.TokenUsage.SetResponseCompleteTime()

			// 尝试从内容中提取工具调用
			if len(result.Tools) == 0 && result.Content != "" {
				result._extractToolsFromContent()
			}
		}
	}

	return result
}

// _parseResponse 解析API响应
func (l *LLM) _parseResponse(response g.Map, result *ToolResult) *ToolResult {
	result.RawResponse = response

	// 再次检查是否有错误（防止遗漏）
	if errorField := response["error"]; errorField != nil {
		if errorStr := gconv.String(errorField); errorStr != "" {
			result.Error = "API响应包含错误: " + errorStr
			return result
		}
		if errorMap := gconv.Map(errorField); errorMap != nil {
			if errorMsg := gconv.String(errorMap["message"]); errorMsg != "" {
				result.Error = "API错误: " + errorMsg
				if errorType := gconv.String(errorMap["type"]); errorType != "" {
					result.Error = result.Error + " (类型: " + errorType + ")"
				}
				return result
			}
		}
	}

	// 提取对话内容（支持多模态）
	if choices := gconv.SliceMap(response["choices"]); len(choices) > 0 {
		if message := gconv.Map(choices[0]["message"]); message != nil {
			// 保持多模态内容的原始格式，但ToolResult.Content仍为string用于兼容
			if content := message["content"]; content != nil {
				// 如果是字符串，直接使用
				if str, ok := content.(string); ok {
					result.Content = str
				} else {
					// 如果是多模态内容，提取文本部分
					result.Content = ExtractTextFromMultimodalContent(content)
				}
			}

			// 提取工具调用
			if toolCalls := message["tool_calls"]; toolCalls != nil {
				result.Tools = gconv.SliceMap(toolCalls)
			}
		}
	} else {
		// 如果没有choices字段，这可能是一个错误
		vant2.Error(response, "=== API 响应缺少choices字段 ===")
		result.Error = "API响应格式错误：缺少choices字段或choices为空"
		return result
	}

	// 提取token使用情况
	if usage := gconv.Map(response["usage"]); usage != nil {
		result.TokenUsage.UpdateTokenCount(
			gconv.Int64(usage["prompt_tokens"]),
			gconv.Int64(usage["completion_tokens"]),
			gconv.Int64(usage["total_tokens"]),
		)
	}

	// 尝试从内容中提取工具调用
	if len(result.Tools) == 0 && result.Content != "" {
		result._extractToolsFromContent()
	}

	// 如果既没有内容也没有工具调用，可能是个问题
	if result.Content == "" && len(result.Tools) == 0 {
		vant2.Error(response, "=== API 响应无内容无工具调用 ===")
		result.Error = "API响应异常：无对话内容且无工具调用"
		return result
	}

	return result
}
