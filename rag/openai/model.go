package openai

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// LLM 大模型对话工具链
type LLM struct {
	model          string                  // 模型名称
	maxTokens      int64                   // 最大token
	temperature    float32                 // 温度参数，默认0
	returnJson     bool                    // 是否返回JSON格式
	stop           []string                // 停止词
	enableThinking bool                    // 是否启用思维链，默认false
	systemMessages []Message               // 系统消息列表
	messages       []Message               // 对话消息列表
	tools          []g.Map                 // 工具列表
	toolChoice     string                  // 工具选择策略
	streamFunc     func(result *SSEResult) // 流式回调函数
	apiKey         string                  // API密钥
	baseURL        string                  // API基础URL
}

// Message 消息结构 - 支持多模态内容
type Message struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"` // 支持多模态：string 或 []map[string]interface{}
}

// ToolResult 工具结果结构，用于安全提取各种信息
type ToolResult struct {
	Content     string     `json:"content"`      // 对话结果
	Tools       []g.Map    `json:"tools"`        // 工具调用结果
	TokenUsage  TokenUsage `json:"token_usage"`  // token消耗信息
	RawResponse g.Map      `json:"raw_response"` // 原始响应
	Error       string     `json:"error"`        // 错误信息
}

// TokenUsage token使用情况和性能统计
type TokenUsage struct {
	PromptTokens     int64 `json:"prompt_tokens"`     // 提示token数
	CompletionTokens int64 `json:"completion_tokens"` // 完成token数
	TotalTokens      int64 `json:"total_tokens"`      // 总token数

	// 时间统计（精确到毫秒）
	RequestStartTime     int64 `json:"request_start_time"`     // 接受用户请求时间（毫秒时间戳）
	ApiCallStartTime     int64 `json:"api_call_start_time"`    // 开始调用大模型时间（毫秒时间戳）
	ResponseCompleteTime int64 `json:"response_complete_time"` // 完成对话时间（毫秒时间戳）

	// 计算得出的用时（毫秒）
	TotalDuration   int64 `json:"total_duration"`    // 总用时（请求到完成）
	ApiCallDuration int64 `json:"api_call_duration"` // API调用用时（调用到完成）

	// 性能指标
	TPS float64 `json:"tps"` // 每秒token数（CompletionTokens/秒）
}

// MarshalJSON 自定义JSON序列化，格式化时间和TPS
func (tu *TokenUsage) MarshalJSON() ([]byte, error) {
	formatted := g.Map{
		"prompt_tokens":          tu.PromptTokens,
		"completion_tokens":      tu.CompletionTokens,
		"total_tokens":           tu.TotalTokens,
		"request_start_time":     tu._formatTime(tu.RequestStartTime),
		"api_call_start_time":    tu._formatTime(tu.ApiCallStartTime),
		"response_complete_time": tu._formatTime(tu.ResponseCompleteTime),
		"total_duration":         tu.TotalDuration,
		"api_call_duration":      tu.ApiCallDuration,
		"tps":                    fmt.Sprintf("%.2f", tu.TPS),
	}

	jsonStr := vant2.JsonEncoder(formatted)
	return []byte(jsonStr), nil
}

// FormatForDisplay 获取格式化显示版本的TokenUsage
func (tu *TokenUsage) FormatForDisplay() g.Map {
	return g.Map{
		"prompt_tokens":          tu.PromptTokens,
		"completion_tokens":      tu.CompletionTokens,
		"total_tokens":           tu.TotalTokens,
		"request_start_time":     tu._formatTime(tu.RequestStartTime),
		"api_call_start_time":    tu._formatTime(tu.ApiCallStartTime),
		"response_complete_time": tu._formatTime(tu.ResponseCompleteTime),
		"total_duration":         tu.TotalDuration,
		"api_call_duration":      tu.ApiCallDuration,
		"tps":                    fmt.Sprintf("%.2f", tu.TPS),
	}
}

// SetRequestStartTime 设置请求开始时间
func (tu *TokenUsage) SetRequestStartTime() {
	tu.RequestStartTime = time.Now().UnixMilli()
}

// SetApiCallStartTime 设置API调用开始时间
func (tu *TokenUsage) SetApiCallStartTime() {
	tu.ApiCallStartTime = time.Now().UnixMilli()
}

// SetResponseCompleteTime 设置响应完成时间并计算各种用时
func (tu *TokenUsage) SetResponseCompleteTime() {
	tu.ResponseCompleteTime = time.Now().UnixMilli()

	// 计算总用时
	if tu.RequestStartTime > 0 {
		tu.TotalDuration = tu.ResponseCompleteTime - tu.RequestStartTime
	}

	// 计算API调用用时
	if tu.ApiCallStartTime > 0 {
		tu.ApiCallDuration = tu.ResponseCompleteTime - tu.ApiCallStartTime
	}

	// 计算TPS（每秒token数）
	if tu.ApiCallDuration > 0 && tu.CompletionTokens > 0 {
		seconds := float64(tu.ApiCallDuration) / 1000.0
		tu.TPS = float64(tu.CompletionTokens) / seconds
	}
}

// UpdateTokenCount 更新token计数（保留原有时间信息）
func (tu *TokenUsage) UpdateTokenCount(promptTokens, completionTokens, totalTokens int64) {
	tu.PromptTokens = promptTokens
	tu.CompletionTokens = completionTokens
	tu.TotalTokens = totalTokens

	// 如果已有完成时间，重新计算TPS
	if tu.ResponseCompleteTime > 0 && tu.ApiCallStartTime > 0 && tu.ApiCallDuration > 0 && tu.CompletionTokens > 0 {
		seconds := float64(tu.ApiCallDuration) / 1000.0
		tu.TPS = float64(tu.CompletionTokens) / seconds
	}
}

// GetPerformanceReport 获取性能报告
func (tu *TokenUsage) GetPerformanceReport() g.Map {
	return g.Map{
		"token_统计": g.Map{
			"提示tokens": tu.PromptTokens,
			"完成tokens": tu.CompletionTokens,
			"总tokens":  tu.TotalTokens,
		},
		"时间统计": g.Map{
			"请求开始时间":    tu._formatTime(tu.RequestStartTime),
			"API调用开始时间": tu._formatTime(tu.ApiCallStartTime),
			"响应完成时间":    tu._formatTime(tu.ResponseCompleteTime),
		},
		"用时统计": g.Map{
			"总用时毫秒":     tu.TotalDuration,
			"API调用用时毫秒": tu.ApiCallDuration,
			"总用时秒":      fmt.Sprintf("%.3f", float64(tu.TotalDuration)/1000.0),
			"API调用用时秒":  fmt.Sprintf("%.3f", float64(tu.ApiCallDuration)/1000.0),
		},
		"性能指标": g.Map{
			"TPS每秒tokens": fmt.Sprintf("%.2f", tu.TPS),
			"单token耗时毫秒": func() string {
				if tu.CompletionTokens > 0 && tu.ApiCallDuration > 0 {
					return fmt.Sprintf("%.2f", float64(tu.ApiCallDuration)/float64(tu.CompletionTokens))
				}
				return "0.00"
			}(),
		},
	}
}

// _formatTime 格式化时间为易读格式
func (tu *TokenUsage) _formatTime(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.UnixMilli(timestamp).Format("2006-01-02 15:04:05.000")
}

// LLMConfig LLM配置结构
type LLMConfig struct {
	BaseURL      string `json:"base_url"`      // API基础URL
	ApiKey       string `json:"api_key"`       // API密钥
	DefaultModel string `json:"default_model"` // 默认模型
}

// SSEResult SSE流式结果结构，用于流式回调
type SSEResult struct {
	rawData        g.Map           // 原始报文
	currentContent string          // 当前片段内容
	totalContent   strings.Builder // 累积的总内容
	tokenUsage     TokenUsage      // token使用情况（包含时间统计）
	tools          []g.Map         // 工具调用结果（包括原生和解析的）
	isComplete     bool            // 是否完成
}

// Raw 获取原始报文
func (sr *SSEResult) Raw() g.Map {
	return sr.rawData
}

// Content 获取当前片段内容
func (sr *SSEResult) Content() string {
	return sr.currentContent
}

// ContentTotal 获取累积的总内容
func (sr *SSEResult) ContentTotal() string {
	return sr.totalContent.String()
}

// GetTokenUsage 获取token使用情况
func (sr *SSEResult) GetTokenUsage() TokenUsage {
	return sr.tokenUsage
}

// GetTools 获取所有工具调用（包括原生tools和JSON解析的）
func (sr *SSEResult) GetTools() []g.Map {
	if sr.tools == nil {
		return make([]g.Map, 0)
	}
	return sr.tools
}

// IsComplete 判断是否完成
func (sr *SSEResult) IsComplete() bool {
	return sr.isComplete
}

// _updateFromSSEData 从SSE数据更新结果
func (sr *SSEResult) _updateFromSSEData(data g.Map) {
	sr.rawData = data

	// 检查是否是完成信号
	if dataField := data["data"]; dataField != nil {
		if dataStr := gconv.String(dataField); dataStr == "[DONE]" {
			sr.isComplete = true
			// 记录响应完成时间并计算各种用时
			sr.tokenUsage.SetResponseCompleteTime()
			// 最后解析总内容中的工具调用
			sr._extractToolsFromTotalContent()
			return
		}
	}

	// 更新当前内容
	sr.currentContent = GetContentFromSSE(data)
	if sr.currentContent != "" {
		sr.totalContent.WriteString(sr.currentContent)
	}

	// 更新token使用情况
	newTokenUsage := GetTokenUsageFromSSE(data)
	if newTokenUsage.TotalTokens > 0 {
		sr.tokenUsage.UpdateTokenCount(newTokenUsage.PromptTokens, newTokenUsage.CompletionTokens, newTokenUsage.TotalTokens)
	}

	// 检查原生工具调用
	sr._checkNativeTools(data)

	// 每次更新后都尝试从总内容中提取工具调用
	sr._extractToolsFromTotalContent()
}

// _checkNativeTools 检查原生工具调用
func (sr *SSEResult) _checkNativeTools(data g.Map) {
	// 检查choices.0.delta.tool_calls
	if choices := gconv.SliceMap(data["choices"]); len(choices) > 0 {
		if delta := gconv.Map(choices[0]["delta"]); delta != nil {
			if toolCalls := delta["tool_calls"]; toolCalls != nil {
				if toolCallsSlice := gconv.SliceMap(toolCalls); len(toolCallsSlice) > 0 {
					// 正确处理tool_calls，避免重复添加碎片
					for _, toolCall := range toolCallsSlice {
						if toolCallMap := gconv.Map(toolCall); toolCallMap != nil {
							// 检查是否已存在相同的tool_call
							exists := false
							toolCallId := gconv.String(toolCallMap["id"])
							for _, existingTool := range sr.tools {
								if gconv.String(existingTool["id"]) == toolCallId {
									exists = true
									break
								}
							}
							if !exists && toolCallId != "" {
								sr.tools = append(sr.tools, toolCallMap)
							}
						}
					}
				}
			}
		}
	}

	// 检查choices.0.message.tool_calls（完整格式）
	if choices := gconv.SliceMap(data["choices"]); len(choices) > 0 {
		if message := gconv.Map(choices[0]["message"]); message != nil {
			if toolCalls := message["tool_calls"]; toolCalls != nil {
				if toolCallsSlice := gconv.SliceMap(toolCalls); len(toolCallsSlice) > 0 {
					for _, toolCall := range toolCallsSlice {
						if toolCallMap := gconv.Map(toolCall); toolCallMap != nil {
							// 检查是否已存在
							exists := false
							toolCallId := gconv.String(toolCallMap["id"])
							for _, existingTool := range sr.tools {
								if gconv.String(existingTool["id"]) == toolCallId {
									exists = true
									break
								}
							}
							if !exists && toolCallId != "" {
								sr.tools = append(sr.tools, toolCallMap)
							}
						}
					}
				}
			}
		}
	}
}

// _extractToolsFromTotalContent 从累积内容中提取工具调用
func (sr *SSEResult) _extractToolsFromTotalContent() {
	totalContent := sr.totalContent.String()
	if totalContent == "" {
		return
	}

	// 使用类似ToolResult的逻辑提取JSON
	var _function interface{}

	if strings.HasPrefix(totalContent, "{") && strings.HasSuffix(totalContent, "}") {
		_function = vant2.JsonDecoder(totalContent)
	} else if totalContent != "" && strings.Contains(totalContent, "{") && strings.Contains(totalContent, "}") {
		re := regexp.MustCompile(`(?s)\{.*?\}`)
		_function = vant2.JsonDecoder(re.FindString(totalContent))
		if _function == nil {
			startIndex := strings.Index(totalContent, "{")
			endIndex := strings.LastIndex(totalContent, "}")
			if startIndex != -1 && endIndex != -1 && startIndex < endIndex {
				jsonContent := totalContent[startIndex : endIndex+1]
				_function = vant2.JsonDecoder(jsonContent)
			}
		}
	}

	if _function != nil {
		if toolMap := gconv.Map(_function); toolMap != nil {
			// 检查是否已存在相同的工具调用，避免重复
			exists := false
			for _, existingTool := range sr.tools {
				if _toolsEqual(existingTool, toolMap) {
					exists = true
					break
				}
			}
			if !exists {
				sr.tools = append(sr.tools, toolMap)
			}
		}
	}
}

// _toolsEqual 比较两个工具调用是否相同
func _toolsEqual(tool1, tool2 g.Map) bool {
	// 简单比较，可以根据需要增强
	json1 := vant2.JsonEncoder(tool1)
	json2 := vant2.JsonEncoder(tool2)
	return json1 == json2
}

// GetContentFromSSE 从SSE数据中安全提取对话内容（多模态兼容）
func GetContentFromSSE(sseData g.Map) string {
	// 检查是否是MiniMax模型的最后一次输出（避免重复显示）
	if isMiniMaxFinalOutput(sseData) {
		return ""
	}

	// 获取原始多模态内容
	multimodalContent := GetMultimodalContentFromSSE(sseData)
	if multimodalContent == nil {
		return ""
	}

	// 如果是字符串，直接返回
	if str, ok := multimodalContent.(string); ok {
		return str
	}

	// 如果是多模态数组，提取文本部分
	if arr, ok := multimodalContent.([]interface{}); ok {
		var textParts []string
		for _, part := range arr {
			if partMap := gconv.Map(part); partMap != nil {
				if partType := gconv.String(partMap["type"]); partType == "text" {
					if text := gconv.String(partMap["text"]); text != "" {
						textParts = append(textParts, text)
					}
				}
			}
		}
		return strings.Join(textParts, " ")
	}

	// 其他情况转换为字符串
	return gconv.String(multimodalContent)
}

// isMiniMaxFinalOutput 检测是否是MiniMax模型的最后一次输出
func isMiniMaxFinalOutput(sseData g.Map) bool {
	// 检查模型名称是否以minimax开头（不区分大小写）
	model := gconv.String(sseData["model"])
	if model == "" {
		return false
	}

	modelLower := strings.ToLower(model)
	if !strings.HasPrefix(modelLower, "minimax") {
		return false
	}

	// 检查是否有usage信息且值大于0（表示最后一次输出）
	if usage := gconv.Map(sseData["usage"]); usage != nil {
		totalTokens := gconv.Int64(usage["total_tokens"])
		completionTokens := gconv.Int64(usage["completion_tokens"])
		promptTokens := gconv.Int64(usage["prompt_tokens"])

		// 只要有任何一个token数量大于0，就认为是最后一次输出
		if totalTokens > 0 || completionTokens > 0 || promptTokens > 0 {
			return true
		}
	}

	return false
}

// GetMultimodalContentFromSSE 从SSE数据中获取原始多模态内容
func GetMultimodalContentFromSSE(sseData g.Map) interface{} {
	// 检查是否是完成信号
	if dataField := sseData["data"]; dataField != nil {
		if dataStr := gconv.String(dataField); dataStr == "[DONE]" {
			return nil
		}
	}

	// 尝试从choices.0.delta.content提取（流式格式）
	if choices := gconv.SliceMap(sseData["choices"]); len(choices) > 0 {
		if delta := gconv.Map(choices[0]["delta"]); delta != nil {
			if content := delta["content"]; content != nil {
				return content
			}
		}
	}

	// 尝试从choices.0.message.content提取（完整消息格式）
	if choices := gconv.SliceMap(sseData["choices"]); len(choices) > 0 {
		if message := gconv.Map(choices[0]["message"]); message != nil {
			if content := message["content"]; content != nil {
				return content
			}
		}
	}

	// 尝试直接从content字段提取
	if content := sseData["content"]; content != nil {
		return content
	}

	return nil
}

// GetTokenUsageFromSSE 从SSE数据中安全提取token使用情况
func GetTokenUsageFromSSE(sseData g.Map) TokenUsage {
	tokenUsage := TokenUsage{}

	// 检查是否是完成信号
	if dataField := sseData["data"]; dataField != nil {
		if dataStr := gconv.String(dataField); dataStr == "[DONE]" {
			return tokenUsage
		}
	}

	// 尝试从usage字段提取
	if usage := gconv.Map(sseData["usage"]); usage != nil {
		tokenUsage.PromptTokens = gconv.Int64(usage["prompt_tokens"])
		tokenUsage.CompletionTokens = gconv.Int64(usage["completion_tokens"])
		tokenUsage.TotalTokens = gconv.Int64(usage["total_tokens"])
		return tokenUsage
	}

	// 尝试从choices.0.usage提取（某些API可能在这里）
	if choices := gconv.SliceMap(sseData["choices"]); len(choices) > 0 {
		if usage := gconv.Map(choices[0]["usage"]); usage != nil {
			tokenUsage.PromptTokens = gconv.Int64(usage["prompt_tokens"])
			tokenUsage.CompletionTokens = gconv.Int64(usage["completion_tokens"])
			tokenUsage.TotalTokens = gconv.Int64(usage["total_tokens"])
			return tokenUsage
		}
	}

	// 尝试直接从根级别提取（兼容格式）
	if promptTokens := gconv.Int64(sseData["prompt_tokens"]); promptTokens > 0 {
		tokenUsage.PromptTokens = promptTokens
	}
	if completionTokens := gconv.Int64(sseData["completion_tokens"]); completionTokens > 0 {
		tokenUsage.CompletionTokens = completionTokens
	}
	if totalTokens := gconv.Int64(sseData["total_tokens"]); totalTokens > 0 {
		tokenUsage.TotalTokens = totalTokens
	}

	return tokenUsage
}

// GetSSEDataType 判断SSE数据的类型
func GetSSEDataType(sseData g.Map) string {
	// 检查是否是完成信号
	if dataField := sseData["data"]; dataField != nil {
		if dataStr := gconv.String(dataField); dataStr == "[DONE]" {
			return "done"
		}
	}

	// 检查是否是错误
	if errorField := sseData["error"]; errorField != nil {
		return "error"
	}

	// 检查是否有内容
	if GetContentFromSSE(sseData) != "" {
		return "content"
	}

	// 检查是否有token使用情况
	tokenUsage := GetTokenUsageFromSSE(sseData)
	if tokenUsage.TotalTokens > 0 {
		return "usage"
	}

	// 检查是否有工具调用
	if choices := gconv.SliceMap(sseData["choices"]); len(choices) > 0 {
		if delta := gconv.Map(choices[0]["delta"]); delta != nil {
			if toolCalls := delta["tool_calls"]; toolCalls != nil {
				return "tool_calls"
			}
		}
	}

	return "unknown"
}

// New 创建新的LLM实例
func New(config ...LLMConfig) *LLM {
	llm := &LLM{
		temperature:    0,
		enableThinking: false,
		systemMessages: make([]Message, 0),
		messages:       make([]Message, 0),
		tools:          make([]g.Map, 0),
		toolChoice:     "auto",
	}

	// 如果传入了配置对象，使用配置对象的值
	if len(config) > 0 {
		cfg := config[0]
		llm.baseURL = cfg.BaseURL
		llm.apiKey = cfg.ApiKey
		llm.model = cfg.DefaultModel
	} else {
		_cfg := LLMKey(DefaultModel)
		llm.model = _cfg.DefaultModel
		llm.baseURL = _cfg.BaseURL
		llm.apiKey = _cfg.ApiKey
	}

	return llm
}

// Model 设置模型名称
func (l *LLM) Model(model string) *LLM {
	l.model = model
	return l
}

// MaxTokens 设置最大token数
func (l *LLM) MaxTokens(tokens int64) *LLM {
	l.maxTokens = tokens
	return l
}

// Temperature 设置温度参数
func (l *LLM) Temperature(temp float32) *LLM {
	l.temperature = temp
	return l
}

// ReturnJson 设置返回JSON格式
func (l *LLM) ReturnJson() *LLM {
	l.returnJson = true
	return l
}

// Stop 设置停止词
func (l *LLM) Stop(stopWords ...string) *LLM {
	l.stop = stopWords
	return l
}

// EnableThinking 启用思维链
func (l *LLM) EnableThinking() *LLM {
	l.enableThinking = true
	return l
}

// System 添加系统消息
func (l *LLM) System(content string) *LLM {
	l.systemMessages = append(l.systemMessages, Message{
		Role:    "system",
		Content: content,
	})
	return l
}

// AddSystemTime 添加系统时间消息
func (l *LLM) AddSystemTime() *LLM {
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	return l.System("当前系统时间是： " + timeStr)
}

// Messages 设置对话消息列表
func (l *LLM) Messages(messages []g.Map) *LLM {
	l.messages = make([]Message, 0)
	for _, msg := range messages {
		l.messages = append(l.messages, Message{
			Role:    gconv.String(msg["role"]),
			Content: msg["content"],
		})
	}
	return l
}

// Tools 设置工具列表
func (l *LLM) Tools(tools []g.Map, choice ...string) *LLM {
	l.tools = tools
	if len(choice) > 0 {
		l.toolChoice = choice[0]
	}
	return l
}

// Stream 设置流式回调函数
func (l *LLM) Stream(callback func(result *SSEResult)) *LLM {
	if callback != nil {
		l.streamFunc = callback
	}
	return l
}

// ApiKey 设置API密钥
func (l *LLM) ApiKey(key string) *LLM {
	l.apiKey = key
	return l
}

// BaseURL 设置API基础URL
func (l *LLM) BaseURL(url string) *LLM {
	l.baseURL = url
	return l
}

// Reset 重置LLM实例，清除所有消息和配置
func (l *LLM) Reset() *LLM {
	l.systemMessages = make([]Message, 0)
	l.messages = make([]Message, 0)
	l.tools = make([]g.Map, 0)
	l.streamFunc = nil
	return l
}

// Clone 克隆LLM实例
func (l *LLM) Clone() *LLM {
	newLLM := &LLM{
		model:          l.model,
		maxTokens:      l.maxTokens,
		temperature:    l.temperature,
		returnJson:     l.returnJson,
		stop:           append([]string{}, l.stop...),
		enableThinking: l.enableThinking,
		systemMessages: append([]Message{}, l.systemMessages...),
		messages:       append([]Message{}, l.messages...),
		tools:          append([]g.Map{}, l.tools...),
		toolChoice:     l.toolChoice,
		apiKey:         l.apiKey,
		baseURL:        l.baseURL,
	}
	return newLLM
}

// GetModel 获取当前模型名称
func (l *LLM) GetModel() string {
	return l.model
}

// GetContent 安全获取对话结果
func (tr *ToolResult) GetContent() string {
	return tr.Content
}

// GetTools 安全获取工具调用结果
func (tr *ToolResult) GetTools() []g.Map {
	if tr.Tools == nil {
		return make([]g.Map, 0)
	}
	return tr.Tools
}

// GetTokenUsage 安全获取token消耗信息
func (tr *ToolResult) GetTokenUsage() TokenUsage {
	return tr.TokenUsage
}

// HasError 检查是否有错误
func (tr *ToolResult) HasError() bool {
	return tr.Error != ""
}

// GetError 获取错误信息
func (tr *ToolResult) GetError() string {
	return tr.Error
}

// FormatJson 强制返回JSON格式并自动解析为Tools对象
func (l *LLM) FormatJson() *LLM {
	l.returnJson = true
	return l
}

// _extractToolsFromContent 从内容中正则匹配工具调用（GetFunctionFromContent）
func (tr *ToolResult) _extractToolsFromContent() {
	if tr.Content == "" {
		return
	}

	content := tr.Content
	var _function interface{}

	if strings.HasPrefix(content, "{") && strings.HasSuffix(content, "}") {
		_function = vant2.JsonDecoder(content)
	} else if _function == nil && content != "" && strings.Contains(content, "{") && strings.Contains(content, "}") {
		re := regexp.MustCompile(`(?s)\{.*?\}`)
		vant2.Error(re.FindString(content))
		_function = vant2.JsonDecoder(re.FindString(content))
		if _function == nil {
			startIndex := strings.Index(content, "{")
			endIndex := strings.LastIndex(content, "}")
			if startIndex != -1 && endIndex != -1 && startIndex < endIndex {
				jsonContent := content[startIndex : endIndex+1]
				_function = vant2.JsonDecoder(jsonContent)
			}
		}
	}

	if _function != nil {
		if toolMap := gconv.Map(_function); toolMap != nil {
			tr.Tools = []g.Map{toolMap}
		}
	}
}

// _buildOpenAISSEFormat 构建符合OpenAI标准的SSE格式数据
func (l *LLM) _buildOpenAISSEFormat(sseResult *SSEResult, rawData g.Map) g.Map {
	// 基础的OpenAI SSE格式
	openaiFormat := g.Map{
		"id":      fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()),
		"object":  "chat.completion.chunk",
		"created": time.Now().Unix(),
		"model":   l.model,
		"choices": []g.Map{
			{
				"index":         0,
				"delta":         g.Map{},
				"finish_reason": nil,
			},
		},
	}

	// 检查是否完成
	if sseResult.IsComplete() {
		openaiFormat["choices"].([]g.Map)[0]["finish_reason"] = "stop"
		openaiFormat["choices"].([]g.Map)[0]["delta"] = g.Map{}

		// 添加usage信息
		if tokenUsage := sseResult.GetTokenUsage(); tokenUsage.TotalTokens > 0 {
			openaiFormat["usage"] = g.Map{
				"prompt_tokens":     tokenUsage.PromptTokens,
				"completion_tokens": tokenUsage.CompletionTokens,
				"total_tokens":      tokenUsage.TotalTokens,
			}
		}
	} else {
		// 有内容时添加到delta
		if content := sseResult.Content(); content != "" {
			openaiFormat["choices"].([]g.Map)[0]["delta"].(g.Map)["content"] = content
		}

		// 检查是否有工具调用 - 修复tool_calls格式
		if tools := sseResult.GetTools(); len(tools) > 0 {
			// 只有检测到完整的工具调用时才处理
			toolCalls := make([]g.Map, 0)
			for i, tool := range tools {
				// 从原生的tool_calls格式中提取
				if functionData := tool["function"]; functionData != nil {
					if functionMap := gconv.Map(functionData); functionMap != nil {
						toolCall := g.Map{
							"index": i,
							"id":    fmt.Sprintf("call_%d", time.Now().UnixNano()+int64(i)),
							"type":  "function",
							"function": g.Map{
								"name":      gconv.String(functionMap["name"]),
								"arguments": gconv.String(functionMap["arguments"]),
							},
						}
						toolCalls = append(toolCalls, toolCall)
					}
				} else {
					// 如果是解析出来的JSON对象格式
					toolCall := g.Map{
						"index": i,
						"id":    fmt.Sprintf("call_%d", time.Now().UnixNano()+int64(i)),
						"type":  "function",
						"function": g.Map{
							"name":      gconv.String(tool["name"]),
							"arguments": vant2.JsonEncoder(tool),
						},
					}
					toolCalls = append(toolCalls, toolCall)
				}
			}

			if len(toolCalls) > 0 {
				openaiFormat["choices"].([]g.Map)[0]["delta"].(g.Map)["tool_calls"] = toolCalls
				openaiFormat["choices"].([]g.Map)[0]["finish_reason"] = "tool_calls"
			}
		}
	}

	return openaiFormat
}
