package openai

import (
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

// _callLLMAPI 调用大模型API
func _callLLMAPI(llm *LLM, params g.Map, isStream bool, tokenUsage *TokenUsage) interface{} {
	// 获取API配置
	apiURL := llm.baseURL
	apiKey := llm.apiKey

	// 如果没有设置，从配置文件获取默认值
	if apiURL == "" {
		if defaultURL, err := g.Cfg("llm").Get(gctx.New(), "private.api_url"); err == nil {
			apiURL = defaultURL.String()
		}
	}

	if apiKey == "" {
		if defaultKey, err := g.Cfg("llm").Get(gctx.New(), "private.api_key"); err == nil {
			apiKey = defaultKey.String()
		}
	}

	// 检查必要参数
	if apiKey == "" {
		return g.Map{
			"error":      "API密钥未设置，请在配置文件中设置private.api_key或通过ApiKey()方法设置",
			"error_type": "config_error",
		}
	}

	// 构建Ajax请求参数
	ajaxArgs := &w.AjaxArgs{
		Url:           apiURL,
		Authorization: apiKey,
		Method:        "POST",
		Data:          w.Map(params),
		MaxOutTime:    60, // 60秒超时
		Headers: w.Map{
			"Content-Type": "application/json",
		},
	}

	// 打印请求信息用于调试
	vant2.ErrorMap(g.Map{
		"url": apiURL,
		"headers": g.Map{
			"Authorization": "Bearer " + apiKey[:10] + "...",
		},
		"method":  "POST",
		"timeout": 60,
	})

	// 流式调用
	if isStream && llm.streamFunc != nil {
		// 创建SSEResult实例
		sseResult := &SSEResult{}

		// 传递原有的时间信息并记录API调用开始时间
		if tokenUsage != nil {
			sseResult.tokenUsage.RequestStartTime = tokenUsage.RequestStartTime
		}
		sseResult.tokenUsage.SetApiCallStartTime()

		// 包装流式回调函数
		sseCallback := func(data w.Map) {
			// 检查流式数据中的错误
			if errorMsg := gconv.String(data["error"]); errorMsg != "" {
				vant2.Error(data, "=== SSE 流式错误 ===")
			}

			// 更新SSEResult
			sseResult._updateFromSSEData(g.Map(data))

			// 构建符合OpenAI格式的SSE数据
			openaiData := llm._buildOpenAISSEFormat(sseResult, g.Map(data))

			// 打印OpenAI格式数据用于调试
			if openaiData != nil {
				// vant2.Error(openaiData, "=== OpenAI 格式 SSE 数据 ===")
			}

			// 调用用户设定的回调函数
			llm.streamFunc(sseResult)
		}

		result := vant2.Ajax(ajaxArgs, sseCallback)

		// 检查Ajax调用本身的错误
		if result != nil {
			if resultMap := gconv.Map(result); resultMap != nil {
				if errorMsg := gconv.String(resultMap["error"]); errorMsg != "" {
					vant2.Error(resultMap, "=== Ajax SSE 调用错误 ===")
					return g.Map{
						"error":        errorMsg,
						"error_type":   "ajax_error",
						"raw_response": resultMap,
					}
				}
				if message := gconv.String(resultMap["message"]); message != "" && message != "Success" {
					vant2.Error(resultMap, "=== Ajax SSE 调用信息 ===")
					return g.Map{
						"error":        message,
						"error_type":   "ajax_timeout",
						"raw_response": resultMap,
					}
				}
			}
		}

		// 返回包含完整SSEResult的结果
		return g.Map{
			"stream_result": sseResult,
			"ajax_result":   result,
		}
	}

	// 普通调用
	result := vant2.Ajax(ajaxArgs)

	// 详细的错误处理
	if result == nil {
		vant2.Error("Ajax返回nil", "=== Ajax 调用错误 ===")
		return g.Map{
			"error":      "网络请求失败，Ajax返回nil",
			"error_type": "network_error",
		}
	}

	// 转换为Map进行错误检查
	if resultMap := gconv.Map(result); resultMap != nil {
		// 检查Ajax层面的错误（如超时、网络错误等）
		if errorMsg := gconv.String(resultMap["error"]); errorMsg != "" {
			vant2.Error(resultMap, "=== Ajax 调用错误 ===")
			return g.Map{
				"error":        errorMsg,
				"error_type":   "ajax_error",
				"raw_response": resultMap,
			}
		}

		// 检查超时错误（排除Success成功状态）
		if message := gconv.String(resultMap["message"]); message != "" && message != "Success" {
			vant2.Error(resultMap, "=== Ajax 调用超时/信息 ===")
			return g.Map{
				"error":        message,
				"error_type":   "ajax_timeout",
				"raw_response": resultMap,
			}
		}

		// 检查API层面的错误（如401、429、500等）
		if apiError := resultMap["error"]; apiError != nil {
			// 如果error是对象格式（OpenAI API标准错误格式）
			if apiErrorMap := gconv.Map(apiError); apiErrorMap != nil {
				errorMessage := gconv.String(apiErrorMap["message"])
				errorType := gconv.String(apiErrorMap["type"])
				errorCode := gconv.String(apiErrorMap["code"])

				vant2.Error(g.Map{
					"api_error":     apiErrorMap,
					"full_response": resultMap,
				}, "=== API 错误响应 ===")

				return g.Map{
					"error":          errorMessage,
					"error_type":     "api_error",
					"error_code":     errorCode,
					"api_error_type": errorType,
					"raw_response":   resultMap,
				}
			}

			// 如果error是字符串格式
			if errorStr := gconv.String(apiError); errorStr != "" {
				vant2.Error(g.Map{
					"api_error":     errorStr,
					"full_response": resultMap,
				}, "=== API 字符串错误 ===")

				return g.Map{
					"error":        errorStr,
					"error_type":   "api_error",
					"raw_response": resultMap,
				}
			}
		}

		// 检查HTTP状态码（如果vant2.Ajax包含的话）
		if useTime := gconv.Int64(resultMap["use_time"]); useTime > 0 {
			// 请求成功，但可能没有choices字段
			if choices := resultMap["choices"]; choices == nil {
				vant2.Error(resultMap, "=== API 响应缺少choices字段 ===")
				return g.Map{
					"error":        "API响应格式错误：缺少choices字段",
					"error_type":   "response_format_error",
					"raw_response": resultMap,
				}
			}
		}

		// 打印成功的响应信息
		vant2.Error(g.Map{
			"use_time":    gconv.Int64(resultMap["use_time"]),
			"has_choices": resultMap["choices"] != nil,
			"has_usage":   resultMap["usage"] != nil,
		}, "=== API 成功响应信息 ===")
	}

	return result
}
