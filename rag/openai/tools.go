package openai

import (
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// GetLastMessage 从 Message []g.Map 中获取最后一条消息
func GetLastMessage(messages []g.Map) g.Map {
	if len(messages) == 0 {
		return g.Map{}
	}
	return messages[len(messages)-1]
}

// GenerateDialogContent 生成对话内容字符串
// 传入 messages []g.Map，生成格式化的对话内容
// 只保留 role 为 user、assistant 和 tool 的消息
func GenerateDialogContent(messages []g.Map) string {
	if len(messages) == 0 {
		return ""
	}

	var content strings.Builder
	content.WriteString("当前对话内容: \n\n")

	for _, message := range messages {
		// 获取 role 字段
		role := gconv.String(message["role"])

		// 只处理 user、assistant 和 tool 角色
		if role != "user" && role != "assistant" && role != "tool" {
			continue
		}

		// 获取 content 字段并提取文本内容
		var contentText string
		if message["content"] != nil {
			// 使用 ExtractTextFromMultimodalContent 方法提取多模态内容中的文本
			contentText = ExtractTextFromMultimodalContent(message["content"])
		}

		// 如果内容为空，跳过
		if contentText == "" {
			continue
		}

		// 添加到结果中
		content.WriteString(fmt.Sprintf("%s: %s\n", role, contentText))
	}

	return content.String()
}

// GenerateHistoryFormat 生成历史会话格式字符串
// 格式：历史会话：\n user: xxx\n assistant: xxx\n 当前用户输入：xxx
func GenerateHistoryFormat(messages []g.Map) string {
	if len(messages) == 0 {
		return ""
	}

	var result strings.Builder
	result.WriteString("历史会话：\n")

	// 获取最后一条用户消息作为当前用户输入
	var currentUserInput string
	var historyMessages []g.Map

	// 从后往前查找，找到最后一条用户消息
	for i := len(messages) - 1; i >= 0; i-- {
		message := messages[i]
		role := gconv.String(message["role"])

		if role == "user" && currentUserInput == "" {
			// 提取当前用户输入
			if message["content"] != nil {
				currentUserInput = ExtractTextFromMultimodalContent(message["content"])
			}
			// 历史消息不包括最后一条用户消息
			historyMessages = messages[:i]
			break
		}
	}

	// 生成历史对话内容
	for _, message := range historyMessages {
		role := gconv.String(message["role"])

		// 只处理 user、assistant 和 tool 角色
		if role != "user" && role != "assistant" && role != "tool" {
			continue
		}

		// 获取内容文本
		var contentText string
		if message["content"] != nil {
			contentText = ExtractTextFromMultimodalContent(message["content"])
		}

		// 如果内容为空，跳过
		if contentText == "" {
			continue
		}

		// 添加到历史会话中
		result.WriteString(fmt.Sprintf("%s: %s\n", role, contentText))
	}

	// 添加当前用户输入
	if currentUserInput != "" {
		result.WriteString(fmt.Sprintf("当前用户输入：%s", currentUserInput))
	}

	return result.String()
}
