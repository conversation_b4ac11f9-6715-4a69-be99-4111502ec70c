# Embedding 模块使用文档

## Embedding 方法

1. **EmbeddingText** - 单个文本嵌入
```go
vector, err := embedding.EmbeddingText("这是一段文本")
```

2. **EmbeddingTexts** - 多个文本嵌入
```go
vectors, err := embedding.EmbeddingTexts([]string{"文本1", "文本2"})
```

## Rerank 方法

3. **Rerank** - 智能重排序
```go
results, err := embedding.Rerank(&embedding.RerankOptions{
    Query:     query,
    Documents: documents,
})

results, err := embedding.Rerank(&embedding.RerankOptions{
    Query:      query,
    Documents:  documents,
    MaxResults: 5,
    MinScore:   0.2,
})
```

**重要**: documents支持多种类型
- `[]string` - 字符串数组
- `[]interface{}` - 对象数组（自动提取content字段）
- `[]map[string]interface{}` - Map数组
- `gdb.Result` - 数据库查询结果
- 任何包含content字段的对象列表

## gdb.Result 演示

```go
// 数据库查询结果
articles, err := g.DB().Table("articles").Where("status", 1).All()
if err != nil {
    return err
}

// 重排序（自动从content字段提取文本）
results, err := embedding.Rerank(&embedding.RerankOptions{
    Query:      "机器学习",
    Documents:  articles,
    MaxResults: 10,
    MinScore:   0.3,
})

// 返回结果格式：
// [
//   {
//     "id": 1,
//     "title": "深度学习入门",
//     "content": "深度学习是机器学习的重要分支",
//     "author": "张三",
//     "created_at": "2024-01-15",
//     "relevance_score": 0.95,
//     "original_index": 0
//   }
// ]
```

**参数说明**:
- `query`: 查询字符串
- `documents`: 文档列表
- `maxResults`: 最大返回数量（可选，0=不限制）
- `minScore`: 最小相关性分数（可选，0.0=不过滤）