package embedding

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/util/gconv"
)

// RerankItem 重排序结果项
type RerankItem struct {
	Text           string  `json:"text"`            // 文档文本
	Index          int     `json:"index"`           // 原始索引
	RelevanceScore float64 `json:"relevance_score"` // 相关性分数
}

// 内部使用的完整响应结构（不对外暴露）
type _embeddingResponse struct {
	Model string `json:"model"`
	Data  []struct {
		Object    string    `json:"object"`
		Embedding []float64 `json:"embedding"`
		Index     int       `json:"index"`
	} `json:"data"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

type _rerankResponse struct {
	ID      string `json:"id"`
	Results []struct {
		Document struct {
			Text string `json:"text"`
		} `json:"document"`
		Index          int     `json:"index"`
		RelevanceScore float64 `json:"relevance_score"`
	} `json:"results"`
	Tokens struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
	} `json:"tokens"`
}

// EmbeddingError 错误结构
type EmbeddingError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

func (e *EmbeddingError) Error() string {
	return e.Message
}

// Embedding 获取文本嵌入向量
// input: 字符串 -> 返回 []float64 (单个向量)
// input: []string -> 返回 [][]float64 (多个向量数组)
func Embedding(input interface{}) (interface{}, error) {
	// 验证input参数
	if input == nil {
		return nil, fmt.Errorf("input参数不能为空")
	}

	isStringInput := false
	// 验证input类型和内容
	switch v := input.(type) {
	case string:
		if v == "" {
			return nil, fmt.Errorf("input字符串不能为空")
		}
		isStringInput = true
	case []string:
		if len(v) == 0 {
			return nil, fmt.Errorf("input数组不能为空")
		}
		for i, str := range v {
			if str == "" {
				return nil, fmt.Errorf("input数组第%d个元素不能为空", i)
			}
		}
	default:
		return nil, fmt.Errorf("input参数必须是字符串或字符串数组")
	}

	// 构建请求数据
	requestData := map[string]interface{}{
		"model": DEFAULT_EMBEDDING_MODEL,
		"input": input,
	}

	// 发送请求
	result, err := _sendRequest(EMBEDDINGS_API_URL, requestData)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var embeddingResult _embeddingResponse
	if err := json.Unmarshal(result, &embeddingResult); err != nil {
		return nil, fmt.Errorf("解析embedding响应失败: %v", err)
	}

	// 根据输入类型返回不同的结果
	if isStringInput {
		// 字符串输入，返回单个向量
		if len(embeddingResult.Data) > 0 {
			return embeddingResult.Data[0].Embedding, nil
		}
		return nil, fmt.Errorf("API响应中没有embedding数据")
	} else {
		// 数组输入，返回多个向量的数组
		embeddings := make([][]float64, len(embeddingResult.Data))
		for i, data := range embeddingResult.Data {
			embeddings[i] = data.Embedding
		}
		return embeddings, nil
	}
}

// RerankOptions 重排序选项
type RerankOptions struct {
	Query      string      `json:"query"`       // 查询字符串
	Documents  interface{} `json:"documents"`   // 文档数据：支持[]string或[]interface{}(对象数组)
	MaxResults int         `json:"max_results"` // 最大返回数量，0表示不限制
	MinScore   float64     `json:"min_score"`   // 最小相关性分数，低于此分数的结果将被过滤
}

// Rerank 智能文档重排序 - 支持字符串数组和对象数组
// 返回: 重新排序后的原始数据结构 + relevance_score
func Rerank(options *RerankOptions) (interface{}, error) {
	// 验证options参数
	if options == nil {
		return nil, fmt.Errorf("options参数不能为空")
	}

	// 验证query参数
	if options.Query == "" {
		return nil, fmt.Errorf("query参数不能为空")
	}
	if len(options.Query) > 1000 {
		return nil, fmt.Errorf("query参数长度不能超过1000字符")
	}

	// 验证documents参数
	if options.Documents == nil {
		return nil, fmt.Errorf("documents参数不能为空")
	}

	// 解析documents并提取文本内容
	documentTexts, originalDocs, isStringArray, err := _parseDocuments(options.Documents)
	if err != nil {
		return nil, err
	}

	if len(documentTexts) == 0 {
		return nil, fmt.Errorf("documents数组不能为空")
	}

	// 构建请求数据
	requestData := map[string]interface{}{
		"model":     DEFAULT_RERANK_MODEL,
		"query":     options.Query,
		"documents": documentTexts,
	}

	// 发送请求
	result, err := _sendRequest(RERANK_API_URL, requestData)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var rerankResult _rerankResponse
	if err := json.Unmarshal(result, &rerankResult); err != nil {
		return nil, fmt.Errorf("解析rerank响应失败: %v", err)
	}

	// 处理结果并应用过滤
	return _processRerankResults(rerankResult.Results, originalDocs, isStringArray, options)
}

// _parseDocuments 解析documents参数，支持字符串数组和对象数组
func _parseDocuments(documents interface{}) ([]string, []interface{}, bool, error) {
	switch docs := documents.(type) {
	case []string:
		// 字符串数组
		if len(docs) == 0 {
			return nil, nil, true, fmt.Errorf("字符串数组不能为空")
		}

		// 转换为interface{}数组以保持一致性
		originalDocs := make([]interface{}, len(docs))
		for i, doc := range docs {
			if doc == "" {
				return nil, nil, true, fmt.Errorf("documents数组第%d个元素不能为空字符串", i)
			}
			originalDocs[i] = doc
		}

		return docs, originalDocs, true, nil

	case []interface{}:
		// 对象数组
		if len(docs) == 0 {
			return nil, nil, false, fmt.Errorf("对象数组不能为空")
		}

		documentTexts := make([]string, len(docs))
		for i, doc := range docs {
			if doc == nil {
				return nil, nil, false, fmt.Errorf("documents数组第%d个元素不能为null", i)
			}

			// 尝试转换为字符串
			if str, ok := doc.(string); ok {
				if str == "" {
					return nil, nil, false, fmt.Errorf("documents数组第%d个元素不能为空字符串", i)
				}
				documentTexts[i] = str
			} else {
				// 转换为Map并提取content字段
				docMap := gconv.Map(doc)
				if content, exists := docMap["content"]; exists {
					contentStr := gconv.String(content)
					if contentStr == "" {
						return nil, nil, false, fmt.Errorf("documents数组第%d个元素的content字段不能为空", i)
					}
					documentTexts[i] = contentStr
				} else {
					return nil, nil, false, fmt.Errorf("documents数组第%d个元素必须是字符串或包含content字段的对象", i)
				}
			}
		}

		return documentTexts, docs, false, nil

	default:
		return nil, nil, false, fmt.Errorf("documents参数必须是字符串数组[]string或对象数组[]interface{}")
	}
}

// _processRerankResults 处理重排序结果并应用过滤
func _processRerankResults(apiResults []struct {
	Document struct {
		Text string `json:"text"`
	} `json:"document"`
	Index          int     `json:"index"`
	RelevanceScore float64 `json:"relevance_score"`
}, originalDocs []interface{}, isStringArray bool, options *RerankOptions) (interface{}, error) {

	// 按相关性分数排序（已经是排序的，但为了确保）
	sort.Slice(apiResults, func(i, j int) bool {
		return apiResults[i].RelevanceScore > apiResults[j].RelevanceScore
	})

	// 构建结果
	var results []interface{}

	for _, apiResult := range apiResults {
		// 应用分数过滤
		if options.MinScore > 0 && apiResult.RelevanceScore < options.MinScore {
			continue
		}

		// 检查索引有效性
		if apiResult.Index < 0 || apiResult.Index >= len(originalDocs) {
			continue
		}

		// 获取原始文档
		originalDoc := originalDocs[apiResult.Index]

		if isStringArray {
			// 字符串数组：返回包含文本和分数的结构
			results = append(results, map[string]interface{}{
				"text":            gconv.String(originalDoc),
				"relevance_score": apiResult.RelevanceScore,
				"original_index":  apiResult.Index,
			})
		} else {
			// 对象数组：在原始对象基础上添加relevance_score
			if originalDoc == nil {
				continue
			}

			// 复制原始对象并添加相关性分数
			if str, ok := originalDoc.(string); ok {
				// 如果原始是字符串，创建包含字符串和分数的对象
				results = append(results, map[string]interface{}{
					"content":         str,
					"relevance_score": apiResult.RelevanceScore,
					"original_index":  apiResult.Index,
				})
			} else {
				// 对象类型，复制并添加分数
				docMap := gconv.Map(originalDoc)
				newDoc := make(map[string]interface{})

				// 复制原始字段
				for k, v := range docMap {
					newDoc[k] = v
				}

				// 添加相关性分数
				newDoc["relevance_score"] = apiResult.RelevanceScore
				newDoc["original_index"] = apiResult.Index

				results = append(results, newDoc)
			}
		}

		// 应用最大结果数限制
		if options.MaxResults > 0 && len(results) >= options.MaxResults {
			break
		}
	}

	return results, nil
}

// _sendRequest 发送HTTP请求的内部方法
func _sendRequest(apiURL string, requestData map[string]interface{}) ([]byte, error) {
	// 获取API token
	apiToken := tokenMgr.GetNextToken()
	vant2.Error(apiToken)
	if apiToken == "" {
		return nil, fmt.Errorf("暂无可用的API密钥")
	}

	// 序列化请求数据
	requestBody, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Duration(REQUEST_TIMEOUT) * time.Second,
	}

	// 创建请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewReader(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiToken)
	req.Header.Set("User-Agent", "OpenAI-Embedding-Methods/1.0")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != 200 {
		// 尝试解析错误响应
		var errorResp EmbeddingError
		if err := json.Unmarshal(responseBody, &errorResp); err == nil {
			// 如果成功解析了错误响应，返回错误信息
			return nil, &errorResp
		} else {
			// 如果无法解析错误响应，返回HTTP状态错误
			return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
		}
	}

	return responseBody, nil
}

// 方便调用的简化函数

// EmbeddingText 对单个文本进行嵌入，直接返回向量
func EmbeddingText(text string) ([]float64, error) {
	result, err := Embedding(text)
	if err != nil {
		return nil, err
	}
	return result.([]float64), nil
}

// EmbeddingTexts 对多个文本进行嵌入，直接返回向量数组
func EmbeddingTexts(texts []string) ([][]float64, error) {
	result, err := Embedding(texts)
	if err != nil {
		return nil, err
	}
	return result.([][]float64), nil
}

// RerankDocuments 对文档进行重排序的简化方法 (兼容旧版本)
func RerankDocuments(query string, documents []string) ([]RerankItem, error) {
	results, err := Rerank(&RerankOptions{
		Query:      query,
		Documents:  documents,
		MaxResults: 0,
		MinScore:   0.0,
	})
	if err != nil {
		return nil, err
	}

	// 转换为旧的RerankItem格式
	resultArray := results.([]interface{})
	items := make([]RerankItem, len(resultArray))

	for i, result := range resultArray {
		resultMap := result.(map[string]interface{})
		items[i] = RerankItem{
			Text:           gconv.String(resultMap["text"]),
			Index:          gconv.Int(resultMap["original_index"]),
			RelevanceScore: gconv.Float64(resultMap["relevance_score"]),
		}
	}

	return items, nil
}

// RerankWithOptions 便捷的重排序方法
func RerankWithOptions(query string, documents interface{}, maxResults int, minScore float64) (interface{}, error) {
	return Rerank(&RerankOptions{
		Query:      query,
		Documents:  documents,
		MaxResults: maxResults,
		MinScore:   minScore,
	})
}

// RerankObjects 专门处理对象数组的重排序方法
func RerankObjects(query string, documents []interface{}, maxResults int, minScore float64) ([]map[string]interface{}, error) {
	results, err := Rerank(&RerankOptions{
		Query:      query,
		Documents:  documents,
		MaxResults: maxResults,
		MinScore:   minScore,
	})
	if err != nil {
		return nil, err
	}

	// 转换为map数组
	resultArray := results.([]interface{})
	objects := make([]map[string]interface{}, len(resultArray))

	for i, result := range resultArray {
		objects[i] = result.(map[string]interface{})
	}

	return objects, nil
}
