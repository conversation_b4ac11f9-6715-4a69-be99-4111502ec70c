package embedding

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Controller embedding控制器
type Controller struct {
	tokenIndex int // 当前使用的token索引
}

// TokenManager 简单的token管理器
type TokenManager struct {
	tokens []string
	index  int
}

// NewTokenManager 创建token管理器
func NewTokenManager(tokens []string) *TokenManager {
	return &TokenManager{
		tokens: tokens,
		index:  0,
	}
}

// GetNextToken 获取下一个可用token
func (tm *TokenManager) GetNextToken() string {
	if len(tm.tokens) == 0 {
		return ""
	}

	token := tm.tokens[tm.index]
	tm.index = (tm.index + 1) % len(tm.tokens)
	return token
}

// 全局token管理器
var tokenMgr = NewTokenManager(API_TOKENS)

// Embeddings 处理embeddings接口
func (c *Controller) Embeddings(r *ghttp.Request) {
	// Token验证
	if !c._validateToken(r) {
		return
	}

	// 获取请求体
	requestBody := r.GetBody()
	if len(requestBody) == 0 {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求体不能为空",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 解析并处理请求
	var reqData map[string]interface{}
	if err := json.Unmarshal(requestBody, &reqData); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求体格式错误",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 验证input参数
	input, exists := reqData["input"]
	if !exists || input == nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "缺少必需的input参数",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 验证input不能为空
	switch v := input.(type) {
	case string:
		if strings.TrimSpace(v) == "" {
			r.Response.WriteJsonExit(g.Map{
				"error": g.Map{
					"message": "input参数不能为空字符串",
					"type":    "invalid_request_error",
				},
			})
			return
		}
	case []interface{}:
		if len(v) == 0 {
			r.Response.WriteJsonExit(g.Map{
				"error": g.Map{
					"message": "input参数不能为空数组",
					"type":    "invalid_request_error",
				},
			})
			return
		}
		// 检查数组中的每个元素都是字符串且不为空
		for i, item := range v {
			if str, ok := item.(string); !ok {
				r.Response.WriteJsonExit(g.Map{
					"error": g.Map{
						"message": fmt.Sprintf("input数组第%d个元素必须是字符串", i),
						"type":    "invalid_request_error",
					},
				})
				return
			} else if strings.TrimSpace(str) == "" {
				r.Response.WriteJsonExit(g.Map{
					"error": g.Map{
						"message": fmt.Sprintf("input数组第%d个元素不能为空字符串", i),
						"type":    "invalid_request_error",
					},
				})
				return
			}
		}
	default:
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "input参数必须是字符串或字符串数组",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 强制使用默认模型，不管用户传什么
	reqData["model"] = DEFAULT_EMBEDDING_MODEL

	// 重新序列化请求体
	modifiedBody, err := json.Marshal(reqData)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求处理失败",
				"type":    "internal_error",
			},
		})
		return
	}

	// 记录请求开始日志
	clientToken := strings.TrimPrefix(r.Header.Get("Authorization"), "Bearer ")
	apiToken := tokenMgr.GetNextToken()
	if apiToken == "" {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "暂无可用的API密钥",
				"type":    "server_error",
			},
		})
		return
	}

	logEntry := embeddingLogger.LogRequest(r, clientToken, apiToken, "embeddings", modifiedBody)

	// 转发请求
	c._forwardRequest(r, EMBEDDINGS_API_URL, modifiedBody, "embeddings", logEntry)
}

// Rerank 处理rerank接口
func (c *Controller) Rerank(r *ghttp.Request) {
	// Token验证
	if !c._validateToken(r) {
		return
	}

	// 获取请求体
	requestBody := r.GetBody()
	if len(requestBody) == 0 {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求体不能为空",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 解析并处理请求
	var reqData map[string]interface{}
	if err := json.Unmarshal(requestBody, &reqData); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求体格式错误",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 验证query参数
	query, exists := reqData["query"]
	if !exists || query == nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "缺少必需的query参数",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	queryStr, ok := query.(string)
	if !ok {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "query参数必须是字符串",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 验证query长度在0-1000之间
	if len(queryStr) == 0 || len(queryStr) > 1000 {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "query参数长度必须在1-1000字符之间",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 验证documents参数
	documents, exists := reqData["documents"]
	if !exists || documents == nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "缺少必需的documents参数",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	documentsArray, ok := documents.([]interface{})
	if !ok {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "documents参数必须是字符串数组",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	if len(documentsArray) == 0 {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "documents数组不能为空",
				"type":    "invalid_request_error",
			},
		})
		return
	}

	// 检查documents数组中的每个元素都是字符串
	for i, doc := range documentsArray {
		if _, ok := doc.(string); !ok {
			r.Response.WriteJsonExit(g.Map{
				"error": g.Map{
					"message": fmt.Sprintf("documents数组第%d个元素必须是字符串", i),
					"type":    "invalid_request_error",
				},
			})
			return
		}
	}

	// 强制使用默认模型，不管用户传什么
	reqData["model"] = DEFAULT_RERANK_MODEL

	// 重新序列化请求体
	modifiedBody, err := json.Marshal(reqData)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求处理失败",
				"type":    "internal_error",
			},
		})
		return
	}

	// 记录请求开始日志
	clientToken := strings.TrimPrefix(r.Header.Get("Authorization"), "Bearer ")
	apiToken := tokenMgr.GetNextToken()
	if apiToken == "" {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "暂无可用的API密钥",
				"type":    "server_error",
			},
		})
		return
	}

	logEntry := embeddingLogger.LogRequest(r, clientToken, apiToken, "rerank", modifiedBody)

	// 转发请求
	c._forwardRequest(r, RERANK_API_URL, modifiedBody, "rerank", logEntry)
}

// _validateToken 验证客户端Token
func (c *Controller) _validateToken(r *ghttp.Request) bool {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "缺少Authorization头",
				"type":    "authentication_error",
			},
		})
		return false
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")
	for _, fixedToken := range FIXED_TOKENS {
		if token == fixedToken {
			return true
		}
	}

	r.Response.WriteJsonExit(g.Map{
		"error": g.Map{
			"message": "无效的API密钥",
			"type":    "authentication_error",
		},
	})
	return false
}

// _forwardRequest 转发请求到目标API
func (c *Controller) _forwardRequest(r *ghttp.Request, apiURL string, requestBody []byte, apiType string, logEntry *EmbeddingLogEntry) {
	startTime := time.Now()

	// 从日志条目中获取API token
	apiToken := logEntry.TargetToken

	// 创建HTTP请求
	client := &http.Client{
		Timeout: time.Duration(REQUEST_TIMEOUT) * time.Second,
	}

	req, err := http.NewRequest("POST", apiURL, bytes.NewReader(requestBody))
	if err != nil {
		// g.Log().Error(r.GetCtx(), "创建请求失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "请求创建失败",
				"type":    "server_error",
			},
		})
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiToken)

	// 复制原始请求的其他头信息（除了Authorization）
	for name, values := range r.Header {
		if strings.ToLower(name) != "authorization" && strings.ToLower(name) != "content-length" {
			for _, value := range values {
				req.Header.Add(name, value)
			}
		}
	}

	// 记录发起请求时间
	requestSentTime := time.Now()

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		// 记录错误日志
		embeddingLogger.LogResponse(logEntry, 500, []byte(err.Error()), startTime, requestSentTime)
		// g.Log().Error(r.GetCtx(), "请求发送失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "代理请求失败: " + err.Error(),
				"type":    "server_error",
			},
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		// 记录错误日志
		embeddingLogger.LogResponse(logEntry, 500, []byte("读取响应失败"), startTime, requestSentTime)
		// g.Log().Error(r.GetCtx(), "读取响应失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": "读取响应失败",
				"type":    "server_error",
			},
		})
		return
	}

	// 记录成功响应日志
	embeddingLogger.LogResponse(logEntry, resp.StatusCode, responseBody, startTime, requestSentTime)

	// 设置响应头
	r.Response.Header().Set("Content-Type", "application/json")

	// 复制状态码
	r.Response.Status = resp.StatusCode

	// 返回响应
	r.Response.Write(responseBody)
}

// Stop 停止服务并清理资源
func (c *Controller) Stop() {
	// 停止日志记录器
	embeddingLogger.Stop()
}
