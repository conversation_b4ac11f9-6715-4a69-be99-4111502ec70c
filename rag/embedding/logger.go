package embedding

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
)

// EmbeddingLogEntry embedding日志条目结构
type EmbeddingLogEntry struct {
	Time             string `json:"time"`              // 请求时间
	RequestToken     string `json:"request_token"`     // 用户请求token
	TargetToken      string `json:"target_token"`      // 目标API token
	RequestIP        string `json:"request_ip"`        // 请求IP
	APIType          string `json:"api_type"`          // API类型: embeddings 或 rerank
	InputContent     string `json:"input_content"`     // 输入内容
	OutputContent    string `json:"output_content"`    // 输出内容(仅rerank有)
	Status           int    `json:"status"`            // 状态码
	InputTokens      int    `json:"input_tokens"`      // 输入token数
	TotalTokens      int    `json:"total_tokens"`      // 总token数
	RequestReceived  int64  `json:"request_received"`  // 接收到请求的时间(ms)
	RequestSent      int64  `json:"request_sent"`      // 发起请求到API的时间(ms)
	ResponseReceived int64  `json:"response_received"` // API返回数据的时间(ms)
	TotalTime        int64  `json:"total_time"`        // 总用时(ms)
}

// EmbeddingLogger embedding日志记录器
type EmbeddingLogger struct {
	logChan  chan *EmbeddingLogEntry
	wg       sync.WaitGroup
	stopChan chan struct{}
}

// NewEmbeddingLogger 创建日志记录器
func NewEmbeddingLogger() *EmbeddingLogger {
	logger := &EmbeddingLogger{
		logChan:  make(chan *EmbeddingLogEntry, 1000), // 异步日志缓冲区
		stopChan: make(chan struct{}),
	}

	// 启动异步日志写入协程
	logger.wg.Add(1)
	go logger.asyncLogWriter()

	return logger
}

// WriteLog 异步写入请求日志
func (l *EmbeddingLogger) WriteLog(entry *EmbeddingLogEntry) {
	select {
	case l.logChan <- entry:
		// 成功放入异步队列
	default:
		// 如果队列满了，记录警告但不阻塞
		// g.Log().Warning(nil, "embedding日志队列已满，丢弃日志条目")
	}
}

// asyncLogWriter 异步日志写入协程
func (l *EmbeddingLogger) asyncLogWriter() {
	defer l.wg.Done()

	for {
		select {
		case entry := <-l.logChan:
			l.writeLogSync(entry)
		case <-l.stopChan:
			// 处理剩余的日志条目
			for {
				select {
				case entry := <-l.logChan:
					l.writeLogSync(entry)
				default:
					return
				}
			}
		}
	}
}

// writeLogSync 同步写入日志（内部方法）
func (l *EmbeddingLogger) writeLogSync(entry *EmbeddingLogEntry) {
	// 确保logs目录存在
	if !gfile.Exists(LOG_DIR) {
		err := gfile.Mkdir(LOG_DIR)
		if err != nil {
			// g.Log().Error(nil, "创建日志目录失败:", err)
			return
		}
	}

	// 生成日志文件名 (按日期)
	now := time.Now()
	filename := fmt.Sprintf(LOG_FILE_FORMAT, now.Format("2006-01-02"))
	logFile := filepath.Join(LOG_DIR, filename)

	// 格式化日志内容
	logContent := l.formatLogEntry(entry)

	// 写入文件
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		// g.Log().Error(nil, "打开日志文件失败:", err)
		return
	}
	defer file.Close()

	_, err = file.WriteString(logContent + "\n")
	if err != nil {
		// g.Log().Error(nil, "写入日志失败:", err)
	}
}

// Stop 停止日志记录器，等待所有日志写入完成
func (l *EmbeddingLogger) Stop() {
	close(l.stopChan)
	l.wg.Wait()
}

// LogRequest 记录请求开始日志
func (l *EmbeddingLogger) LogRequest(r *ghttp.Request, requestToken, targetToken, apiType string, requestBody []byte) *EmbeddingLogEntry {
	// 解析输入内容
	inputContent := l.extractInputContent(requestBody, apiType)

	return &EmbeddingLogEntry{
		Time:             time.Now().Format("2006-01-02 15:04:05"),
		RequestToken:     requestToken,
		TargetToken:      targetToken,
		RequestIP:        l.getRealIP(r),
		APIType:          apiType,
		InputContent:     inputContent,
		OutputContent:    "",
		Status:           0,
		InputTokens:      0,
		TotalTokens:      0,
		RequestReceived:  time.Now().UnixMilli(),
		RequestSent:      0,
		ResponseReceived: 0,
		TotalTime:        0,
	}
}

// LogResponse 记录响应完成日志
func (l *EmbeddingLogger) LogResponse(entry *EmbeddingLogEntry, statusCode int, responseBody []byte, startTime time.Time, requestSentTime time.Time) {
	now := time.Now()

	// 计算用时
	totalTime := now.Sub(startTime).Milliseconds()

	// 提取token信息和输出内容
	inputTokens, totalTokens, outputContent := l.extractResponseInfo(responseBody, entry.APIType)

	// 更新日志条目
	entry.Status = statusCode
	entry.OutputContent = outputContent
	entry.InputTokens = inputTokens
	entry.TotalTokens = totalTokens
	entry.RequestSent = requestSentTime.UnixMilli()
	entry.TotalTime = totalTime
	entry.ResponseReceived = now.UnixMilli()

	// 写入日志
	l.WriteLog(entry)
}

// formatLogEntry 格式化日志条目
func (l *EmbeddingLogger) formatLogEntry(entry *EmbeddingLogEntry) string {
	// 将毫秒时间戳转换为可读时间格式
	requestReceivedTime := ""
	requestSentTime := ""
	responseReceivedTime := ""

	if entry.RequestReceived > 0 {
		requestReceivedTime = time.UnixMilli(entry.RequestReceived).Format("15:04:05.000")
	}
	if entry.RequestSent > 0 {
		requestSentTime = time.UnixMilli(entry.RequestSent).Format("15:04:05.000")
	}
	if entry.ResponseReceived > 0 {
		responseReceivedTime = time.UnixMilli(entry.ResponseReceived).Format("15:04:05.000")
	}

	// 根据API类型显示不同的请求类型描述
	var requestType string
	if entry.APIType == "embeddings" {
		requestType = "Embeddings文本嵌入请求"
	} else if entry.APIType == "rerank" {
		requestType = "Rerank文本重排请求"
	} else {
		requestType = "未知请求类型"
	}

	// 构建日志内容
	logFormat := `----------------------------------------
时间: %s
API类型: %s
请求Token: %s
目标Token: %s
请求IP: %s
输入内容: %s`

	// 对于rerank，添加输出内容
	if entry.APIType == "rerank" && entry.OutputContent != "" {
		logFormat += `
输出内容: %s`
	}

	logFormat += `
状态: %d
输入token数: %d
总token数: %d
接收请求时间: %s
发起请求时间: %s
接收响应时间: %s
总用时: %dms
请求类型: %s
----------------------------------------`

	// 准备参数
	args := []interface{}{
		entry.Time,
		strings.ToUpper(entry.APIType),
		entry.RequestToken,
		entry.TargetToken,
		entry.RequestIP,
		entry.InputContent,
	}

	// 如果是rerank且有输出内容，添加输出参数
	if entry.APIType == "rerank" && entry.OutputContent != "" {
		args = append(args, entry.OutputContent)
	}

	// 添加剩余参数
	args = append(args,
		entry.Status,
		entry.InputTokens,
		entry.TotalTokens,
		requestReceivedTime,
		requestSentTime,
		responseReceivedTime,
		entry.TotalTime,
		requestType,
	)

	return fmt.Sprintf(logFormat, args...)
}

// extractInputContent 提取输入内容
func (l *EmbeddingLogger) extractInputContent(requestBody []byte, apiType string) string {
	var reqData map[string]interface{}
	if err := json.Unmarshal(requestBody, &reqData); err != nil {
		return string(requestBody)
	}

	// 根据API类型提取不同的输入字段
	if apiType == "embeddings" {
		if input, exists := reqData["input"]; exists {
			if inputBytes, err := json.MarshalIndent(input, "", "  "); err == nil {
				return string(inputBytes)
			}
		}
	} else if apiType == "rerank" {
		// rerank需要记录query和documents
		inputMap := make(map[string]interface{})
		if query, exists := reqData["query"]; exists {
			inputMap["query"] = query
		}
		if documents, exists := reqData["documents"]; exists {
			inputMap["documents"] = documents
		}
		if inputBytes, err := json.MarshalIndent(inputMap, "", "  "); err == nil {
			return string(inputBytes)
		}
	}

	// 如果提取失败，返回格式化的完整请求体
	if formattedBytes, err := json.MarshalIndent(reqData, "", "  "); err == nil {
		return string(formattedBytes)
	}
	return string(requestBody)
}

// extractResponseInfo 从响应中提取token信息和输出内容
func (l *EmbeddingLogger) extractResponseInfo(responseBody []byte, apiType string) (inputTokens, totalTokens int, outputContent string) {
	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return 0, 0, ""
	}

	// 提取token使用信息
	if usage, ok := response["usage"].(map[string]interface{}); ok {
		if promptTokens, ok := usage["prompt_tokens"].(float64); ok {
			inputTokens = int(promptTokens)
		}
		if totalTokensFloat, ok := usage["total_tokens"].(float64); ok {
			totalTokens = int(totalTokensFloat)
		}
	}

	// 对于rerank，提取输出结果
	if apiType == "rerank" {
		if results, exists := response["results"]; exists {
			if outputBytes, err := json.MarshalIndent(results, "", "  "); err == nil {
				outputContent = string(outputBytes)
			}
		}
	}

	return inputTokens, totalTokens, outputContent
}

// getRealIP 获取真实IP
func (l *EmbeddingLogger) getRealIP(r *ghttp.Request) string {
	// 尝试从X-Forwarded-For获取
	if ip := r.Header.Get("X-Forwarded-For"); ip != "" {
		return strings.Split(ip, ",")[0]
	}

	// 尝试从X-Real-IP获取
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		return ip
	}

	// 使用RemoteAddr
	return r.GetRemoteIp()
}

// 全局日志记录器实例
var embeddingLogger = NewEmbeddingLogger()
