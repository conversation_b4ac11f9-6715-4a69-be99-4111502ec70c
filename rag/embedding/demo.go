package embedding

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// DemoController demo控制器
type DemoController struct{}

// EmbeddingDemo 演示embedding方法
func (d *DemoController) EmbeddingDemo(r *ghttp.Request) {
	// 演示单个文本嵌入
	singleText := "这是一个测试文本，用于演示embedding功能"
	singleEmbedding, err := EmbeddingText(singleText)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 演示多个文本嵌入
	multipleTexts := []string{
		"苹果是一种水果",
		"香蕉也是水果",
		"汽车是交通工具",
		"飞机可以在天空中飞行",
	}
	multipleEmbeddings, err := EmbeddingTexts(multipleTexts)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 返回演示结果
	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "Embedding演示成功！",
		"results": g.Map{
			"single_text": g.Map{
				"input":            singleText,
				"embedding_length": len(singleEmbedding),
				"first_10_values":  singleEmbedding[:10], // 只显示前10个值
			},
			"multiple_texts": g.Map{
				"input_count":        len(multipleTexts),
				"input_texts":        multipleTexts,
				"embeddings_count":   len(multipleEmbeddings),
				"embedding_lengths":  []int{len(multipleEmbeddings[0]), len(multipleEmbeddings[1]), len(multipleEmbeddings[2]), len(multipleEmbeddings[3])},
				"first_embedding_10": multipleEmbeddings[0][:10], // 第一个embedding的前10个值
			},
		},
	})
}

// RerankDemo 演示新的强大rerank功能
func (d *DemoController) RerankDemo(r *ghttp.Request) {
	// 1. 演示字符串数组重排序
	query1 := "水果"
	stringDocs := []string{
		"苹果是一种很受欢迎的水果，含有丰富的维生素",
		"汽车是现代社会重要的交通工具",
		"香蕉富含钾元素，是健康的水果选择",
		"电脑可以帮助我们处理各种数据和信息",
		"橙子含有大量维生素C，对健康有益",
		"手机是现代人必备的通讯设备",
	}

	stringResults, err := Rerank(&RerankOptions{
		Query:     query1,
		Documents: stringDocs,
	})
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   "字符串数组重排序失败: " + err.Error(),
		})
		return
	}

	// 2. 演示对象数组重排序
	query2 := "人工智能"
	objectDocs := []interface{}{
		g.Map{"id": 1, "title": "AI技术", "content": "人工智能技术正在改变世界", "category": "tech"},
		g.Map{"id": 2, "title": "美食推荐", "content": "今天推荐一道美味的红烧肉", "category": "food"},
		g.Map{"id": 3, "title": "机器学习", "content": "机器学习是人工智能的重要分支", "category": "tech"},
		g.Map{"id": 4, "title": "旅游攻略", "content": "春天是去日本赏樱花的好时节", "category": "travel"},
		g.Map{"id": 5, "title": "深度学习", "content": "深度神经网络在图像识别领域表现出色", "category": "tech"},
		g.Map{"id": 6, "title": "运动健身", "content": "每天坚持运动有益身体健康", "category": "health"},
	}

	objectResults, err := Rerank(&RerankOptions{
		Query:     query2,
		Documents: objectDocs,
	})
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   "对象数组重排序失败: " + err.Error(),
		})
		return
	}

	// 3. 演示带过滤的重排序
	filteredResults, err := Rerank(&RerankOptions{
		Query:      query2,
		Documents:  objectDocs,
		MaxResults: 3,
		MinScore:   0.1,
	})
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   "过滤重排序失败: " + err.Error(),
		})
		return
	}

	// 返回演示结果
	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "🎉 强大的Rerank功能演示成功！",
		"features": []string{
			"✅ 支持字符串数组和对象数组",
			"✅ 自动提取对象中的content字段",
			"✅ 返回原始数据结构 + relevance_score",
			"✅ 支持最大返回数量限制",
			"✅ 支持最小相关性分数过滤",
		},
		"results": g.Map{
			"string_array_demo": g.Map{
				"query":       query1,
				"input_count": len(stringDocs),
				"results":     stringResults,
				"description": "字符串数组重排序，返回包含text和relevance_score的结构",
			},
			"object_array_demo": g.Map{
				"query":       query2,
				"input_count": len(objectDocs),
				"results":     objectResults,
				"description": "对象数组重排序，在原始对象基础上添加relevance_score字段",
			},
			"filtered_demo": g.Map{
				"query":          query2,
				"filter_options": g.Map{"max_results": 3, "min_score": 0.1},
				"results":        filteredResults,
				"description":    "过滤重排序，只返回前3个且相关性分数>=0.1的结果",
			},
		},
	})
}

// AdvancedRerankDemo 高级重排序演示 - 简洁版
func (d *DemoController) AdvancedRerankDemo(r *ghttp.Request) {
	// 简单的实际应用场景：搜索文章
	query := "机器学习"
	articles := []interface{}{
		g.Map{"id": 1, "title": "深度学习入门", "content": "深度学习是机器学习的重要分支", "author": "张三"},
		g.Map{"id": 2, "title": "美食制作", "content": "教你制作美味家常菜", "author": "李四"},
		g.Map{"id": 3, "title": "Python实战", "content": "使用Python实现机器学习算法", "author": "王五"},
		g.Map{"id": 4, "title": "旅游攻略", "content": "春天旅游好去处推荐", "author": "赵六"},
	}

	// 演示：获取前2个，相关性分数>=0.1的结果
	results, err := Rerank(&RerankOptions{
		Query:      query,
		Documents:  articles,
		MaxResults: 2,
		MinScore:   0.1,
	})
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   "演示失败: " + err.Error(),
		})
		return
	}

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "✅ 高级重排序演示成功",
		"demo": g.Map{
			"query":          query,
			"input_articles": articles,
			"filter_options": g.Map{"max_results": 2, "min_score": 0.1},
			"sorted_results": results,
			"result_count":   len(results.([]interface{})),
		},
		"usage": "Rerank(&RerankOptions{Query: query, Documents: documents, MaxResults: 2, MinScore: 0.1})",
	})
}

// CombinedDemo 综合演示 - 同时展示embedding和rerank
func (d *DemoController) CombinedDemo(r *ghttp.Request) {
	// 1. 先演示embedding
	texts := []string{
		"人工智能技术正在改变世界",
		"机器学习是AI的重要分支",
		"深度学习在图像识别中表现出色",
	}

	embeddings, err := EmbeddingTexts(texts)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   "Embedding失败: " + err.Error(),
		})
		return
	}

	// 2. 再演示新的强大rerank
	query := "人工智能"
	documents := []interface{}{
		g.Map{"id": 1, "content": "人工智能是计算机科学的一个分支", "type": "tech"},
		g.Map{"id": 2, "content": "今天天气很好，适合出门游玩", "type": "weather"},
		g.Map{"id": 3, "content": "机器学习算法可以从数据中学习模式", "type": "tech"},
		g.Map{"id": 4, "content": "我喜欢吃苹果和香蕉", "type": "food"},
		g.Map{"id": 5, "content": "深度神经网络在AI领域应用广泛", "type": "tech"},
		g.Map{"id": 6, "content": "明天要去超市买菜", "type": "daily"},
	}

	rerankResults, err := RerankWithOptions(query, documents, 4, 0.1)
	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"success": false,
			"error":   "Rerank失败: " + err.Error(),
		})
		return
	}

	// 3. 返回综合结果
	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "🎉 Embedding + 强大Rerank 综合演示成功！",
		"new_features": g.Map{
			"embedding": []string{
				"✅ 支持单个/多个文本向量化",
				"✅ 直接返回向量数据，无冗余结构",
			},
			"rerank": []string{
				"✅ 支持字符串数组和对象数组",
				"✅ 自动提取content字段",
				"✅ 智能过滤：最大数量 + 最小分数",
				"✅ 保持原始数据结构并添加相关性分数",
				"✅ 真正实用的封装方法",
			},
		},
		"embedding_demo": g.Map{
			"input_texts":      texts,
			"embeddings_count": len(embeddings),
			"vector_dimension": len(embeddings[0]),
			"sample_values":    embeddings[0][:5], // 显示第一个向量的前5个值
		},
		"rerank_demo": g.Map{
			"query":            query,
			"input_documents":  documents,
			"filter_options":   g.Map{"max_results": 4, "min_score": 0.1},
			"filtered_results": rerankResults,
		},
	})
}

// DemoIndex 演示首页 - 提供所有演示链接
func (d *DemoController) DemoIndex(r *ghttp.Request) {
	r.Response.WriteJsonExit(g.Map{
		"message":     "🚀 Embedding & 强大Rerank 演示中心",
		"description": "欢迎使用我们升级后的文本嵌入和智能文档重排序演示！",
		"new_features": g.Map{
			"rerank_upgrades": []string{
				"🎯 支持对象数组输入（自动提取content字段）",
				"🔥 智能过滤（最大返回数量 + 最小相关性分数）",
				"💪 保持原始数据结构并添加relevance_score",
				"⚡ 真正实用的封装方法",
			},
		},
		"available_demos": g.Map{
			"embedding_demo": g.Map{
				"url":         "/demo/embedding",
				"description": "演示文本嵌入功能，包括单个文本和多个文本的向量化",
				"method":      "GET",
			},
			"rerank_demo": g.Map{
				"url":         "/demo/rerank",
				"description": "演示强大的重排序功能：支持字符串数组、对象数组、智能过滤",
				"method":      "GET",
			},
			"advanced_rerank_demo": g.Map{
				"url":         "/demo/advanced-rerank",
				"description": "高级重排序演示：模拟实际应用场景，展示各种过滤选项",
				"method":      "GET",
			},
			"combined_demo": g.Map{
				"url":         "/demo/combined",
				"description": "综合演示，同时展示embedding和升级版rerank的强大功能",
				"method":      "GET",
			},
		},
		"usage_examples": g.Map{
			"programmatic_usage": g.Map{
				"embedding":       "embedding.EmbeddingText('文本') 或 embedding.EmbeddingTexts([]string)",
				"rerank_simple":   "embedding.Rerank(&embedding.RerankOptions{Query: query, Documents: documents})",
				"rerank_filtered": "embedding.Rerank(&embedding.RerankOptions{Query: query, Documents: documents, MaxResults: 5, MinScore: 0.2})",
				"rerank_legacy":   "embedding.RerankWithOptions(query, documents, maxResults, minScore) // 便捷方法",
			},
			"api_usage": "也可以通过 /v1/embeddings 和 /v1/rerank 接口调用",
		},
	})
}
