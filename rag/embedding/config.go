package embedding

const (
	// API名称
	API_NAME = "embedding"

	// Embeddings API地址
	EMBEDDINGS_API_URL = "https://api.siliconflow.cn/v1/embeddings"

	// Rerank API地址
	RERANK_API_URL = "https://api.siliconflow.cn/v1/rerank"

	// 请求超时时间（秒）
	REQUEST_TIMEOUT = 60

	// 日志目录
	LOG_DIR = "./runtime/logs"

	// 日志文件名格式
	LOG_FILE_FORMAT = "embedding_proxy_%s.log"

	// 默认模型配置
	DEFAULT_EMBEDDING_MODEL = "BAAI/bge-m3"
	DEFAULT_RERANK_MODEL    = "BAAI/bge-reranker-v2-m3"
)

// API Token池配置
var API_TOKENS = []string{
	"sk-zjtdzijmuylwbhufsvguqasrtqqdzxvdpfekzcnrswtnoher", // 17701058496
	"sk-zgimbicibzmbunblwjcpizzbycjjidyssttdnfwglashzmmv", // 13700343692
	"sk-uroafwfvqkjntjmmmvfopeppupqjxtzfhyzuktiadvurhsul", // 15632627397
	"sk-exilfqodorlyvbmcquxfosbixcppkjaxdapwwprlrwbqezzd", // 13585251053 小颖猪
	"sk-ivfjczwadyooxbhdfxsuuupldtqivkiabzlcuakvahocqwlt", // 19975008369 小颖猪
	"sk-lvnvtgjuqgcqhevqfzrakwqwndvlwjgwsuymkddmwkgogbut", // 17761816734 小洁玉
}

// 客户端可使用的固定Token列表
var FIXED_TOKENS = []string{
	"sk-123213123123",
	"sk-456456456456",
	"sk-5f5c21f24622b17a4c17c39fdf49b5e7",
}
