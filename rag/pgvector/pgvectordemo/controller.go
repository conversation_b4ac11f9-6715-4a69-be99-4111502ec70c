package pgvectordemo

import (
	"assistant/rag/pgvector"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Controller pgvector控制器
type Controller struct{}

// TestIndex 测试首页
func (c *Controller) TestIndex(r *ghttp.Request) {
	html := `<!DOCTYPE html><html><head><meta charset="utf-8"><title>PGVector CRUD</title><style>body{font:14px Arial;margin:20px;background:#f8f9fa}h1{color:#333}h2{color:#666;border-bottom:1px solid #ddd;padding-bottom:5px}button{background:#007cba;color:#fff;border:0;padding:6px 12px;margin:2px;border-radius:3px;cursor:pointer}button:hover{background:#005a87}button.qa{background:#28a745}button.qa:hover{background:#1e7e34}.result{margin:10px 0;padding:10px;background:#e7f3ff;border-radius:3px;font-family:monospace;white-space:pre-wrap;max-height:400px;overflow-y:auto}</style></head><body>
<h1>🔍 PGVector 全功能测试工具</h1>

<h2>📄 文档向量库测试</h2>
<p><strong>初始化:</strong> <button onclick="test('/pgvector/crud/init')">初始化测试数据</button> <button onclick="test('/pgvector/crud/clean')">清理数据</button></p>
<p><strong>新增:</strong> <button onclick="test('/pgvector/crud/create')">基础新增</button> <button onclick="test('/pgvector/crud/create-split')">新增带分割</button></p>
<p><strong>查询:</strong> <button onclick="test('/pgvector/crud/query')">普通查询</button> <button onclick="test('/pgvector/crud/search')">语义搜索</button> <button onclick="test('/pgvector/crud/search-manual-embedding')">手动Embedding搜索</button> <button onclick="test('/pgvector/crud/rerank')">Rerank高级查询</button></p>
<p><strong>高级查询:</strong> <button onclick="test('/pgvector/crud/query-content')">策略1:去重文档</button> <button onclick="test('/pgvector/crud/search-split-docs')">策略2:所有段落</button></p>
<p><strong>修改删除:</strong> <button onclick="test('/pgvector/crud/update')">修改数据</button> <button onclick="test('/pgvector/crud/update-split')">修改带分割数据</button> <button onclick="test('/pgvector/crud/delete')">删除数据</button></p>
<p><strong>调试:</strong> <button onclick="test('/pgvector/crud/debug-tables')">查看三表数据</button></p>

<h2>❓ QA问答库测试</h2>
<p><strong>初始化:</strong> <button class="qa" onclick="test('/pgvector/qa/clean')">清理QA数据</button></p>
<p><strong>新增:</strong> <button class="qa" onclick="test('/pgvector/qa/create')">新增QA</button> <button class="qa" onclick="test('/pgvector/qa/create-batch')">批量新增QA</button></p>
<p><strong>查询:</strong> <button class="qa" onclick="test('/pgvector/qa/query')">查询QA</button> <button class="qa" onclick="test('/pgvector/qa/search')">语义搜索QA</button> <button class="qa" onclick="test('/pgvector/qa/rerank')">QA Rerank高级搜索</button></p>
<p><strong>修改删除:</strong> <button class="qa" onclick="test('/pgvector/qa/update')">更新QA</button> <button class="qa" onclick="test('/pgvector/qa/delete')">删除QA</button></p>

<div id="result"></div>
<script>async function test(url){const r=document.getElementById('result');r.innerHTML='<div class="result">⏳ 执行中...</div>';try{const res=await fetch(url);const data=await res.json();r.innerHTML='<div class="result">'+JSON.stringify(data,null,2)+'</div>';}catch(e){r.innerHTML='<div class="result">❌ '+e.message+'</div>';}}</script>
</body></html>`
	r.Response.Header().Set("Content-Type", "text/html; charset=utf-8")
	r.Response.Write(html)
}

// 🔧 初始化测试数据 - 按钮名称:"初始化测试数据"
func (c *Controller) CrudInit(r *ghttp.Request) {
	timestamp := int(time.Now().Unix())

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "初始化完成",
		"data": g.Map{
			"action":    "初始化测试环境",
			"timestamp": timestamp,
			"tables":    []string{"pgvector_search", "pgvector_search_content", "pgvector_search_split"},
			"note":      "准备进行CRUD演示",
		},
	})
}

// 🧹 清理数据 - 按钮名称:"清理数据"
func (c *Controller) CrudClean(r *ghttp.Request) {
	// 执行清理
	pg := pgvector.NewDB()
	pg.Where(g.Map{"model": "demo"}).Delete()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "数据清理完成",
		"data": g.Map{
			"action": "清理demo数据",
			"note":   "已删除所有model=demo的测试数据",
		},
	})
}

// ➕ 基础新增 - 按钮名称:"基础新增"
func (c *Controller) CrudCreate(r *ghttp.Request) {
	pg := pgvector.NewDB()

	success := pg.Data(g.Map{
		"content":     "这是一个关于人工智能的基础介绍文档",
		"content_raw": "AI基础介绍",
		"title":       "人工智能基础指南",
		"extra":       `{"litpic":"https://example.com/ai-cover.jpg","writer":"张三"}`,
		"user_id":     1001,
		"model":       "demo",
		"aid":         1,
		"channel":     "tech",
		"status":      "active",
	}).SaveModel()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "基础新增完成",
		"data": g.Map{
			"action": "新增AI文档",
			"result": success,
			"note":   "保存到基础表，自动生成embedding",
		},
	})
}

// ➕ 新增带分割 - 按钮名称:"新增带分割"
func (c *Controller) CrudCreateSplit(r *ghttp.Request) {
	longContent := `
	深度学习是机器学习的一个重要分支，它模仿人脑神经网络的工作原理。通过多层神经网络，深度学习能够处理复杂的数据模式。

	在图像识别领域，深度学习取得了突破性进展。卷积神经网络（CNN）可以自动提取图像特征，实现高精度的图像分类和目标检测。

	自然语言处理也是深度学习的重要应用领域。循环神经网络（RNN）和Transformer架构使得机器能够理解和生成人类语言。

	深度学习的训练需要大量的数据和计算资源。GPU的发展为深度学习的普及提供了强大的硬件支持。
	`

	pg := pgvector.NewDB()

	success := pg.SplitLength(100).Data(g.Map{
		"content":     longContent,
		"content_raw": "深度学习详解",
		"title":       "深度学习技术详解",
		"extra":       `{"litpic":"https://example.com/dl-cover.jpg","writer":"李四","category":"AI技术"}`,
		"user_id":     1001,
		"model":       "demo",
		"aid":         2,
		"channel":     "tech",
		"status":      "active",
	}).SaveModel()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "带分割新增完成",
		"data": g.Map{
			"action":       "新增长文档",
			"result":       success,
			"split_length": 100,
			"note":         "长内容自动分割存储到三个表",
		},
	})
}

// 🔍 普通查询 - 按钮名称:"普通查询"
func (c *Controller) CrudQuery(r *ghttp.Request) {
	pg := pgvector.NewDB()

	results := pg.Where(g.Map{"model": "demo"}).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "普通查询完成",
		"data": g.Map{
			"action": "查询demo数据",
			"count":  len(results),
			"items":  results,
		},
	})
}

// 🧠 语义搜索 - 按钮名称:"语义搜索"
func (c *Controller) CrudSearch(r *ghttp.Request) {
	pg := pgvector.NewDB()

	results := pg.Where(g.Map{"model": "demo"}).Query("人工智能技术").Limit(5).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "语义搜索完成",
		"data": g.Map{
			"action": "语义搜索",
			"query":  "人工智能技术",
			"count":  len(results),
			"items":  results,
		},
	})
}

// 🎯 手动Embedding搜索 - 按钮名称:"手动Embedding搜索"
func (c *Controller) CrudSearchManualEmbedding(r *ghttp.Request) {
	pg := pgvector.NewDB()

	// 模拟一个手动传入的embedding向量（1024维）
	// 在实际使用中，这个向量应该从用户请求参数中获取
	mockEmbedding := make([]float64, 1024)
	for i := 0; i < 1024; i++ {
		// 创建一个模拟的向量，这里使用简单的数学函数生成
		// 实际应用中应该是用户传入的真实embedding向量
		mockEmbedding[i] = 0.1*float64(i%10) - 0.5
	}

	// 使用手动设置的embedding进行搜索，而不是通过Query()自动生成
	results := pg.Where(g.Map{"model": "demo"}).Embedding(mockEmbedding).Limit(5).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "手动Embedding搜索完成",
		"data": g.Map{
			"action":           "手动Embedding搜索",
			"embedding_source": "用户手动传入",
			"embedding_dim":    1024,
			"embedding_sample": mockEmbedding[:5], // 显示前5个维度作为示例
			"count":            len(results),
			"items":            results,
			"note":             "此搜索使用预设的embedding向量，而不是通过文本自动生成",
		},
	})
}

// 🎪 Rerank高级查询 - 按钮名称:"Rerank高级查询"
func (c *Controller) CrudRerank(r *ghttp.Request) {
	pg := pgvector.NewDB()

	results := pg.Where(g.Map{"model": "demo"}).Query("这是一个关于人工智能的基础介绍文档").Limit(10).Rerank(0.1).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "Rerank高级查询完成",
		"data": g.Map{
			"action":       "Rerank查询",
			"query":        "深度学习神经网络",
			"rerank_score": 0.1,
			"count":        len(results),
			"items":        results,
		},
	})
}

// ✏️ 修改数据 - 按钮名称:"修改数据"
func (c *Controller) CrudUpdate(r *ghttp.Request) {
	pg := pgvector.NewDB()

	success := pg.Where(g.Map{"model": "demo", "aid": 1}).Data(g.Map{
		"content": "这是一个关于人工智能的更新文档，包含最新的AI发展趋势",
		"status":  "updated",
	}).Update()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "数据修改完成",
		"data": g.Map{
			"action": "更新AI文档",
			"result": success,
			"note":   "更新content和status字段",
		},
	})
}

// ✏️ 修改带分割数据 - 按钮名称:"修改带分割数据"
func (c *Controller) CrudUpdateSplit(r *ghttp.Request) {
	longContent := `
	<p>深度学习是机器学习的一个重要分支，它模仿人脑神经网络的工作原理。</p><br/>
	<p>通过多层神经网络，深度学习能够处理复杂的数据模式。在图像识别领域，深度学习取得了突破性进展。</p>

	<p>卷积神经网络（CNN）可以自动提取图像特征，实现高精度的图像分类和目标检测。</p><br/>
	<p>自然语言处理也是深度学习的重要应用领域。循环神经网络（RNN）和Transformer架构使得机器能够理解和生成人类语言。</p>

	<p>深度学习的训练需要大量的数据和计算资源。GPU的发展为深度学习的普及提供了强大的硬件支持。</p>
	<p>未来，深度学习将在更多领域发挥重要作用。</p>
	`

	pg := pgvector.NewDB()

	success := pg.SplitLength(150).Where(g.Map{"model": "demo", "aid": 2}).Data(g.Map{
		"content":     longContent,
		"content_raw": "深度学习详解（已更新）",
		"status":      "updated",
		"channel":     "tech-updated",
	}).Update()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "带分割数据修改完成",
		"data": g.Map{
			"action":       "更新带分割的长文档",
			"result":       success,
			"split_length": 150,
			"note":         "同时更新表2和表3，表3先清空再重新插入",
		},
	})
}

// 🗑️ 删除数据 - 按钮名称:"删除数据"
func (c *Controller) CrudDelete(r *ghttp.Request) {
	pg := pgvector.NewDB()

	success := pg.Where(g.Map{"model": "demo", "aid": 2}).Delete()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "数据删除完成",
		"data": g.Map{
			"action": "删除长文档",
			"result": success,
			"note":   "物理删除，同时清理三个表的相关数据",
		},
	})
}

// 📋 策略1:去重文档 - 按钮名称:"策略1:去重文档"
func (c *Controller) CrudQueryContent(r *ghttp.Request) {
	pg := pgvector.NewDB()

	// 策略1：基于表3 Embedding检索，GROUP去重返回表2文档（带rerank）
	results := pg.Where(g.Map{"model": "demo"}).Query("人工智能").Rerank(0.01).Limit(5).SelectSplit(true)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "查询策略1完成",
		"data": g.Map{
			"action":         "基于表3 Embedding检索，GROUP去重返回表2文档",
			"query":          "人工智能",
			"search_base":    "表3向量检索 + 表2条件过滤",
			"return_mode":    "去重文档，每个文档附带最相似段落",
			"rerank_enabled": true,
			"rerank_score":   0.01,
			"limit":          5,
			"count":          len(results),
			"items":          results,
			"note":           "使用 SelectSplit(true) + Rerank(0.1) - 返回文档列表，不重复，智能重排序",
		},
	})
}

// 📄 策略2:所有段落 - 按钮名称:"策略2:所有段落"
func (c *Controller) CrudSearchSplitDocs(r *ghttp.Request) {
	pg := pgvector.NewDB()

	// 策略2：基于表3 Embedding检索，返回所有匹配段落（带rerank）
	results := pg.Where(g.Map{"model": "demo"}).Query("人工智能").Rerank(0.01).Limit(10).SelectSplit(false)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "查询策略2完成",
		"data": g.Map{
			"action":         "基于表3 Embedding检索，返回所有匹配段落",
			"query":          "人工智能",
			"search_base":    "表3向量检索 + 表2条件过滤",
			"return_mode":    "返回所有匹配段落（可重复文档）",
			"rerank_enabled": true,
			"rerank_score":   0.01,
			"limit":          10,
			"count":          len(results),
			"items":          results,
			"note":           "使用 SelectSplit(false) + Rerank(0.1) - 返回所有段落，智能重排序",
		},
	})
}

// 🚫 已废弃方法 - CrudSearchSplitSegments (已被CrudSearchSplitDocs替代)
func (c *Controller) CrudSearchSplitSegments(r *ghttp.Request) {
	r.Response.WriteJsonExit(g.Map{
		"success": false,
		"message": "此方法已被CrudSearchSplitDocs替代",
	})
}

// 🔧 查看三表数据 - 按钮名称:"查看三表数据"
func (c *Controller) CrudDebugTables(r *ghttp.Request) {
	// 直接查询三个表的原始数据
	dbName := "vsearch"

	// 查询基础表
	baseResults, err1 := vant2.DB("pgvector_search", dbName).All()

	// 查询内容表
	contentResults, err2 := vant2.DB("pgvector_search_content", dbName).All()

	// 查询分割表
	splitResults, err3 := vant2.DB("pgvector_search_split", dbName).All()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "三表数据调试查询完成",
		"data": g.Map{
			"table1_base": g.Map{
				"table": "pgvector_search",
				"count": len(baseResults),
				"error": func() string {
					if err1 != nil {
						return err1.Error()
					}
					return ""
				}(),
				"items": baseResults,
			},
			"table2_content": g.Map{
				"table": "pgvector_search_content",
				"count": len(contentResults),
				"error": func() string {
					if err2 != nil {
						return err2.Error()
					}
					return ""
				}(),
				"items": contentResults,
			},
			"table3_split": g.Map{
				"table": "pgvector_search_split",
				"count": len(splitResults),
				"error": func() string {
					if err3 != nil {
						return err3.Error()
					}
					return ""
				}(),
				"items": splitResults,
			},
			"note": "直接查询三个表的原始数据，用于调试分割存储问题",
		},
	})
}
