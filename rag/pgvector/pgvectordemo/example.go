package pgvectordemo

import (
	"assistant/rag/pgvector"
	"vant2/tool/w"
)

// 以下是PGVector包的使用示例

// ExampleDelete 删除示例
func ExampleDelete() {
	// 1. 物理删除
	success := pgvector.NewDB().
		Where(w.Map{"user_id": 123}).
		Where("status", "inactive").
		Delete()

	if success {
		// 删除成功
	}

	// 2. 软删除
	success = pgvector.NewDB().
		Where(w.Map{"id": 456}).
		Remove()

	// 3. 根据ID删除
	success = pgvector.NewDB().
		DeleteById(789)

	// 4. 根据model和aid删除
	success = pgvector.NewDB().
		DeleteByModel("article", 123)
}

// ExampleQuery 查询示例
func ExampleQuery() {
	// 1. 基础语义搜索
	results := pgvector.NewDB().
		Where(w.Map{"user_id": 123}).
		Query("机器学习相关内容").
		Limit(10).
		List()

	// 2. 使用rerank的语义搜索
	results = pgvector.NewDB().
		Where(w.Map{"user_id": 123}).
		Query("深度学习算法").
		Limit(20).
		Rerank(0.5). // 设置rerank最小分数
		List()

	// 3. 使用预设embedding查询
	embedding := []float64{0.1, 0.2, 0.3} // 预设的向量
	results = pgvector.NewDB().
		Embedding(embedding).
		Distance(0.7). // 设置距离阈值
		Limit(15).
		List()

	// 4. 从分割表查询 - 更精确的搜索
	results = pgvector.NewDB().
		Query("人工智能").
		SelectSplit() // 直接从pgvector_search_split表查询

	// 5. 查询单条记录
	record := pgvector.NewDB().OneById(123)

	// 6. 获取相关内容
	relatedList := pgvector.NewDB().
		GetRelated(123, 10)

	// 7. 快捷搜索方法
	searchResults := pgvector.NewDB().
		SearchWithRerank("自然语言处理", 20)

	_ = results
	_ = record
	_ = relatedList
	_ = searchResults
}

// ExampleSave 保存示例
func ExampleSave() {
	// 1. 基础保存
	success := pgvector.NewDB().
		Data(w.Map{
			"content":     "这是一段关于机器学习的内容",
			"content_raw": "机器学习基础知识",
			"user_id":     123,
			"model":       "article",
			"aid":         456,
			"channel":     "tech",
			"status":      "active",
			"group_id":    1,
		}).
		Save()

	// 2. 带内容分割的保存
	success = pgvector.NewDB().
		SplitLength(500). // 500字符分割
		Data(w.Map{
			"content":     "这是一段很长的文章内容...", // 长内容会被自动分割
			"content_raw": "文章标题",
			"user_id":     123,
			"model":       "article",
			"aid":         789,
		}).
		Save()

	// 3. 使用SaveModel方法（自动判断插入或更新）
	success = pgvector.NewDB().
		Data(w.Map{
			"content": "更新的内容",
			"model":   "article",
			"aid":     456,
			"status":  "updated",
		}).
		SaveModel()

	// 4. 强制插入
	success = pgvector.NewDB().
		Data(w.Map{
			"content": "新插入的内容",
			"user_id": 123,
			"model":   "test_article", // 添加必需字段
			"aid":     2001,
		}).
		Insert()

	// 5. 强制更新
	success = pgvector.NewDB().
		Where(w.Map{"id": 123}).
		Data(w.Map{
			"status": "updated",
			"tags":   "machine-learning,ai",
		}).
		Update()

	// 6. 快速更新标志字段
	success = pgvector.NewDB().
		Where(w.Map{"id": 123}).
		SaveFlag(w.Map{
			"status": "published",
			"tags":   "tech,ai",
		})

	// 7. 根据model和aid更新标志
	success = pgvector.NewDB().
		SaveFlagByModel("article", 456, w.Map{
			"status": "archived",
		})

	// 8. 批量插入
	dataList := []w.Map{
		{
			"content": "内容1",
			"user_id": 123,
			"model":   "test_article", // 添加必需的model字段
			"aid":     1001,
		},
		{
			"content": "内容2",
			"user_id": 124,
			"model":   "test_article", // 添加必需的model字段
			"aid":     1002,
		},
	}
	success = pgvector.NewDB().
		BatchInsert(dataList)

	_ = success
}

// ExampleAdvanced 高级用法示例
func ExampleAdvanced() {
	// 1. 链式调用组合使用
	results := pgvector.NewDB().
		DBName("vsearch").
		VectorName("vector").
		ContentName("content").
		Where(w.Map{"user_id": 123}).
		Where("status", "active").
		Query("机器学习").
		Distance(0.8).
		Limit(20).
		Rerank(0.6).
		EmbeddingModel("BAAI/bge-m3").
		List()

	// 2. 分类查询
	techArticles := pgvector.NewDB().
		GetByChannel("tech", 10)

	userContent := pgvector.NewDB().
		GetByUser(123, 15)

	activeContent := pgvector.NewDB().
		GetByStatus("active", 20)

	// 3. 统计和检查
	count := pgvector.NewDB().
		Where(w.Map{"user_id": 123}).
		Count()

	exists := pgvector.NewDB().
		Where(w.Map{"model": "article", "aid": 456}).
		Exists()

	// 4. 复制实例
	baseInstance := pgvector.NewDB().
		EmbeddingModel("custom-model")

	// 使用复制实例创建新数据
	newInstance := baseInstance.CloneWithData(w.Map{
		"content": "新内容",
		"user_id": 123,
	})
	success := newInstance.Insert()

	_ = results
	_ = techArticles
	_ = userContent
	_ = activeContent
	_ = count
	_ = exists
	_ = success
}

// ExampleRealWorld 真实世界使用场景示例
func ExampleRealWorld() {
	// 场景1: 文章内容管理系统
	// 保存长文章并自动分割
	articleSaved := pgvector.NewDB().
		SplitLength(800).
		Data(w.Map{
			"content":     "这是一篇很长的技术文章...", // 长文章内容
			"content_raw": "Go语言并发编程详解",
			"user_id":     123,
			"model":       "article",
			"aid":         789,
			"channel":     "tech",
			"status":      "published",
			"group_id":    1,
			"tags":        "golang,concurrency,programming",
		}).
		SaveModel()

	// 场景2: 智能问答系统
	// 根据用户问题搜索相关内容
	question := "如何在Go中实现并发？"
	answers := pgvector.NewDB().
		Where(w.Map{"status": "published"}).
		Where(w.Map{"channel": "tech"}).
		Query(question).
		Distance(0.75). // 相似度阈值
		Limit(10).
		Rerank(0.6). // rerank最小分数
		List()

	// 场景3: 推荐系统
	// 根据用户阅读的文章推荐相关内容
	currentArticleId := int64(456)
	recommendations := pgvector.NewDB().
		Where(w.Map{"status": "published"}).
		Where(w.Map{"user_id !=": 123}). // 排除当前用户的内容
		GetRelated(currentArticleId, 5)

	// 场景4: 内容审核
	// 更新文章状态
	moderationResult := pgvector.NewDB().
		SaveFlagByModel("article", 789, w.Map{
			"status": "approved",
			"tags":   "golang,concurrency,programming,approved",
		})

	// 场景5: 用户内容查询
	// 查询用户发布的所有内容
	userContent := pgvector.NewDB().
		GetByUser(123, 20)

	// 场景6: 批量导入
	// 批量导入文档
	documents := []w.Map{
		{
			"content":     "Python基础教程内容...",
			"content_raw": "Python基础教程",
			"user_id":     123,
			"model":       "tutorial",
			"aid":         1001,
			"channel":     "education",
			"status":      "published",
		},
		{
			"content":     "JavaScript进阶指南内容...",
			"content_raw": "JavaScript进阶指南",
			"user_id":     124,
			"model":       "tutorial",
			"aid":         1002,
			"channel":     "education",
			"status":      "published",
		},
	}

	batchResult := pgvector.NewDB().
		SplitLength(600).
		BatchInsert(documents)

	// 场景7: 数据清理
	// 删除过期或无效内容
	cleanupResult := pgvector.NewDB().
		Where(w.Map{"status": "expired"}).
		Delete()

	// 清理软删除的数据
	cleanupDeleted := pgvector.NewDB().
		ClearDeleted()

	// 场景8: 分割表精确搜索
	// 对于长文档，使用分割表获得更精确的搜索结果
	preciseResults := pgvector.NewDB().
		Where(w.Map{"model": "long_article"}).
		Query("深度学习算法优化").
		SelectSplit() // 直接查询pgvector_search_split表

	_ = articleSaved
	_ = answers
	_ = recommendations
	_ = moderationResult
	_ = userContent
	_ = batchResult
	_ = cleanupResult
	_ = cleanupDeleted
	_ = preciseResults
}
