package pgvectordemo

import (
	"assistant/rag/pgvector"
	"fmt"
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// ChatController 聊天历史控制器
type ChatController struct{}

// TestIndex 聊天历史测试首页
func (c *ChatController) TestIndex(r *ghttp.Request) {
	html := `<!DOCTYPE html><html><head><meta charset="utf-8"><title>ChatHistory 聊天历史测试</title><style>body{font:14px Arial;margin:20px;background:#f0f8ff}h1{color:#2e8b57;text-align:center}button{background:#4682b4;color:#fff;border:0;padding:8px 15px;margin:3px;border-radius:4px;cursor:pointer;font-size:13px}button:hover{background:#36648b}.result{margin:15px 0;padding:15px;background:#ffffff;border-radius:5px;font-family:Consolas,monospace;white-space:pre-wrap;max-height:500px;overflow-y:auto;border:1px solid #ddd;box-shadow:0 2px 4px rgba(0,0,0,0.1)}.section{margin:15px 0;padding:10px;background:#f9f9f9;border-radius:5px;border-left:4px solid #4682b4}</style></head><body><h1>🤖 ChatHistory 聊天历史系统测试</h1>

<div class="section">
<h3>🔧 数据管理</h3>
<button onclick="test('/pgvector/chat/clean')">清空聊天数据</button>
<button onclick="test('/pgvector/chat/init')">初始化20条对话</button>
<button onclick="test('/pgvector/chat/debug')">查看原始数据</button>
</div>

<div class="section">
<h3>🔍 基础查询</h3>
<button onclick="test('/pgvector/chat/list-basic')">基础List查询</button>
<button onclick="test('/pgvector/chat/list-where')">条件Where查询</button>
<button onclick="test('/pgvector/chat/conversations')">最近对话组</button>
</div>

<div class="section">
<h3>🧠 智能检索</h3>
<button onclick="test('/pgvector/chat/query-simple')">Query相似性查询</button>
<button onclick="test('/pgvector/chat/query-embedding')">自定义Embedding查询</button>
<button onclick="test('/pgvector/chat/query-distance')">设置距离阈值查询</button>
</div>

<div class="section">
<h3>🎯 高级功能</h3>
<button onclick="test('/pgvector/chat/rerank')">Rerank重排序</button>
<button onclick="test('/pgvector/chat/fullchat')">FullChat完整对话</button>
<button onclick="test('/pgvector/chat/message-list')">MessageList上下文</button>
<button onclick="test('/pgvector/chat/complex-demo')">复杂查询演示</button>
</div>

<div class="section">
<h3>💾 CRUD操作</h3>
<button onclick="test('/pgvector/chat/save-user')">保存用户消息</button>
<button onclick="test('/pgvector/chat/save-assistant')">保存助手回复</button>
<button onclick="test('/pgvector/chat/update-message')">修改消息</button>
<button onclick="test('/pgvector/chat/delete-message')">删除消息</button>
</div>

<div id="result"></div>
<script>
async function test(url){
  const r=document.getElementById('result');
  r.innerHTML='<div class="result">⏳ 执行中...</div>';
  try{
    const res=await fetch(url);
    const data=await res.json();
    r.innerHTML='<div class="result">✅ 成功\\n\\n'+JSON.stringify(data,null,2)+'</div>';
  }catch(e){
    r.innerHTML='<div class="result">❌ 错误: '+e.message+'</div>';
  }
}
</script>
</body></html>`
	r.Response.Header().Set("Content-Type", "text/html; charset=utf-8")
	r.Response.Write(html)
}

// 🧹 清空聊天数据 - 按钮名称:"清空聊天数据"
func (c *ChatController) ChatClean(r *ghttp.Request) {
	// 软删除所有demo数据
	success := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id": 1001,
	}).Delete()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "聊天数据清理完成",
		"data": g.Map{
			"action": "软删除demo聊天数据",
			"result": success,
			"note":   "已删除user_id=1001的所有聊天记录",
		},
	})
}

// 🔧 初始化20条对话 - 按钮名称:"初始化20条对话"
func (c *ChatController) ChatInit(r *ghttp.Request) {
	// 先清空旧数据
	pgvector.ChatHistoryDB().Where(w.Map{"user_id": 1001}).Delete()

	// 准备20条对话数据（10轮对话）
	conversations := [][]w.Map{
		{
			{"role": "user", "content": "什么是人工智能？", "conversation_id": 1001},
			{"role": "assistant", "content": "人工智能（AI）是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。", "conversation_id": 1001},
		},
		{
			{"role": "user", "content": "深度学习和机器学习有什么区别？", "conversation_id": 1002},
			{"role": "assistant", "content": "机器学习是AI的子集，而深度学习是机器学习的子集。深度学习使用多层神经网络来学习数据表示。", "conversation_id": 1002},
		},
		{
			{"role": "user", "content": "如何开始学习Python编程？", "conversation_id": 1003},
			{"role": "assistant", "content": "建议从基础语法开始，然后练习编写小程序，逐步学习数据结构、函数和面向对象编程。", "conversation_id": 1003},
		},
		{
			{"role": "user", "content": "什么是神经网络？", "conversation_id": 1004},
			{"role": "assistant", "content": "神经网络是一种计算模型，由相互连接的节点（神经元）组成，模仿人脑的结构和功能。", "conversation_id": 1004},
		},
		{
			{"role": "user", "content": "自然语言处理有哪些应用？", "conversation_id": 1005},
			{"role": "assistant", "content": "NLP广泛应用于机器翻译、情感分析、聊天机器人、语音识别、文本摘要等领域。", "conversation_id": 1005},
		},
		{
			{"role": "user", "content": "数据库索引的作用是什么？", "conversation_id": 1006},
			{"role": "assistant", "content": "数据库索引能够大幅提高查询速度，通过创建数据结构来快速定位表中的特定行。", "conversation_id": 1006},
		},
		{
			{"role": "user", "content": "什么是向量数据库？", "conversation_id": 1007},
			{"role": "assistant", "content": "向量数据库专门存储和检索高维向量数据，支持相似性搜索，常用于AI和机器学习应用。", "conversation_id": 1007},
		},
		{
			{"role": "user", "content": "Go语言有什么特点？", "conversation_id": 1008},
			{"role": "assistant", "content": "Go语言具有简洁的语法、强大的并发支持、快速编译、垃圾回收和优秀的性能等特点。", "conversation_id": 1008},
		},
		{
			{"role": "user", "content": "什么是微服务架构？", "conversation_id": 1009},
			{"role": "assistant", "content": "微服务架构将应用拆分为多个独立的小服务，每个服务专注于特定业务功能，便于开发和维护。", "conversation_id": 1009},
		},
		{
			{"role": "user", "content": "如何优化数据库查询性能？", "conversation_id": 1010},
			{"role": "assistant", "content": "可以通过创建索引、优化SQL语句、分区表、使用缓存、避免全表扫描等方式优化性能。", "conversation_id": 1010},
		},
	}

	successCount := 0
	totalCount := 0

	// 插入对话数据
	for _, conversation := range conversations {
		for _, message := range conversation {
			totalCount++

			success := pgvector.ChatHistoryDB().SaveMessage(
				1001, // user_id
				100,  // channel_id
				gconv.Int64(message["conversation_id"]),
				gconv.String(message["role"]),
				pgvector.FormatContentForStorage(message["content"]), // 多模态兼容
				fmt.Sprintf(`{"model":"gpt-4","temperature":0.7,"timestamp":%d}`, time.Now().Unix()),
			)

			if success {
				successCount++
			}

			// 稍微延迟确保时间戳不同
			time.Sleep(10 * time.Millisecond)
		}
	}

	r.Response.WriteJsonExit(g.Map{
		"success": successCount == totalCount,
		"message": "聊天数据初始化完成",
		"data": g.Map{
			"action":        "初始化20条对话记录",
			"total_count":   totalCount,
			"success_count": successCount,
			"conversations": len(conversations),
			"user_id":       1001,
			"channel_id":    100,
			"note":          "已创建10轮完整对话，每轮包含user和assistant消息",
		},
	})
}

// 🔍 基础List查询 - 按钮名称:"基础List查询"
func (c *ChatController) ChatListBasic(r *ghttp.Request) {
	// 最近5条对话
	results := pgvector.ChatHistoryDB().List(5)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "基础List查询完成",
		"data": g.Map{
			"action": "获取最近5条对话",
			"count":  len(results),
			"items":  results,
			"note":   "ChatHistoryDB().List(5) - 按时间倒序获取最新对话",
		},
	})
}

// 🔍 条件Where查询 - 按钮名称:"条件Where查询"
func (c *ChatController) ChatListWhere(r *ghttp.Request) {
	// 指定用户和频道的最近3条对话
	results := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).List(3)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "条件Where查询完成",
		"data": g.Map{
			"action":     "查询指定用户频道的对话",
			"user_id":    1001,
			"channel_id": 100,
			"count":      len(results),
			"items":      results,
			"note":       "ChatHistoryDB().Where({...}).List(3)",
		},
	})
}

// 🧠 Query相似性查询 - 按钮名称:"Query相似性查询"
func (c *ChatController) ChatQuerySimple(r *ghttp.Request) {
	// 最近3条 + 相似2条
	results := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).Query("人工智能和机器学习", 2).List(3)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "Query相似性查询完成",
		"data": g.Map{
			"action":        "最新对话 + 相似对话",
			"query_text":    "人工智能和机器学习",
			"recent_limit":  3,
			"similar_limit": 2,
			"total_count":   len(results),
			"items":         results,
			"note":          "ChatHistoryDB().Where({...}).Query(\"人工智能和机器学习\", 2).List(3)",
		},
	})
}

// 🎯 Rerank重排序 - 按钮名称:"Rerank重排序"
func (c *ChatController) ChatRerank(r *ghttp.Request) {
	// 相似性查询 + 重排序
	results := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).Query("编程和数据库", 3).Rerank(0.1).List(2)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "Rerank重排序完成",
		"data": g.Map{
			"action":       "相似性查询 + 智能重排序",
			"query_text":   "编程和数据库",
			"rerank_score": 0.1,
			"count":        len(results),
			"items":        results,
			"note":         "ChatHistoryDB().Query(\"编程和数据库\", 3).Rerank(0.1).List(2)",
		},
	})
}

// 🎯 FullChat完整对话 - 按钮名称:"FullChat完整对话"
func (c *ChatController) ChatFullChat(r *ghttp.Request) {
	// 相似性查询 + 完整对话扩展
	results := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).Query("神经网络", 2).FullChat().List(2)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "FullChat完整对话完成",
		"data": g.Map{
			"action": "自动扩展user-assistant配对",
			"query":  "神经网络",
			"count":  len(results),
			"items":  results,
			"note":   "ChatHistoryDB().Query(\"神经网络\", 2).FullChat().List(2) - 确保user-assistant完整配对",
		},
	})
}

// 🎯 MessageList上下文 - 按钮名称:"MessageList上下文"
func (c *ChatController) ChatMessageList(r *ghttp.Request) {
	// 生成大模型上下文格式 - 一步干到底的连贯操作
	messages := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).Query("人工智能", 2).FullChat().ListAndMessages(5)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "MessageList上下文生成完成",
		"data": g.Map{
			"action":        "生成大模型标准上下文格式",
			"query":         "人工智能",
			"message_count": len(messages),
			"messages":      messages,
			"note":          "ChatHistoryDB().Query().FullChat().List().MessageList() - 输出标准Message格式",
		},
	})
}

// 🎪 复杂查询演示 - 按钮名称:"复杂查询演示"
func (c *ChatController) ChatComplexDemo(r *ghttp.Request) {
	// 演示完整的链式调用 - 一步干到底
	messages := pgvector.ChatHistoryDB().
		Where(w.Map{"user_id": 1001, "channel_id": 100}).
		Query("数据库和性能优化", 3).
		Rerank(0.05).
		FullChat().
		Distance(0.8).
		ListAndMessages(2)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "复杂查询演示完成",
		"data": g.Map{
			"action":        "完整链式调用演示",
			"query":         "数据库和性能优化",
			"rerank_score":  0.05,
			"distance":      0.8,
			"recent_limit":  2,
			"similar_limit": 3,
			"message_count": len(messages),
			"messages":      messages,
			"chain_call":    "Where().Query().Rerank().FullChat().Distance().List().MessageList()",
			"note":          "演示所有功能的组合使用",
		},
	})
}

// 💾 保存用户消息 - 按钮名称:"保存用户消息"
func (c *ChatController) ChatSaveUser(r *ghttp.Request) {
	// 保存新的用户消息
	success := pgvector.ChatHistoryDB().SaveMessage(
		1001, // user_id
		200,  // channel_id (新频道)
		2001, // conversation_id
		"user",
		"请解释一下什么是区块链技术？",
		`{"model":"gpt-4","source":"web","session_id":"test_session"}`,
	)

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "用户消息保存完成",
		"data": g.Map{
			"action":          "保存用户问题",
			"user_id":         1001,
			"channel_id":      200,
			"conversation_id": 2001,
			"role":            "user",
			"content":         "请解释一下什么是区块链技术？",
			"result":          success,
			"note":            "ChatHistoryDB().SaveMessage() - 快速保存方法",
		},
	})
}

// 💾 保存助手回复 - 按钮名称:"保存助手回复"
func (c *ChatController) ChatSaveAssistant(r *ghttp.Request) {
	// 保存助手回复
	success := pgvector.ChatHistoryDB().SaveMessage(
		1001, // user_id
		200,  // channel_id
		2001, // conversation_id (相同对话组)
		"assistant",
		"区块链是一种分布式账本技术，通过加密链接的区块链式存储数据，具有去中心化、不可篡改的特点。",
		`{"model":"gpt-4","tokens":45,"response_time":1.2}`,
	)

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "助手回复保存完成",
		"data": g.Map{
			"action":          "保存助手回答",
			"user_id":         1001,
			"channel_id":      200,
			"conversation_id": 2001,
			"role":            "assistant",
			"content":         "区块链是一种分布式账本技术...",
			"result":          success,
			"note":            "与用户消息形成完整对话对",
		},
	})
}

// ✏️ 修改消息 - 按钮名称:"修改消息"
func (c *ChatController) ChatUpdateMessage(r *ghttp.Request) {
	// 先查找要修改的消息ID
	dbName := "vsearch"
	records, err := vant2.DB("pgvector_chat_history", dbName).Where(w.Map{
		"user_id":    1001,
		"channel_id": 200,
		"role":       "assistant",
		"is_delete":  0,
	}).Limit(1).All()

	success := false
	if err == nil && len(records) > 0 {
		messageId := gconv.Int64(records[0]["id"])
		// 使用Save方法更新（包含ID则为更新）
		success = pgvector.ChatHistoryDB().Data(w.Map{
			"id":      messageId,
			"content": "区块链是一种革新性的分布式账本技术，它通过密码学方法将数据区块按时间顺序连接，形成不可篡改的链式结构，实现去中心化的信任机制。",
			"extra":   `{"model":"gpt-4","tokens":65,"updated":true}`,
		}).Save()
	}

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "消息修改完成",
		"data": g.Map{
			"action": "更新助手回复内容",
			"result": success,
			"note":   "ChatHistoryDB().Where().Data().Update() - 修改已存在的消息",
		},
	})
}

// 🗑️ 删除消息 - 按钮名称:"删除消息"
func (c *ChatController) ChatDeleteMessage(r *ghttp.Request) {
	// 软删除指定对话组的消息
	success := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":         1001,
		"conversation_id": 2001,
	}).Delete()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "消息删除完成",
		"data": g.Map{
			"action":          "软删除对话组",
			"user_id":         1001,
			"conversation_id": 2001,
			"result":          success,
			"note":            "ChatHistoryDB().Where().Delete() - 软删除，数据仍保留",
		},
	})
}

// 📋 查看原始数据 - 按钮名称:"查看原始数据"
func (c *ChatController) ChatDebug(r *ghttp.Request) {
	// 直接查询原始表数据
	dbName := "vsearch"
	results, err := vant2.DB("pgvector_chat_history", dbName).Where("user_id", 1001).Limit(10).All()

	r.Response.WriteJsonExit(g.Map{
		"success": err == nil,
		"message": "原始数据查询完成",
		"data": g.Map{
			"action":  "查看pgvector_chat_history表数据",
			"table":   "pgvector_chat_history",
			"user_id": 1001,
			"count":   len(results),
			"items":   results,
			"error": func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			"note": "直接查询数据库表，用于调试",
		},
	})
}

// 📊 最近对话组 - 按钮名称:"最近对话组"
func (c *ChatController) ChatConversations(r *ghttp.Request) {
	// 获取最近的对话组
	conversations := pgvector.ChatHistoryDB().GetRecentConversations(1001, 100, 5)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "最近对话组查询完成",
		"data": g.Map{
			"action":        "获取用户最近对话组",
			"user_id":       1001,
			"channel_id":    100,
			"count":         len(conversations),
			"conversations": conversations,
			"note":          "ChatHistoryDB().GetRecentConversations() - 按对话组聚合",
		},
	})
}

// 🎯 自定义Embedding查询 - 按钮名称:"自定义Embedding查询"
func (c *ChatController) ChatQueryEmbedding(r *ghttp.Request) {
	// 创建模拟的embedding向量
	mockEmbedding := make([]float64, 1024)
	for i := 0; i < 1024; i++ {
		mockEmbedding[i] = 0.1*float64(i%20) - 1.0
	}

	// 使用自定义embedding查询
	results := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).Embedding(mockEmbedding).List(3)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "自定义Embedding查询完成",
		"data": g.Map{
			"action":           "使用预设embedding向量查询",
			"embedding_dim":    1024,
			"embedding_sample": mockEmbedding[:5],
			"count":            len(results),
			"items":            results,
			"note":             "ChatHistoryDB().Embedding(vector).List() - 跳过文本embedding生成",
		},
	})
}

// 📏 设置距离阈值查询 - 按钮名称:"设置距离阈值查询"
func (c *ChatController) ChatQueryDistance(r *ghttp.Request) {
	// 设置距离阈值进行查询
	results := pgvector.ChatHistoryDB().Where(w.Map{
		"user_id":    1001,
		"channel_id": 100,
	}).Query("编程语言", 3).Distance(0.6).List(2)

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "距离阈值查询完成",
		"data": g.Map{
			"action":   "设置相似度距离阈值",
			"query":    "编程语言",
			"distance": 0.6,
			"count":    len(results),
			"items":    results,
			"note":     "ChatHistoryDB().Query().Distance(0.6).List() - 过滤距离大于0.6的结果",
		},
	})
}
