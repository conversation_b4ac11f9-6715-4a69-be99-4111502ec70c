package pgvectordemo

import (
	"assistant/rag/pgvector"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// QAController QA问答库专用控制器
type QAController struct{}

// ======================================
// QA问答库 专用测试方法
// ======================================

// 🧹 清理QA数据 - 按钮名称:"清理QA数据"
func (c *QAController) QACrudClean(r *ghttp.Request) {
	// 执行清理
	qa := pgvector.NewQA()
	success := qa.Where(g.Map{"model": "faq"}).Delete()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "QA数据清理完成",
		"data": g.Map{
			"action": "清理faq数据",
			"note":   "已删除所有model=faq的QA测试数据",
		},
	})
}

// ➕ 新增QA - 按钮名称:"新增QA"
func (c *QAController) QACrudCreate(r *ghttp.Request) {
	qa := pgvector.NewQA()

	success := qa.Data(g.Map{
		"q":       "什么是人工智能？",
		"a":       "人工智能（AI）是指由人造机器所表现出来的智能。通过计算机程序模拟人类的思维过程和智能行为，如学习、推理、感知、理解、交流等。",
		"remark":  "AI基础概念问答",
		"title":   "人工智能定义",
		"extra":   `{"category":"AI基础","difficulty":"初级"}`,
		"user_id": 1001,
		"model":   "faq",
		"aid":     1,
		"channel": "tech",
		"status":  "active",
	}).SaveQA()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "QA新增完成",
		"data": g.Map{
			"action": "新增AI基础QA",
			"result": success,
			"note":   "保存到pgvector_qa表，自动生成q|a合并embedding",
		},
	})
}

// ➕ 批量新增QA - 按钮名称:"批量新增QA"
func (c *QAController) QACrudCreateBatch(r *ghttp.Request) {
	qa := pgvector.NewQA()

	qaList := []g.Map{
		{
			"q":       "什么是机器学习？",
			"a":       "机器学习是人工智能的一个重要分支，它使计算机能够通过经验自动改进性能，而不需要明确编程。主要包括监督学习、无监督学习和强化学习。",
			"remark":  "机器学习基础概念",
			"title":   "机器学习定义",
			"model":   "faq",
			"aid":     2,
			"user_id": 1001,
			"channel": "tech",
		},
		{
			"q":       "深度学习和机器学习有什么区别？",
			"a":       "深度学习是机器学习的一个子集，使用多层神经网络来模拟人脑处理信息的方式。相比传统机器学习，深度学习能够自动提取特征，处理更复杂的数据模式。",
			"remark":  "深度学习与机器学习的关系",
			"title":   "深度学习vs机器学习",
			"model":   "faq",
			"aid":     3,
			"user_id": 1001,
			"channel": "tech",
		},
		{
			"q":       "什么是自然语言处理（NLP）？",
			"a":       "自然语言处理是计算机科学、人工智能和语言学的交叉领域，目标是让计算机能够理解、处理和生成人类语言。包括文本分析、情感分析、机器翻译、语音识别等应用。",
			"remark":  "NLP领域介绍",
			"title":   "自然语言处理定义",
			"model":   "faq",
			"aid":     4,
			"user_id": 1001,
			"channel": "tech",
		},
	}

	successCount := 0
	for _, qaData := range qaList {
		if qa.Data(qaData).SaveQA() {
			successCount++
		}
	}

	r.Response.WriteJsonExit(g.Map{
		"success": successCount > 0,
		"message": "批量QA新增完成",
		"data": g.Map{
			"action":        "批量新增QA",
			"total":         len(qaList),
			"success_count": successCount,
			"result":        successCount > 0,
			"note":          "批量保存多个QA到pgvector_qa表",
		},
	})
}

// 🔍 查询QA - 按钮名称:"查询QA"
func (c *QAController) QACrudQuery(r *ghttp.Request) {
	qa := pgvector.NewQA()

	results := qa.Where(g.Map{"model": "faq"}).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "QA查询完成",
		"data": g.Map{
			"action": "查询faq QA数据",
			"count":  len(results),
			"items":  results,
		},
	})
}

// 🧠 语义搜索QA - 按钮名称:"语义搜索QA"
func (c *QAController) QACrudSearch(r *ghttp.Request) {
	qa := pgvector.NewQA()

	results := qa.Where(g.Map{"model": "faq"}).Query("机器学习和深度学习").Limit(5).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "QA语义搜索完成",
		"data": g.Map{
			"action": "语义搜索QA",
			"query":  "机器学习和深度学习",
			"count":  len(results),
			"items":  results,
		},
	})
}

// 🎪 QA Rerank高级搜索 - 按钮名称:"QA Rerank高级搜索"
func (c *QAController) QACrudRerank(r *ghttp.Request) {
	qa := pgvector.NewQA()

	results := qa.Where(g.Map{"model": "faq"}).Query("人工智能技术").Limit(10).Rerank(0.1).List()

	r.Response.WriteJsonExit(g.Map{
		"success": true,
		"message": "QA Rerank搜索完成",
		"data": g.Map{
			"action":       "QA Rerank搜索",
			"query":        "人工智能技术",
			"rerank_score": 0.1,
			"count":        len(results),
			"items":        results,
		},
	})
}

// ✏️ 更新QA - 按钮名称:"更新QA"
func (c *QAController) QACrudUpdate(r *ghttp.Request) {
	qa := pgvector.NewQA()

	success := qa.Where(g.Map{"model": "faq", "aid": 1}).Data(g.Map{
		"a":      "人工智能（AI）是指由人造机器所表现出来的智能。它是计算机科学的一个分支，致力于创建能够模拟、扩展和辅助人类智能的系统。现代AI包括机器学习、深度学习、自然语言处理等多个领域。",
		"remark": "AI基础概念问答（已更新）",
		"status": "updated",
	}).Update()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "QA更新完成",
		"data": g.Map{
			"action": "更新AI基础QA",
			"result": success,
			"note":   "更新答案和备注字段，自动重新生成embedding",
		},
	})
}

// 🗑️ 删除QA - 按钮名称:"删除QA"
func (c *QAController) QACrudDelete(r *ghttp.Request) {
	qa := pgvector.NewQA()

	success := qa.Where(g.Map{"model": "faq", "aid": 4}).Delete()

	r.Response.WriteJsonExit(g.Map{
		"success": success,
		"message": "QA删除完成",
		"data": g.Map{
			"action": "删除NLP QA",
			"result": success,
			"note":   "物理删除，清理pgvector_qa表数据",
		},
	})
}
