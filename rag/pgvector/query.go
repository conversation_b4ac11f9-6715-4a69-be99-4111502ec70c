package pgvector

import (
	"assistant/rag/embedding"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// List 查询列表 - 支持向量相似度搜索和rerank
func (pg *PGVector) List() w.SliceMap {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector List 验证失败:", err)
		return nil
	}

	tables := pg._getTableNames()

	// 如果没有查询文本和embedding，直接执行普通查询
	if pg.queryText == "" && pg.embedding == nil {
		return pg._normalQuery(tables.Base)
	}

	// 执行向量相似度查询
	return pg._vectorQuery(tables.Base)
}

// SelectSplit 从分割表中查询数据
// uniqueDoc 参数：true=策略1返回去重文档，false=策略2返回所有段落
func (pg *PGVector) SelectSplit(uniqueDoc ...bool) w.SliceMap {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector SelectSplit 验证失败:", err)
		return nil
	}

	// 确定查询策略
	isUniqueDoc := false
	if len(uniqueDoc) > 0 {
		isUniqueDoc = uniqueDoc[0]
	}

	// 必须有查询条件（向量检索或其他条件）
	if pg.queryText == "" && pg.embedding == nil && len(pg.whereConditions) == 0 {
		vant2.Error("PGVector SelectSplit 需要查询条件")
		return nil
	}

	// 根据策略执行不同的查询
	if isUniqueDoc {
		return pg._splitQueryUniqueDoc() // 策略1：返回去重文档
	} else {
		return pg._splitQueryAllSegments() // 策略2：返回所有段落
	}
}

// One 查询单条记录
func (pg *PGVector) One() w.Map {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector One 验证失败:", err)
		return nil
	}

	if len(pg.whereConditions) == 0 {
		vant2.Error("PGVector One 必须提供where条件")
		return nil
	}

	tables := pg._getTableNames()
	db := pg._buildWhereDB(tables.Base)

	// 添加软删除过滤
	db = db.Where("is_delete", 0)

	record := db.One()
	return gconv.Map(record)
}

// OneById 根据ID查询单条记录
func (pg *PGVector) OneById(id int64) w.Map {
	return pg.Where(w.Map{"id": id}).One()
}

// OneByModel 根据model和aid查询单条记录
func (pg *PGVector) OneByModel(model string, aid int64) w.Map {
	return pg.Where(w.Map{
		"model": model,
		"aid":   aid,
	}).One()
}

// Count 统计记录数量
func (pg *PGVector) Count() int {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector Count 验证失败:", err)
		return 0
	}

	tables := pg._getTableNames()
	db := pg._buildWhereDB(tables.Base)

	// 添加软删除过滤
	db = db.Where("is_delete", 0)

	return db.Count()
}

// Exists 检查记录是否存在
func (pg *PGVector) Exists() bool {
	return pg.Count() > 0
}

// _normalQuery 普通查询（不使用向量相似度）
func (pg *PGVector) _normalQuery(tableName string) w.SliceMap {
	db := pg._buildWhereDB(tableName)

	// 添加软删除过滤
	db = db.Where("is_delete", 0)

	// 设置查询字段
	fields := pg._getQueryFields()
	if fields != "" {
		db = db.Fields(fields)
	}

	// 设置限制
	if pg.limitCount > 0 {
		db.Limit(pg.limitCount)
	}

	// 默认按创建时间倒序
	db = db.Order("time_create DESC")

	result, _ := db.All()
	return gconv.SliceMap(result)
}

// _vectorQuery 向量相似度查询
func (pg *PGVector) _vectorQuery(tableName string) w.SliceMap {
	// 获取embedding向量
	embedding := pg._getEmbedding()
	if embedding == nil {
		vant2.Error("PGVector _vectorQuery 无法获取embedding向量")
		return nil
	}

	db := pg._buildWhereDB(tableName)

	// 添加软删除过滤
	db = db.Where("is_delete", 0)

	// 设置查询字段和向量距离计算
	fields := pg._getQueryFields()
	vectorField := db.VectorFields(&w.VectorAttr{
		FieldName: pg.vectorName,
		Embedding: embedding,
	})

	if fields != "" {
		db = db.Fields(fields + "," + vectorField)
	} else {
		db = db.Fields("*," + vectorField)
	}

	// 设置限制 - 如果使用rerank，则查询更多数据
	limit := pg.limitCount
	if pg.useRerank && limit > 0 {
		limit = limit * 2 // rerank需要更多候选数据
	}
	if limit > 0 {
		db.Limit(limit)
	}

	// 按向量距离排序
	db = db.Order("distance ASC")

	result, _ := db.All()
	list := gconv.SliceMap(result)

	// 过滤距离阈值
	if pg.distance > 0 {
		filteredList := w.SliceMap{}
		for _, item := range list {
			distance := gconv.Float64(item["distance"])
			if distance < pg.distance {
				filteredList = append(filteredList, item)
			}
		}
		list = filteredList
	}

	// 处理rerank重排序
	if pg.useRerank {
		list = pg._processRerank(list)

		// 如果rerank后数量超过原始限制，进行截取
		if pg.limitCount > 0 && len(list) > pg.limitCount {
			list = list[:pg.limitCount]
		}
	}

	return list
}

// _getQueryFields 获取查询字段
func (pg *PGVector) _getQueryFields() string {
	// 基础字段（始终存在）
	baseFields := "id,content,content_raw,user_id,model,aid,channel,status,group_id,time_create"

	// 检查新字段是否存在（title和extra）
	// 如果数据库已更新，则包含新字段；否则只返回基础字段
	tables := pg._getTableNames()
	tableName := tables.Base

	// 尝试查询一条记录检测字段是否存在
	testDB := vant2.DB(tableName, pg.dbName).Limit(1)

	// 先尝试查询包含新字段的SQL，如果失败则只用基础字段
	_, err := testDB.Fields("title,extra").All()
	if err == nil {
		// 新字段存在，返回完整字段列表
		return baseFields + ",title,extra"
	}

	// 新字段不存在，只返回基础字段
	vant2.Primary("PGVector: title和extra字段不存在，请运行 migrate_add_title_extra.sql 迁移脚本")
	return baseFields
}

// GetRelated 获取相关内容（基于向量相似度）
func (pg *PGVector) GetRelated(referenceId int64, limit ...int) w.SliceMap {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector GetRelated 验证失败:", err)
		return nil
	}

	// 先获取参考记录的向量
	reference := pg.OneById(referenceId)
	if reference == nil {
		vant2.Error("PGVector GetRelated 参考记录不存在:", referenceId)
		return nil
	}

	// 提取向量数据
	vector := reference[pg.vectorName]
	if vector == nil {
		vant2.Error("PGVector GetRelated 参考记录没有向量数据:", referenceId)
		return nil
	}

	// 设置查询条件
	pg.embedding = vector
	pg.whereConditions = append(pg.whereConditions, w.Map{
		"id !=": referenceId, // 排除自己
	})

	// 设置限制
	if len(limit) > 0 {
		pg.limitCount = limit[0]
	} else {
		pg.limitCount = 10 // 默认10条相关内容
	}

	return pg.List()
}

// Search 语义搜索
func (pg *PGVector) Search(query string, limit ...int) w.SliceMap {
	pg.queryText = query

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// SearchWithRerank 语义搜索并使用rerank重排序
func (pg *PGVector) SearchWithRerank(query string, limit ...int) w.SliceMap {
	pg.queryText = query
	pg.useRerank = true

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// Fields 设置查询字段
func (pg *PGVector) Fields(fields string) *PGVector {
	// 这里可以扩展字段设置功能
	// 当前使用默认字段，后续可以根据需要扩展
	return pg
}

// Order 排序（向量查询时会被覆盖为按距离排序）
func (pg *PGVector) Order(orderBy string) *PGVector {
	// 向量查询时排序会被距离排序覆盖
	// 这里预留接口，普通查询时可以使用
	return pg
}

// GetByChannel 根据频道查询
func (pg *PGVector) GetByChannel(channel string, limit ...int) w.SliceMap {
	pg.whereConditions = append(pg.whereConditions, w.Map{"channel": channel})

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// GetByGroup 根据分组查询
func (pg *PGVector) GetByGroup(groupId int64, limit ...int) w.SliceMap {
	pg.whereConditions = append(pg.whereConditions, w.Map{"group_id": groupId})

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// GetByUser 根据用户查询
func (pg *PGVector) GetByUser(userId int64, limit ...int) w.SliceMap {
	pg.whereConditions = append(pg.whereConditions, w.Map{"user_id": userId})

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// GetByStatus 根据状态查询
func (pg *PGVector) GetByStatus(status string, limit ...int) w.SliceMap {
	pg.whereConditions = append(pg.whereConditions, w.Map{"status": status})

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// GetByTags 根据标签查询
func (pg *PGVector) GetByTags(tags string, limit ...int) w.SliceMap {
	// 使用LIKE查询标签
	pg.whereConditions = append(pg.whereConditions, w.Map{
		"tags LIKE": "%" + tags + "%",
	})

	if len(limit) > 0 {
		pg.limitCount = limit[0]
	}

	return pg.List()
}

// _getEmbedding 获取embedding向量
func (pg *PGVector) _getEmbedding() interface{} {
	// 如果已经有预设的embedding，直接返回
	if pg.embedding != nil {
		return pg.embedding
	}

	// 如果有查询文本，调用embedding API
	if pg.queryText != "" {
		// 限制文本长度
		text := pg.queryText
		if len(text) > 8000 {
			text = text[:8000]
		}

		// 使用现有的embedding包
		vector, err := embedding.EmbeddingText(text)
		if err != nil {
			vant2.Error("PGVector _getEmbedding 调用embedding失败:", err)
			return nil
		}
		return vector
	}

	return nil
}

// _processRerank 处理rerank重排序
func (pg *PGVector) _processRerank(list w.SliceMap) w.SliceMap {
	if !pg.useRerank || len(list) == 0 || pg.queryText == "" {
		return list
	}

	// 转换为gdb.Result格式（embedding包支持直接处理）
	result := gconv.SliceAny(list)

	// 使用现有的embedding包进行rerank
	rerankOptions := &embedding.RerankOptions{
		Query:     pg.queryText,
		Documents: result,
		MinScore:  pg.rerankScore,
	}

	// 如果设置了限制，添加到rerank选项
	if pg.limitCount > 0 {
		rerankOptions.MaxResults = pg.limitCount
	}

	// 调用rerank
	rerankedResults, err := embedding.Rerank(rerankOptions)
	if err != nil {
		vant2.Error("PGVector _processRerank 调用rerank失败:", err)
		return list
	}

	return gconv.SliceMap(rerankedResults)
}

// _splitQueryUniqueDoc 策略1：表3索引检索，返回去重的表2文档
func (pg *PGVector) _splitQueryUniqueDoc() w.SliceMap {
	// 获取embedding向量
	embedding := pg._getEmbedding()
	if embedding == nil {
		vant2.Error("PGVector _splitQueryUniqueDoc 无法获取embedding向量")
		return nil
	}

	tables := pg._getTableNames()

	// 第一步：在表3中进行向量检索，获取相似的model+aid（去重）
	splitDB := vant2.DB(tables.Split, pg.dbName)

	// 计算向量距离
	vectorField := splitDB.VectorFields(&w.VectorAttr{
		FieldName: "vector",
		Embedding: embedding,
	})

	// 查询表3，获取所有匹配片段并按距离排序
	splitDB = splitDB.Fields("model, aid, content, " + vectorField)

	// 距离过滤
	distanceExpr := strings.Replace(vectorField, " AS distance", "", 1)
	if pg.distance > 0 {
		splitDB = splitDB.Where("("+distanceExpr+") < ?", pg.distance)
	}

	// 按距离排序，获取所有匹配的片段
	splitDB = splitDB.Order("distance ASC")

	// 查询更多结果用于后续过滤
	limit := pg.limitCount
	if limit == 0 {
		limit = 100 // 默认限制
	}
	splitDB.Limit(limit * 10) // 查询更多用于去重

	allSplitResults, _ := splitDB.All()
	allSplitList := gconv.SliceMap(allSplitResults)
	if len(allSplitList) == 0 {
		return w.SliceMap{}
	}

	// 手动去重：每个文档只保留距离最小的片段
	documentMap := make(map[string]w.Map)
	for _, item := range allSplitList {
		model := gconv.String(item["model"])
		aid := gconv.String(item["aid"])
		key := model + "_" + aid

		// 如果这个文档还没有记录，或者当前距离更小，则更新
		if existing, exists := documentMap[key]; !exists {
			documentMap[key] = item
		} else {
			currentDistance := gconv.Float64(item["distance"])
			existingDistance := gconv.Float64(existing["distance"])
			if currentDistance < existingDistance {
				documentMap[key] = item
			}
		}
	}

	// 转换为w.SliceMap并按距离排序
	var splitResult w.SliceMap
	for _, doc := range documentMap {
		splitResult = append(splitResult, doc)
	}

	// 按距离排序
	for i := 0; i < len(splitResult)-1; i++ {
		for j := i + 1; j < len(splitResult); j++ {
			dist1 := gconv.Float64(splitResult[i]["distance"])
			dist2 := gconv.Float64(splitResult[j]["distance"])
			if dist1 > dist2 {
				splitResult[i], splitResult[j] = splitResult[j], splitResult[i]
			}
		}
	}

	// 限制结果数量
	if pg.limitCount > 0 && len(splitResult) > pg.limitCount {
		splitResult = splitResult[:pg.limitCount]
	}

	// 第二步：根据表3筛选出的model+aid，查询表2获取文档信息
	contentDB := vant2.DB(tables.Content, pg.dbName)

	// 应用表2的where条件
	for _, condition := range pg.whereConditions {
		contentDB = contentDB.Where(condition)
	}

	// 添加软删除过滤
	contentDB = contentDB.Where("is_delete", 0)

	// 构建IN条件
	var modelAidConditions []string
	splitMap := make(map[string]w.Map) // 保存最相似段落信息

	for _, item := range splitResult {
		model := gconv.String(item["model"])
		aid := gconv.String(item["aid"])
		key := model + "_" + aid
		splitMap[key] = gconv.Map(item)
		modelAidConditions = append(modelAidConditions, "(model='"+model+"' AND aid="+aid+")")
	}

	if len(modelAidConditions) > 0 {
		contentDB = contentDB.Where("(" + strings.Join(modelAidConditions, " OR ") + ")")
	}

	// 查询表2的业务信息（排除content_raw）
	contentDB = contentDB.Fields("model, aid, channel, status, group_id, tags, user_id, time_create, is_delete")
	contentResults, _ := contentDB.All()

	// 第三步：合并表2业务信息和表3最相似段落
	var finalResults w.SliceMap
	for _, contentItem := range contentResults {
		model := gconv.String(contentItem["model"])
		aid := gconv.String(contentItem["aid"])
		key := model + "_" + aid

		if splitInfo, exists := splitMap[key]; exists {
			// 合并：表2业务字段 + 表3最相似段落content + distance
			merged := w.Map{
				"model":       contentItem["model"],
				"aid":         contentItem["aid"],
				"content":     splitInfo["content"],  // 表3最相似段落
				"distance":    splitInfo["distance"], // 相似度分数
				"channel":     contentItem["channel"],
				"status":      contentItem["status"],
				"group_id":    contentItem["group_id"],
				"tags":        contentItem["tags"],
				"user_id":     contentItem["user_id"],
				"time_create": contentItem["time_create"],
			}
			finalResults = append(finalResults, merged)
		}
	}

	list := finalResults

	// 处理rerank重排序
	if pg.useRerank {
		list = pg._processRerank(list)
		if pg.limitCount > 0 && len(list) > pg.limitCount {
			list = list[:pg.limitCount]
		}
	}

	return list
}

// _splitQueryAllSegments 策略2：表3索引检索，返回所有匹配段落
func (pg *PGVector) _splitQueryAllSegments() w.SliceMap {
	// 获取embedding向量
	embedding := pg._getEmbedding()
	if embedding == nil {
		vant2.Error("PGVector _splitQueryAllSegments 无法获取embedding向量")
		return nil
	}

	tables := pg._getTableNames()

	// 第一步：在表3中进行向量检索，获取所有相似的model+aid+split_index
	splitDB := vant2.DB(tables.Split, pg.dbName)

	// 计算向量距离
	vectorField := splitDB.VectorFields(&w.VectorAttr{
		FieldName: "vector",
		Embedding: embedding,
	})

	// 查询表3，获取所有匹配的分割片段
	splitDB = splitDB.Fields("model, aid, content, split_index, " + vectorField)

	// 距离过滤
	if pg.distance > 0 {
		splitDB = splitDB.Where("("+vectorField+") < ?", pg.distance)
	}

	// 按距离排序
	splitDB = splitDB.Order("distance ASC")

	// 设置限制
	if pg.limitCount > 0 {
		limit := pg.limitCount
		if pg.useRerank {
			limit = limit * 2
		}
		splitDB.Limit(limit)
	}

	// 获取表3的检索结果
	splitResults, _ := splitDB.All()
	if len(splitResults) == 0 {
		return w.SliceMap{}
	}

	// 第二步：获取所有涉及到的model+aid，查询表2获取业务信息
	modelAidMap := make(map[string]w.Map)
	var modelAidConditions []string

	for _, item := range splitResults {
		model := gconv.String(item["model"])
		aid := gconv.String(item["aid"])
		key := model + "_" + aid

		if _, exists := modelAidMap[key]; !exists {
			modelAidMap[key] = w.Map{}
			modelAidConditions = append(modelAidConditions, "(model='"+model+"' AND aid="+aid+")")
		}
	}

	// 查询表2获取业务信息
	contentDB := vant2.DB(tables.Content, pg.dbName)

	// 应用表2的where条件
	for _, condition := range pg.whereConditions {
		contentDB = contentDB.Where(condition)
	}

	// 添加软删除过滤
	contentDB = contentDB.Where("is_delete", 0)

	if len(modelAidConditions) > 0 {
		contentDB = contentDB.Where("(" + strings.Join(modelAidConditions, " OR ") + ")")
	}

	contentResults, _ := contentDB.All()

	// 建立model+aid到业务信息的映射
	for _, content := range contentResults {
		model := gconv.String(content["model"])
		aid := gconv.String(content["aid"])
		key := model + "_" + aid
		modelAidMap[key] = gconv.Map(content)
	}

	// 第三步：合并表3的检索结果和表2的业务信息
	var finalResults w.SliceMap
	for _, splitItem := range splitResults {
		model := gconv.String(splitItem["model"])
		aid := gconv.String(splitItem["aid"])
		key := model + "_" + aid

		if contentInfo, exists := modelAidMap[key]; exists {
			// 合并数据：表3的核心字段 + 表2的业务字段
			merged := w.Map{
				"model":       splitItem["model"],
				"aid":         splitItem["aid"],
				"content":     splitItem["content"],
				"split_index": splitItem["split_index"],
				"distance":    splitItem["distance"],
				"channel":     contentInfo["channel"],
				"status":      contentInfo["status"],
				"group_id":    contentInfo["group_id"],
				"tags":        contentInfo["tags"],
				"user_id":     contentInfo["user_id"],
				"time_create": contentInfo["time_create"],
			}
			finalResults = append(finalResults, merged)
		}
	}

	// 处理rerank重排序
	if pg.useRerank {
		finalResults = pg._processRerank(finalResults)
		if pg.limitCount > 0 && len(finalResults) > pg.limitCount {
			finalResults = finalResults[:pg.limitCount]
		}
	}

	return finalResults
}

// _addTablePrefix 为where条件添加表前缀
func (pg *PGVector) _addTablePrefix(condition w.Map, prefix string) w.Map {
	result := w.Map{}
	for key, value := range condition {
		// 如果key不包含点（没有表前缀），则添加前缀
		if !strings.Contains(key, ".") {
			result[prefix+"."+key] = value
		} else {
			result[key] = value
		}
	}
	return result
}
