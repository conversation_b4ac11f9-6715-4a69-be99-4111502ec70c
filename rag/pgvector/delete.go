package pgvector

import (
	"vant2"
	"vant2/tool/w"
)

// Delete 物理删除数据
// 删除所有3个表中的相关数据
func (pg *PGVector) Delete() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector Delete 验证失败:", err)
		return false
	}

	if len(pg.whereConditions) == 0 {
		vant2.Error("PGVector Delete 必须提供where条件")
		return false
	}

	tables := pg._getTableNames()
	success := true

	// 删除基础表数据
	if !pg._deleteFromTable(tables.Base) {
		success = false
	}

	// 删除内容表数据
	if !pg._deleteFromTable(tables.Content) {
		success = false
	}

	// 删除分割表数据
	if !pg._deleteFromTable(tables.Split) {
		success = false
	}

	return success
}

// Remove 软删除数据
// 在基础表和内容表中设置is_delete=1，分割表保持不变
func (pg *PGVector) Remove() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector Remove 验证失败:", err)
		return false
	}

	if len(pg.whereConditions) == 0 {
		vant2.Error("PGVector Remove 必须提供where条件")
		return false
	}

	tables := pg._getTableNames()
	success := true

	// 设置软删除标识和删除时间
	updateData := w.Map{
		"is_delete":   1,
		"time_delete": vant2.Time(),
	}

	// 在基础表中执行软删除
	baseDB := pg._buildWhereDB(tables.Base)
	if !baseDB.Data(updateData).Update() {
		success = false
	}

	// 在内容表中执行软删除
	contentDB := pg._buildWhereDB(tables.Content)
	if !contentDB.Data(updateData).Update() {
		success = false
	}

	return success
}

// DeleteById 根据ID删除（物理删除）
func (pg *PGVector) DeleteById(id int64) bool {
	return pg.Where(w.Map{"id": id}).Delete()
}

// RemoveById 根据ID软删除
func (pg *PGVector) RemoveById(id int64) bool {
	return pg.Where(w.Map{"id": id}).Remove()
}

// DeleteByModel 根据model和aid删除（物理删除）
func (pg *PGVector) DeleteByModel(model string, aid int64) bool {
	return pg.Where(w.Map{
		"model": model,
		"aid":   aid,
	}).Delete()
}

// RemoveByModel 根据model和aid软删除
func (pg *PGVector) RemoveByModel(model string, aid int64) bool {
	return pg.Where(w.Map{
		"model": model,
		"aid":   aid,
	}).Remove()
}

// DeleteByUser 根据用户ID删除（物理删除）
func (pg *PGVector) DeleteByUser(userId int64) bool {
	return pg.Where(w.Map{"user_id": userId}).Delete()
}

// RemoveByUser 根据用户ID软删除
func (pg *PGVector) RemoveByUser(userId int64) bool {
	return pg.Where(w.Map{"user_id": userId}).Remove()
}

// _deleteFromTable 从指定表中删除数据
func (pg *PGVector) _deleteFromTable(tableName string) bool {
	// 检查表是否存在，如果不存在就跳过
	if !pg._tableExists(tableName) {
		return true // 表不存在视为成功
	}

	db := pg._buildWhereDB(tableName)
	result, err := db.Delete()

	if err != nil {
		vant2.Error("PGVector _deleteFromTable 删除失败:", tableName, err)
		return false
	}

	affected, err := result.RowsAffected()
	if err != nil {
		vant2.Error("PGVector _deleteFromTable 获取影响行数失败:", tableName, err)
		return false
	}

	vant2.Primary("PGVector _deleteFromTable 删除成功:", tableName, "影响行数:", affected)
	return true
}

// _tableExists 检查表是否存在
func (pg *PGVector) _tableExists(tableName string) bool {
	// 查询pg_tables系统表检查表是否存在
	db := vant2.DB("pg_tables", pg.dbName)
	count := db.Where("tablename", tableName).Count()
	return count > 0
}

// ForceDelete 强制删除（忽略软删除标识）
func (pg *PGVector) ForceDelete() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector ForceDelete 验证失败:", err)
		return false
	}

	if len(pg.whereConditions) == 0 {
		vant2.Error("PGVector ForceDelete 必须提供where条件")
		return false
	}

	// 先删除所有软删除的数据（包括is_delete=1的）
	originalConditions := pg.whereConditions

	// 添加软删除条件查询
	pg.whereConditions = append(pg.whereConditions, w.Map{"is_delete": 1})

	tables := pg._getTableNames()
	success := true

	// 删除基础表数据
	if !pg._deleteFromTable(tables.Base) {
		success = false
	}

	// 删除内容表数据
	if !pg._deleteFromTable(tables.Content) {
		success = false
	}

	// 删除分割表数据
	if !pg._deleteFromTable(tables.Split) {
		success = false
	}

	// 恢复原始条件
	pg.whereConditions = originalConditions

	return success
}

// ClearDeleted 清理已软删除的数据（物理删除）
func (pg *PGVector) ClearDeleted() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector ClearDeleted 验证失败:", err)
		return false
	}

	// 添加软删除条件
	pg.whereConditions = append(pg.whereConditions, w.Map{"is_delete": 1})

	return pg.Delete()
}
