package pgvector

import (
	"assistant/rag/embedding"
	"fmt"
	"sort"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// 多模态内容处理工具函数

// FormatContentForStorage 格式化 content 用于数据库存储
// 将复杂的多模态对象序列化为 JSON 字符串
func FormatContentForStorage(content interface{}) string {
	if content == nil {
		return ""
	}

	// 如果是字符串，直接返回
	if str, ok := content.(string); ok {
		return str
	}

	// 其他复杂类型（数组、对象等），序列化为 JSON
	return vant2.JsonEncoder(content)
}

// ParseContentFromStorage 从数据库存储中解析 content
// 智能检测是 JSON 还是普通字符串
func ParseContentFromStorage(stored string) interface{} {
	if stored == "" {
		return ""
	}

	// 尝试解析为 JSON
	if (strings.HasPrefix(stored, "{") && strings.HasSuffix(stored, "}")) ||
		(strings.HasPrefix(stored, "[") && strings.HasSuffix(stored, "]")) {
		if decoded := vant2.JsonDecoder(stored); decoded != nil {
			return decoded
		}
	}

	// 如果不是 JSON 或解析失败，返回原字符串
	return stored
}

// ExtractTextFromContent 从多模态 content 中提取纯文本用于 embedding
// 支持字符串、数组格式的多模态内容
func ExtractTextFromContent(content interface{}) string {
	if content == nil {
		return ""
	}

	// 如果是字符串，直接返回
	if str, ok := content.(string); ok {
		return str
	}

	// 如果是数组，处理多模态格式
	if arr, ok := content.([]interface{}); ok {
		// OpenAI 多模态格式：[{"type": "text", "text": "..."}, {"type": "image_url", ...}]
		var textParts []string
		for _, part := range arr {
			if partMap := gconv.Map(part); partMap != nil {
				if partType := gconv.String(partMap["type"]); partType == "text" {
					if text := gconv.String(partMap["text"]); text != "" {
						textParts = append(textParts, text)
					}
				}
			}
		}
		return strings.Join(textParts, " ")
	}

	// 转换为通用 Map 格式处理
	contentMap := gconv.Map(content)
	if contentMap == nil {
		return gconv.String(content)
	}

	// 检查是否是包含 text 字段的对象
	if text := gconv.String(contentMap["text"]); text != "" {
		return text
	}

	// 检查是否是包含 content 字段的对象
	if innerContent := contentMap["content"]; innerContent != nil {
		return ExtractTextFromContent(innerContent)
	}

	// 如果包含其他字段，尝试序列化为字符串
	return vant2.JsonEncoder(contentMap)
}

// ChatHistory 聊天历史结构体，支持链式调用
type ChatHistory struct {
	// 基础配置
	dbName string // 数据库名，默认vsearch

	// 查询条件
	whereConditions    []w.Map     // where条件数组
	queryText          string      // 查询文本（用于相似度检索）
	queryLimit         int         // 相似度查询额外数量
	embedding          interface{} // 预设的embedding向量
	limitCount         int         // 最新对话限制数量
	excludeRecentCount int         // 排除最新记录数量（用于VectorList）

	// 高级功能
	useRerank   bool    // 是否使用rerank
	rerankScore float64 // rerank最小分数
	useFullChat bool    // 是否扩展完整对话
	distance    float64 // 距离阈值

	// 保存数据
	dataMap w.Map // 要保存的数据

	// 查询结果（内部使用）
	recentChats   w.SliceMap // 最新对话
	similarChats  w.SliceMap // 相似对话
	allChats      w.SliceMap // 合并后的所有对话
	messageFormat []w.Map    // 大模型消息格式
}

// Message 大模型消息格式 - 修复：支持多模态内容
type Message struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"` // 修改为 interface{} 支持多模态
}

// ChatHistoryDB 创建新的聊天历史实例
func ChatHistoryDB() *ChatHistory {
	return &ChatHistory{
		dbName:          "vsearch",
		limitCount:      5,
		queryLimit:      5,
		whereConditions: make([]w.Map, 0),
		distance:        1.5, // 默认距离阈值
	}
}

// DBName 设置数据库名
func (ch *ChatHistory) DBName(name string) *ChatHistory {
	ch.dbName = name
	return ch
}

// Where 添加查询条件
func (ch *ChatHistory) Where(condition w.Map) *ChatHistory {
	ch.whereConditions = append(ch.whereConditions, condition)
	return ch
}

// Query 设置相似度查询（查询文本，额外数量）
func (ch *ChatHistory) Query(text string, extraLimit ...int) *ChatHistory {
	ch.queryText = text
	if len(extraLimit) > 0 && extraLimit[0] > 0 {
		ch.queryLimit = extraLimit[0]
	}
	return ch
}

// Embedding 设置预设的embedding向量
func (ch *ChatHistory) Embedding(emb interface{}) *ChatHistory {
	ch.embedding = emb
	return ch
}

// Rerank 启用rerank重排序
func (ch *ChatHistory) Rerank(score float64) *ChatHistory {
	ch.useRerank = true
	ch.rerankScore = score
	return ch
}

// FullChat 启用完整对话扩展
func (ch *ChatHistory) FullChat() *ChatHistory {
	ch.useFullChat = true
	return ch
}

// Distance 设置距离阈值
func (ch *ChatHistory) Distance(dist float64) *ChatHistory {
	ch.distance = dist
	return ch
}

// Data 设置要保存的数据
func (ch *ChatHistory) Data(data w.Map) *ChatHistory {
	ch.dataMap = data
	return ch
}

// List 执行查询，返回聊天记录（最新对话数量）
func (ch *ChatHistory) List(recentCount ...int) w.SliceMap {
	if len(recentCount) > 0 && recentCount[0] > 0 {
		ch.limitCount = recentCount[0]
	}

	// 1. 获取最新对话
	ch.recentChats = ch._getRecentChats()

	// 2. 如果有相似度查询，获取相似对话
	if ch.queryText != "" {
		ch.similarChats = ch._getSimilarChats()
	}

	// 3. 如果启用FullChat，扩展完整对话
	if ch.useFullChat {
		ch._expandFullChats()
	}

	// 4. 合并结果
	ch._mergeResults()

	return ch.allChats
}

// VectorList 纯向量相似度查询，不包含最新对话
// queryCount: 查询数量
// excludeRecentCount: 排除最新的几条记录（可选）
func (ch *ChatHistory) VectorList(queryCount int, excludeRecentCount ...int) w.SliceMap {
	// 设置查询数量
	ch.queryLimit = queryCount

	// 设置排除最新记录数量
	if len(excludeRecentCount) > 0 && excludeRecentCount[0] > 0 {
		ch.excludeRecentCount = excludeRecentCount[0]
	}

	// 检查是否设置了查询文本或embedding
	if ch.queryText == "" && ch.embedding == nil {
		vant2.Error("VectorList 需要设置查询文本(Query)或embedding向量(Embedding)")
		return w.SliceMap{}
	}

	// 获取相似对话（带排除逻辑）
	ch.similarChats = ch._getSimilarChatsWithExclusion()

	// 如果启用FullChat，扩展完整对话
	if ch.useFullChat {
		ch._expandFullChats()
	}

	// 设置结果（VectorList只返回相似对话，不合并最新对话）
	ch.allChats = ch.similarChats

	return ch.allChats
}

// VectorListAndMessages 纯向量相似度查询并生成消息格式 - 一步干到底
func (ch *ChatHistory) VectorListAndMessages(queryCount int, excludeRecentCount ...int) []Message {
	ch.VectorList(queryCount, excludeRecentCount...)
	return ch.MessageList()
}

// VectorListSystem 纯向量相似度查询并生成记忆系统 prompt - 一步干到底
func (ch *ChatHistory) VectorListSystem(queryCount int, excludeRecentCount ...int) string {
	ch.VectorList(queryCount, excludeRecentCount...)
	return ch._generateMemorySystemContent()
}

// ListAndMessages 执行查询并生成消息格式 - 一步干到底
func (ch *ChatHistory) ListAndMessages(recentCount ...int) []Message {
	ch.List(recentCount...)
	return ch.MessageList()
}

// MessageList 生成大模型消息格式
func (ch *ChatHistory) MessageList() []Message {
	if len(ch.allChats) == 0 {
		return []Message{}
	}

	var messages []Message

	// 1. 如果有相似对话，先生成system提示
	if len(ch.similarChats) > 0 {
		systemContent := ch._generateSystemContent()
		messages = append(messages, Message{
			Role:    "system",
			Content: systemContent,
		})
	}

	// 2. 添加最新对话（按时间排序）
	recentMessages := ch._formatRecentMessages()
	messages = append(messages, recentMessages...)

	return messages
}

// _getRecentChats 获取最新对话
func (ch *ChatHistory) _getRecentChats() w.SliceMap {
	db := vant2.DB("pgvector_chat_history", ch.dbName)

	// 添加where条件
	for _, condition := range ch.whereConditions {
		db = db.Where(condition)
	}

	// 软删除过滤
	db = db.Where("is_delete", 0)

	// 按时间倒序，限制数量
	results, err := db.Order("time_create DESC,id DESC").Limit(ch.limitCount).All()
	if err != nil {
		vant2.Error("ChatHistory _getRecentChats 查询失败:", err)
		return w.SliceMap{}
	}

	return gconv.SliceMap(results)
}

// _getSimilarChats 获取相似对话
func (ch *ChatHistory) _getSimilarChats() w.SliceMap {
	// 获取embedding向量
	var embeddingVector interface{}
	var err error

	if ch.embedding != nil {
		embeddingVector = ch.embedding
	} else {
		embeddingVector, err = embedding.EmbeddingText(ch.queryText)
		if err != nil {
			vant2.Error("ChatHistory 生成embedding失败:", err)
			return w.SliceMap{}
		}
	}

	// 获取最新对话的ID列表（用于排除）
	recentIds := make([]interface{}, len(ch.recentChats))
	for i, chat := range ch.recentChats {
		recentIds[i] = chat["id"]
	}

	db := vant2.DB("pgvector_chat_history", ch.dbName)

	// 添加where条件
	for _, condition := range ch.whereConditions {
		db = db.Where(condition)
	}

	// 软删除过滤
	db = db.Where("is_delete", 0)

	// 排除最新对话的ID
	if len(recentIds) > 0 {
		db = db.Where("id NOT IN (?)", recentIds)
	}

	// 向量相似度查询
	vectorField := db.VectorFields(&w.VectorAttr{
		FieldName: "vector",
		Embedding: embeddingVector,
	})

	db = db.Fields("*," + vectorField)

	// 距离过滤
	if ch.distance > 0 {
		distanceExpr := strings.Replace(vectorField, " AS distance", "", 1)
		db = db.Where("("+distanceExpr+") < ?", ch.distance)
	}

	// 按距离排序，限制数量
	limit := ch.queryLimit
	if ch.useRerank {
		limit = limit * 2 // rerank需要更多候选数据
	}

	results, err := db.Order("distance ASC").Limit(limit).All()
	if err != nil {
		vant2.Error("ChatHistory _getSimilarChats 查询失败:", err)
		return w.SliceMap{}
	}
	similarList := gconv.SliceMap(results)

	// 处理rerank重排序
	if ch.useRerank && len(similarList) > 0 {
		similarList = ch._processRerank(similarList)
		if len(similarList) > ch.queryLimit {
			similarList = similarList[:ch.queryLimit]
		}
	}

	return similarList
}

// _getSimilarChatsWithExclusion 获取相似对话（支持排除最新记录）
func (ch *ChatHistory) _getSimilarChatsWithExclusion() w.SliceMap {
	// 获取embedding向量
	var embeddingVector interface{}
	var err error

	if ch.embedding != nil {
		embeddingVector = ch.embedding
	} else {
		embeddingVector, err = embedding.EmbeddingText(ch.queryText)
		if err != nil {
			vant2.Error("ChatHistory 生成embedding失败:", err)
			return w.SliceMap{}
		}
	}

	// 获取要排除的最新记录ID列表
	var excludeIds []interface{}
	if ch.excludeRecentCount > 0 {
		excludeIds = ch._getExcludeIds()
	}

	db := vant2.DB("pgvector_chat_history", ch.dbName)

	// 添加where条件
	for _, condition := range ch.whereConditions {
		db = db.Where(condition)
	}

	// 软删除过滤
	db = db.Where("is_delete", 0)

	// 排除指定的ID
	if len(excludeIds) > 0 {
		db = db.Where("id NOT IN (?)", excludeIds)
	}

	// 向量相似度查询
	vectorField := db.VectorFields(&w.VectorAttr{
		FieldName: "vector",
		Embedding: embeddingVector,
	})

	db = db.Fields("*," + vectorField)

	// 距离过滤
	if ch.distance > 0 {
		distanceExpr := strings.Replace(vectorField, " AS distance", "", 1)
		db = db.Where("("+distanceExpr+") < ?", ch.distance)
	}

	// 按距离排序，限制数量
	limit := ch.queryLimit
	if ch.useRerank {
		limit = limit * 2 // rerank需要更多候选数据
	}

	results, err := db.Order("distance ASC").Limit(limit).All()
	if err != nil {
		vant2.Error("ChatHistory _getSimilarChatsWithExclusion 查询失败:", err)
		return w.SliceMap{}
	}
	similarList := gconv.SliceMap(results)

	// 处理rerank重排序
	if ch.useRerank && len(similarList) > 0 {
		similarList = ch._processRerank(similarList)
		if len(similarList) > ch.queryLimit {
			similarList = similarList[:ch.queryLimit]
		}
	}

	return similarList
}

// _getExcludeIds 获取要排除的最新记录ID列表
func (ch *ChatHistory) _getExcludeIds() []interface{} {
	if ch.excludeRecentCount <= 0 {
		return []interface{}{}
	}

	db := vant2.DB("pgvector_chat_history", ch.dbName)

	// 添加where条件
	for _, condition := range ch.whereConditions {
		db = db.Where(condition)
	}

	// 软删除过滤
	db = db.Where("is_delete", 0)

	// 查询最新的记录ID
	results, err := db.Fields("id").
		Order("time_create DESC,id DESC").
		Limit(ch.excludeRecentCount).
		All()
	if err != nil {
		vant2.Error("ChatHistory _getExcludeIds 查询失败:", err)
		return []interface{}{}
	}

	// 提取ID列表
	ids := make([]interface{}, len(results))
	for i, record := range results {
		ids[i] = record["id"]
	}

	return ids
}

// _expandFullChats 扩展完整对话
func (ch *ChatHistory) _expandFullChats() {
	if len(ch.similarChats) == 0 {
		return
	}

	// 收集需要扩展的ID（基于user-assistant配对关系）
	targetIds := make(map[interface{}]bool)

	for _, chat := range ch.similarChats {
		chatId := gconv.Int64(chat["id"])
		role := gconv.String(chat["role"])

		// 添加当前记录ID
		targetIds[chatId] = true

		// 根据role和id关系找配对
		if role == "user" {
			// user找下一个assistant (id+1)
			targetIds[chatId+1] = true
		} else if role == "assistant" {
			// assistant找上一个user (id-1)
			targetIds[chatId-1] = true
		}
		// system角色不需要配对
	}

	if len(targetIds) == 0 {
		return
	}

	// 转换为查询用的ID列表
	idList := make([]interface{}, 0, len(targetIds))
	for id := range targetIds {
		idList = append(idList, id)
	}

	// 查询完整对话
	db := vant2.DB("pgvector_chat_history", ch.dbName)

	// 添加where条件
	for _, condition := range ch.whereConditions {
		db = db.Where(condition)
	}

	db = db.Where("is_delete", 0).
		Where("id IN (?)", idList).
		Order("time_create ASC")

	fullResults, err := db.All()
	if err != nil {
		vant2.Error("ChatHistory _expandFullChats 查询失败:", err)
		return
	}
	fullChats := gconv.SliceMap(fullResults)

	// 替换原有的similarChats为完整对话
	ch.similarChats = fullChats
}

// _mergeResults 合并最新对话和相似对话
func (ch *ChatHistory) _mergeResults() {
	// 合并结果，最新对话在前
	ch.allChats = make(w.SliceMap, 0, len(ch.recentChats)+len(ch.similarChats))

	// 添加最新对话
	ch.allChats = append(ch.allChats, ch.recentChats...)

	// 添加相似对话（需要去重）
	existingIds := make(map[interface{}]bool)
	for _, chat := range ch.recentChats {
		existingIds[chat["id"]] = true
	}

	for _, chat := range ch.similarChats {
		if !existingIds[chat["id"]] {
			ch.allChats = append(ch.allChats, chat)
			existingIds[chat["id"]] = true
		}
	}
}

// _generateSystemContent 生成system角色的上下文内容
func (ch *ChatHistory) _generateSystemContent() string {
	if len(ch.similarChats) == 0 {
		return ""
	}

	var content strings.Builder
	content.WriteString("对话辅助记忆系统，查询到和当前对话可能存在相关性的历史对话记录如下：\n\n")

	// 按conversation_id分组显示
	conversationMap := make(map[interface{}][]w.Map)
	for _, chat := range ch.similarChats {
		convId := chat["conversation_id"]
		conversationMap[convId] = append(conversationMap[convId], chat)
	}

	for _, chats := range conversationMap {
		// 按时间排序
		sort.Slice(chats, func(i, j int) bool {
			time1 := gconv.Int64(chats[i]["time_create"])
			time2 := gconv.Int64(chats[j]["time_create"])
			return time1 < time2
		})

		for _, chat := range chats {
			role := gconv.String(chat["role"])
			// 使用智能文本提取，支持多模态内容
			content_text := ExtractTextFromContent(chat["content"])
			content.WriteString(fmt.Sprintf("%s: %s\n", role, content_text))
		}
		content.WriteString("\n")
	}

	return content.String()
}

// _generateMemorySystemContent 生成记忆系统 prompt
func (ch *ChatHistory) _generateMemorySystemContent() string {
	if len(ch.similarChats) == 0 {
		return ""
	}

	var content strings.Builder
	content.WriteString("对话辅助记忆系统，查询到和当前对话可能存在相关性的历史对话记录如下：\n\n")

	// 按conversation_id分组显示
	conversationMap := make(map[interface{}][]w.Map)
	for _, chat := range ch.similarChats {
		convId := chat["conversation_id"]
		conversationMap[convId] = append(conversationMap[convId], chat)
	}

	for _, chats := range conversationMap {
		// 按时间排序
		sort.Slice(chats, func(i, j int) bool {
			time1 := gconv.Int64(chats[i]["time_create"])
			time2 := gconv.Int64(chats[j]["time_create"])
			return time1 < time2
		})

		for _, chat := range chats {
			role := gconv.String(chat["role"])
			// 使用智能文本提取，支持多模态内容
			content_text := ExtractTextFromContent(chat["content"])
			content.WriteString(fmt.Sprintf("%s: %s\n", role, content_text))
		}
		content.WriteString("\n")
	}

	return content.String()
}

// _formatRecentMessages 格式化最新对话为消息格式
func (ch *ChatHistory) _formatRecentMessages() []Message {
	if len(ch.recentChats) == 0 {
		return []Message{}
	}

	// 按时间正序排列（最新对话是倒序查询的，需要反转）
	sortedChats := make(w.SliceMap, len(ch.recentChats))
	copy(sortedChats, ch.recentChats)

	sort.Slice(sortedChats, func(i, j int) bool {
		time1 := gconv.Int64(sortedChats[i]["time_create"])
		time2 := gconv.Int64(sortedChats[j]["time_create"])
		return time1 < time2
	})

	messages := make([]Message, 0, len(sortedChats))
	for _, chat := range sortedChats {
		role := gconv.String(chat["role"])
		// 解析存储的 content，保持多模态格式
		content := ParseContentFromStorage(gconv.String(chat["content"]))

		// 跳过system角色和空内容
		if role == "system" || content == "" || (content == "" && gconv.String(content) == "") {
			continue
		}

		messages = append(messages, Message{
			Role:    role,
			Content: content, // 保持原始的多模态格式
		})
	}

	return messages
}

// _processRerank 处理重排序
func (ch *ChatHistory) _processRerank(list w.SliceMap) w.SliceMap {
	if len(list) == 0 {
		return list
	}

	// 使用embedding包的Rerank功能
	rerankOptions := &embedding.RerankOptions{
		Query:     ch.queryText,
		Documents: gconv.SliceMap(list),
		MinScore:  ch.rerankScore,
	}

	rerankedResult, err := embedding.Rerank(rerankOptions)
	if err != nil {
		vant2.Error("ChatHistory Rerank失败:", err)
		return list
	}

	// 类型断言
	if result, ok := rerankedResult.(w.SliceMap); ok {
		return result
	}

	return list
}

// Save 保存聊天记录
func (ch *ChatHistory) Save() bool {
	if ch.dataMap == nil || len(ch.dataMap) == 0 {
		vant2.Error("ChatHistory Save 数据不能为空")
		return false
	}

	// 提取文本用于生成embedding向量（多模态兼容）
	contentForEmbedding := ExtractTextFromContent(ch.dataMap["content"])
	if contentForEmbedding == "" {
		vant2.Error("ChatHistory Save content不能为空")
		return false
	}

	embeddingVector, err := embedding.EmbeddingText(contentForEmbedding)
	if err != nil {
		vant2.Error("ChatHistory Save 生成embedding失败:", err)
		return false
	}

	// 准备保存数据
	saveData := w.Map{
		"vector":          ch._formatVector(embeddingVector),
		"user_id":         ch.dataMap["user_id"],
		"channel_id":      gconv.Int64(ch.dataMap["channel_id"]),
		"conversation_id": gconv.Int64(ch.dataMap["conversation_id"]),
		"role":            gconv.String(ch.dataMap["role"]),
		"content":         FormatContentForStorage(ch.dataMap["content"]), // 多模态兼容存储
		"tokens":          gconv.Int(ch.dataMap["tokens"]),
		"extra":           ch.dataMap["extra"],
		"is_delete":       0,
	}

	// 检查是插入还是更新
	if id := gconv.Int64(ch.dataMap["id"]); id > 0 {
		// 更新
		return vant2.DB("pgvector_chat_history", ch.dbName).Where("id", id).Data(saveData).Update()
	} else {
		// 插入
		_, err := vant2.DB("pgvector_chat_history", ch.dbName).Data(saveData).Insert()
		return err == nil
	}
}

// Delete 软删除聊天记录
func (ch *ChatHistory) Delete() bool {
	if len(ch.whereConditions) == 0 {
		vant2.Error("ChatHistory Delete 必须提供where条件")
		return false
	}

	db := vant2.DB("pgvector_chat_history", ch.dbName)

	// 添加where条件
	for _, condition := range ch.whereConditions {
		db = db.Where(condition)
	}

	// 软删除
	return db.Data(w.Map{
		"is_delete":   1,
		"time_delete": vant2.Time(),
	}).Update()
}

// DeleteById 根据ID软删除
func (ch *ChatHistory) DeleteById(id int64) bool {
	return vant2.DB("pgvector_chat_history", ch.dbName).Where("id", id).Data(w.Map{
		"is_delete": 1,
	}).Update()
}

// _formatVector 格式化向量为PostgreSQL字符串
func (ch *ChatHistory) _formatVector(embeddingVector interface{}) string {
	switch embValue := embeddingVector.(type) {
	case string:
		if len(embValue) > 0 {
			if embValue[0] == '{' && embValue[len(embValue)-1] == '}' {
				return "[" + embValue[1:len(embValue)-1] + "]"
			} else if embValue[0] == '[' && embValue[len(embValue)-1] == ']' {
				return embValue
			} else {
				return "[" + embValue + "]"
			}
		}
	case []interface{}:
		embStr := "["
		for i, v := range embValue {
			if i > 0 {
				embStr += ", "
			}
			embStr += gconv.String(v)
		}
		embStr += "]"
		return embStr
	case []float64:
		embStr := "["
		for i, v := range embValue {
			if i > 0 {
				embStr += ", "
			}
			embStr += gconv.String(v)
		}
		embStr += "]"
		return embStr
	default:
		embStr := gconv.String(embeddingVector)
		if len(embStr) > 0 && embStr[0] != '[' {
			embStr = "[" + embStr + "]"
		}
		return embStr
	}
	return "[]"
}

// 便捷方法

// SaveMessage 快速保存消息（多模态兼容）
func (ch *ChatHistory) SaveMessage(userId int64, channelId int64, conversationId int64, role string, content interface{}, extra ...string) bool {
	data := w.Map{
		"user_id":         userId,
		"channel_id":      channelId,
		"conversation_id": conversationId,
		"role":            role,
		"content":         content, // 直接传递 interface{}，将在 Save() 中处理
	}

	if len(extra) > 0 {
		data["extra"] = extra[0]
	}

	return ch.Data(data).Save()
}

// GetRecentConversations 获取用户最近的对话组
func (ch *ChatHistory) GetRecentConversations(userId int64, channelId int64, limit int) w.SliceMap {
	db := vant2.DB("pgvector_chat_history", ch.dbName)
	results, err := db.Where("user_id", userId).
		Where("channel_id", channelId).
		Where("is_delete", 0).
		Group("conversation_id").
		Fields("conversation_id, MAX(time_create) as last_time, COUNT(*) as message_count").
		Order("last_time DESC").
		Limit(limit).
		All()
	if err != nil {
		vant2.Error("ChatHistory GetRecentConversations 查询失败:", err)
		return w.SliceMap{}
	}

	return gconv.SliceMap(results)
}
