// Package pgvector - QA问答库专用功能
//
// QA问答库是基于PGVector的专门用于问答场景的向量检索系统。
// 与常规文档检索不同，QA库专门针对问题-答案对进行优化。
//
// 主要特性：
// 1. 专门的pgvector_qa表结构，使用q(问题)、a(答案)、remark(备注)字段
// 2. 向量基于"q|a"合并内容生成，取前8000字符
// 3. 支持完整的链式调用：Where、Query、Limit、Rerank等
// 4. 完整的CRUD操作：SaveQA、Update、Delete、List
// 5. 智能语义搜索和Rerank重排序
//
// 快速开始：
//
//	// 1. 保存QA
//	qa := NewQA()
//	success := qa.Data(g.Map{
//	    "q": "什么是人工智能？",
//	    "a": "人工智能是计算机科学的一个分支...",
//	    "model": "faq",
//	    "aid": 1,
//	    "user_id": 1001,
//	}).SaveQA()
//
//	// 2. 语义搜索QA
//	results := NewQA().Where(g.Map{"model": "faq"}).Query("AI技术").Limit(5).List()
//
//	// 3. 高级搜索（带Rerank）
//	results := NewQA().Where(g.Map{"model": "faq"}).Query("机器学习").Rerank(0.1).List()
//
//	// 4. 更新QA
//	success := NewQA().Where(g.Map{"model": "faq", "aid": 1}).Data(g.Map{
//	    "a": "更新后的答案内容...",
//	}).Update()
//
//	// 5. 删除QA
//	success := NewQA().Where(g.Map{"model": "faq", "aid": 1}).Delete()
//
// 数据表结构：
// - 使用pgvector_qa表存储问答数据
// - q字段：问题内容
// - a字段：答案内容
// - remark字段：备注说明
// - vector字段：基于"q|a"合并内容的embedding向量
// - 其他字段：user_id, model, aid, title, extra, channel, status等业务字段
//
// API测试：
// 访问 /pgvector/test 可以看到完整的QA功能测试界面
package pgvector

import (
	"assistant/rag/embedding"
	"errors"
	"fmt"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// QAVector 专门用于问答库的结构体，支持链式调用
type QAVector struct {
	// 基础配置
	tableName  string // 表名，固定为pgvector_qa
	dbName     string // 数据库名，默认vsearch
	vectorName string // 向量字段名，默认vector

	// 查询条件
	whereConditions []w.Map     // where条件数组
	queryText       string      // 查询文本
	embedding       interface{} // 预设的embedding向量
	limitCount      int         // 查询限制数量
	distance        float64     // 距离阈值

	// Rerank配置
	useRerank   bool    // 是否使用rerank
	rerankScore float64 // rerank最小分数

	// QA数据
	dataMap w.Map // 要保存的QA数据

	// Embedding配置
	embeddingModel string // embedding模型名称
}

// NewQA 创建新的QAVector实例
func NewQA() *QAVector {
	return &QAVector{
		tableName:       "pgvector_qa", // 固定QA表名
		dbName:          "vsearch",
		vectorName:      "vector",
		limitCount:      20,
		embeddingModel:  "BAAI/bge-m3",
		whereConditions: make([]w.Map, 0),
	}
}

// DBName 设置数据库名
func (qa *QAVector) DBName(name string) *QAVector {
	qa.dbName = name
	return qa
}

// VectorName 设置向量字段名
func (qa *QAVector) VectorName(name string) *QAVector {
	qa.vectorName = name
	return qa
}

// Where 添加查询条件
func (qa *QAVector) Where(condition interface{}, args ...interface{}) *QAVector {
	switch v := condition.(type) {
	case string:
		// 字符串条件，如 "user_id = ?"
		if len(args) > 0 {
			qa.whereConditions = append(qa.whereConditions, w.Map{
				"condition": v,
				"args":      args,
			})
		}
	case w.Map:
		// Map条件，如 w.Map{"user_id": 1, "status": "active"}
		qa.whereConditions = append(qa.whereConditions, v)
	}
	return qa
}

// Query 设置查询文本
func (qa *QAVector) Query(text string) *QAVector {
	qa.queryText = text
	return qa
}

// Embedding 设置预设的embedding向量
func (qa *QAVector) Embedding(emb interface{}) *QAVector {
	qa.embedding = emb
	return qa
}

// Limit 设置查询限制数量（1-100）
func (qa *QAVector) Limit(count int) *QAVector {
	if count < 1 {
		count = 1
	} else if count > 100 {
		count = 100
	}
	qa.limitCount = count
	return qa
}

// Distance 设置距离阈值
func (qa *QAVector) Distance(dist float64) *QAVector {
	qa.distance = dist
	return qa
}

// Rerank 启用rerank重排序
func (qa *QAVector) Rerank(score ...float64) *QAVector {
	qa.useRerank = true
	if len(score) > 0 {
		qa.rerankScore = score[0]
	}
	return qa
}

// Data 设置要保存的QA数据
func (qa *QAVector) Data(data w.Map) *QAVector {
	qa.dataMap = data
	return qa
}

// EmbeddingModel 设置embedding模型
func (qa *QAVector) EmbeddingModel(model string) *QAVector {
	qa.embeddingModel = model
	return qa
}

// _buildWhereDB 构建带where条件的数据库对象
func (qa *QAVector) _buildWhereDB() *vant2.DBModel {
	db := vant2.DB(qa.tableName, qa.dbName)

	// 应用where条件
	for _, condition := range qa.whereConditions {
		if condStr, ok := condition["condition"]; ok {
			// 字符串条件
			if args, hasArgs := condition["args"]; hasArgs {
				db = db.Where(condStr, args)
			} else {
				db = db.Where(condStr)
			}
		} else {
			// Map条件
			db = db.Where(condition)
		}
	}

	return db
}

// _validateQAData 验证QA数据
func (qa *QAVector) _validateQAData() error {
	if qa.dataMap == nil {
		return errors.New("QA数据不能为空")
	}

	q, hasQ := qa.dataMap["q"]
	a, hasA := qa.dataMap["a"]

	if !hasQ || q == "" {
		return errors.New("问题(q)不能为空")
	}

	if !hasA || a == "" {
		return errors.New("答案(a)不能为空")
	}

	model, hasModel := qa.dataMap["model"]
	_, hasAid := qa.dataMap["aid"]

	if !hasModel || model == "" {
		return errors.New("模型名(model)不能为空")
	}

	if !hasAid {
		return errors.New("关联ID(aid)不能为空")
	}

	return nil
}

// _generateQAEmbedding 生成QA的embedding向量
func (qa *QAVector) _generateQAEmbedding() (interface{}, error) {
	// 如果已经设置了预设的embedding，直接使用
	if qa.embedding != nil {
		return qa.embedding, nil
	}

	// 获取q和a字段
	q := qa.dataMap["q"].(string)
	a := qa.dataMap["a"].(string)

	// 合并q和a，用"|"分隔，取前8000字符
	combined := fmt.Sprintf("%s|%s", q, a)
	if len(combined) > 8000 {
		// 按字符数截取，确保不超过8000字符
		runes := []rune(combined)
		if len(runes) > 8000 {
			combined = string(runes[:8000])
		}
	}

	// 调用embedding服务生成向量
	vector, err := embedding.EmbeddingText(combined)
	if err != nil {
		return nil, fmt.Errorf("生成embedding失败: %v", err)
	}

	return vector, nil
}

// SaveQA 保存QA数据
func (qa *QAVector) SaveQA() bool {
	// 验证数据
	if err := qa._validateQAData(); err != nil {
		// g.Log().Error(nil, "QA数据验证失败:", err)
		return false
	}

	// 生成embedding向量
	vector, err := qa._generateQAEmbedding()
	if err != nil {
		// g.Log().Error(nil, "生成QA embedding失败:", err)
		return false
	}

	// 准备保存数据
	saveData := w.Map{}
	for k, v := range qa.dataMap {
		saveData[k] = v
	}

	// 添加向量
	saveData["vector"] = qa._formatVector(vector)

	// 设置默认值
	if _, hasUserID := saveData["user_id"]; !hasUserID {
		saveData["user_id"] = 0
	}
	if _, hasStatus := saveData["status"]; !hasStatus {
		saveData["status"] = "active"
	}
	if _, hasTitle := saveData["title"]; !hasTitle {
		saveData["title"] = ""
	}
	if _, hasRemark := saveData["remark"]; !hasRemark {
		saveData["remark"] = ""
	}
	if _, hasExtra := saveData["extra"]; !hasExtra {
		saveData["extra"] = "{}"
	}
	if _, hasChannel := saveData["channel"]; !hasChannel {
		saveData["channel"] = ""
	}
	if _, hasGroupID := saveData["group_id"]; !hasGroupID {
		saveData["group_id"] = 0
	}
	if _, hasTags := saveData["tags"]; !hasTags {
		saveData["tags"] = ""
	}

	// 保存到数据库
	db := vant2.DB(qa.tableName, qa.dbName)
	result, err := db.Data(saveData).Insert()
	if err != nil {
		// g.Log().Error(nil, "保存QA失败:", err)
		return false
	}

	rowsAffected, _ := result.RowsAffected()
	return rowsAffected > 0
}

// Update 更新QA数据
func (qa *QAVector) Update() bool {
	// 验证数据
	if qa.dataMap == nil {
		// g.Log().Error(nil, "更新数据不能为空")
		return false
	}

	// 如果更新了q或a字段，需要重新生成embedding
	updateData := w.Map{}
	for k, v := range qa.dataMap {
		updateData[k] = v
	}

	// 检查是否需要重新生成embedding
	if _, hasQ := updateData["q"]; hasQ {
		vector, err := qa._generateQAEmbedding()
		if err != nil {
			return false
		}
		updateData["vector"] = qa._formatVector(vector)
	}

	// 执行更新
	db := qa._buildWhereDB()
	success := db.Data(updateData).Update()
	if !success {
		return false
	}

	return true
}

// Delete 删除QA数据（物理删除）
func (qa *QAVector) Delete() bool {
	db := qa._buildWhereDB()
	result, err := db.Delete()
	if err != nil {
		// g.Log().Error(nil, "删除QA失败:", err)
		return false
	}

	rowsAffected, _ := result.RowsAffected()
	return rowsAffected > 0
}

// List 查询QA列表
func (qa *QAVector) List() []w.Map {
	var results []w.Map

	// 构建基础查询
	db := qa._buildWhereDB()

	// 如果有查询文本或预设embedding，进行向量搜索
	if qa.queryText != "" || qa.embedding != nil {
		results = qa._vectorSearch()
	} else {
		// 普通查询 - 指定需要的字段
		db = db.Fields(qa._getSelectFields())
		queryResults, err := db.Limit(qa.limitCount).All()
		if err != nil {
			// g.Log().Error(nil, "查询QA失败:", err)
			return results
		}

		for _, row := range queryResults {
			results = append(results, w.Map(row.Map()))
		}
	}

	// 应用Rerank
	if qa.useRerank && len(results) > 0 {
		results = qa._applyRerank(results)
	}

	return results
}

// _vectorSearch 执行向量搜索 - 基于现有pgvector的成熟实现
func (qa *QAVector) _vectorSearch() []w.Map {
	// 获取embedding向量
	embedding := qa._getEmbedding()
	if embedding == nil {
		// g.Log().Error(nil, "QA向量搜索无法获取embedding向量")
		return nil
	}

	db := qa._buildWhereDB()

	// 添加软删除过滤
	db = db.Where("is_delete", 0)

	// 设置向量距离计算
	vectorField := db.VectorFields(&w.VectorAttr{
		FieldName: qa.vectorName,
		Embedding: embedding,
	})

	// 设置查询字段 - 指定需要的字段加上距离字段
	db = db.Fields(qa._getSelectFields() + "," + vectorField)

	// 设置限制 - 如果使用rerank，则查询更多数据
	limit := qa.limitCount
	if qa.useRerank && limit > 0 {
		limit = limit * 2 // rerank需要更多候选数据
	}
	if limit > 0 {
		db.Limit(limit)
	}

	// 按向量距离排序
	db = db.Order("distance ASC")

	result, err := db.All()
	if err != nil {
		// g.Log().Error(nil, "QA向量搜索失败:", err)
		return nil
	}

	list := gconv.SliceMap(result)

	// 过滤距离阈值
	if qa.distance > 0 {
		filteredList := w.SliceMap{}
		for _, item := range list {
			distance := gconv.Float64(item["distance"])
			if distance < qa.distance {
				filteredList = append(filteredList, item)
			}
		}
		list = filteredList
	}

	// 转换为[]w.Map格式返回
	results := make([]w.Map, len(list))
	for i, item := range list {
		results[i] = w.Map(item)
	}

	return results
}

// _getEmbedding 获取embedding向量 - 复用现有pgvector逻辑
func (qa *QAVector) _getEmbedding() interface{} {
	// 如果已经有预设的embedding，直接返回
	if qa.embedding != nil {
		return qa.embedding
	}

	// 如果有查询文本，调用embedding API
	if qa.queryText != "" {
		// 限制文本长度
		text := qa.queryText
		if len(text) > 8000 {
			text = text[:8000]
		}

		// 使用现有的embedding包
		vector, err := embedding.EmbeddingText(text)
		if err != nil {
			// g.Log().Error(nil, "QA _getEmbedding 调用embedding失败:", err)
			return nil
		}
		return vector
	}

	return nil
}

// _applyRerank 应用Rerank重排序 - 针对QA数据的专门处理
func (qa *QAVector) _applyRerank(results []w.Map) []w.Map {
	if !qa.useRerank || len(results) == 0 || qa.queryText == "" {
		return results
	}

	// 为QA数据准备Rerank格式：将q和a合并为content字段
	rerankedData := make([]interface{}, len(results))
	for i, result := range results {
		// 创建新对象，包含content字段
		item := make(map[string]interface{})

		// 复制所有原始字段
		for k, v := range result {
			item[k] = v
		}

		// 合并q和a作为content字段（这是Rerank需要的）
		q := ""
		a := ""
		if qVal, ok := result["q"]; ok {
			q = fmt.Sprintf("%v", qVal)
		}
		if aVal, ok := result["a"]; ok {
			a = fmt.Sprintf("%v", aVal)
		}
		item["content"] = fmt.Sprintf("%s|%s", q, a)

		rerankedData[i] = item
	}

	// 使用现有的embedding包进行rerank
	rerankOptions := &embedding.RerankOptions{
		Query:     qa.queryText,
		Documents: rerankedData,
		MinScore:  qa.rerankScore,
	}

	// 如果设置了限制，添加到rerank选项
	if qa.limitCount > 0 {
		rerankOptions.MaxResults = qa.limitCount
	}

	// 调用rerank
	rerankedResults, err := embedding.Rerank(rerankOptions)
	if err != nil {
		// g.Log().Error(nil, "QA Rerank失败:", err)
		return results
	}

	// 转换回[]w.Map格式
	rerankedList := gconv.SliceMap(rerankedResults)
	finalResults := make([]w.Map, len(rerankedList))
	for i, item := range rerankedList {
		// 移除临时的content字段，保持原始数据结构
		resultMap := w.Map(item)
		delete(resultMap, "content") // 删除临时添加的content字段
		finalResults[i] = resultMap
	}

	return finalResults
}

// _getSelectFields 获取需要查询的字段列表，排除敏感字段
func (qa *QAVector) _getSelectFields() string {
	// 定义QA表需要返回的字段，排除敏感字段
	selectFields := []string{
		"id",
		"q",
		"a",
		"remark",
		"model",
		"aid",
		"extra",
		"channel",
		"status",
		"tags",
		"time_create",
	}

	// 将字段数组转换为逗号分隔的字符串
	result := ""
	for i, field := range selectFields {
		if i > 0 {
			result += ","
		}
		result += field
	}

	return result
}

// _formatVector 格式化向量为PostgreSQL字符串
func (qa *QAVector) _formatVector(embeddingVector interface{}) string {
	var embStr string

	switch embValue := embeddingVector.(type) {
	case string:
		if len(embValue) > 0 {
			if embValue[0] == '{' && embValue[len(embValue)-1] == '}' {
				embStr = "[" + embValue[1:len(embValue)-1] + "]"
			} else if embValue[0] == '[' && embValue[len(embValue)-1] == ']' {
				embStr = embValue
			} else {
				embStr = "[" + embValue + "]"
			}
		}
	case []interface{}:
		embStr = "["
		for i, v := range embValue {
			if i > 0 {
				embStr += ", "
			}
			embStr += gconv.String(v)
		}
		embStr += "]"
	case []float64:
		embStr = "["
		for i, v := range embValue {
			if i > 0 {
				embStr += ", "
			}
			embStr += gconv.String(v)
		}
		embStr += "]"
	default:
		embStr = gconv.String(embeddingVector)
		if len(embStr) > 0 && embStr[0] != '[' {
			embStr = "[" + embStr + "]"
		}
	}

	return embStr
}
