package pgvector

import (
	"errors"
	"vant2"
	"vant2/tool/w"
)

// PGVector 核心结构体，支持链式调用
type PGVector struct {
	// 基础配置
	tableName   string // 表名
	dbName      string // 数据库名，默认vsearch
	vectorName  string // 向量字段名，默认vector
	contentName string // 内容字段名，默认content

	// 查询条件
	whereConditions []w.Map     // where条件数组
	queryText       string      // 查询文本
	embedding       interface{} // 预设的embedding向量
	limitCount      int         // 查询限制数量
	distance        float64     // 距离阈值

	// Rerank配置
	useRerank   bool    // 是否使用rerank
	rerankScore float64 // rerank最小分数

	// 保存数据
	dataMap     w.Map // 要保存的数据
	splitLength int   // 内容分割长度，>0时启用分割存储

	// Embedding配置
	embeddingModel string // embedding模型名称
}

// TableNames 表名定义结构
type TableNames struct {
	Base    string // 基础表名
	Content string // 内容表名
	Split   string // 分割表名
}

// NewDB 创建新的PGVector实例
func NewDB() *PGVector {
	return &PGVector{
		tableName:       "pgvector_search", // 默认生产环境表名
		dbName:          "vsearch",
		vectorName:      "vector",
		contentName:     "content",
		limitCount:      20,
		embeddingModel:  "BAAI/bge-m3",
		whereConditions: make([]w.Map, 0),
	}
}

// TableName 设置表名
func (pg *PGVector) TableName(name string) *PGVector {
	pg.tableName = name
	return pg
}

// DBName 设置数据库名
func (pg *PGVector) DBName(name string) *PGVector {
	pg.dbName = name
	return pg
}

// VectorName 设置向量字段名
func (pg *PGVector) VectorName(name string) *PGVector {
	pg.vectorName = name
	return pg
}

// ContentName 设置内容字段名
func (pg *PGVector) ContentName(name string) *PGVector {
	pg.contentName = name
	return pg
}

// Where 添加查询条件
func (pg *PGVector) Where(condition interface{}, args ...interface{}) *PGVector {
	switch v := condition.(type) {
	case string:
		// 字符串条件，如 "user_id = ?"
		if len(args) > 0 {
			pg.whereConditions = append(pg.whereConditions, w.Map{
				"condition": v,
				"args":      args,
			})
		}
	case w.Map:
		// Map条件，如 w.Map{"user_id": 1, "status": "active"}
		pg.whereConditions = append(pg.whereConditions, v)
	}
	return pg
}

// Query 设置查询文本
func (pg *PGVector) Query(text string) *PGVector {
	pg.queryText = text
	return pg
}

// Embedding 设置预设的embedding向量
func (pg *PGVector) Embedding(emb interface{}) *PGVector {
	pg.embedding = emb
	return pg
}

// Limit 设置查询限制数量（1-100）
func (pg *PGVector) Limit(count int) *PGVector {
	if count < 1 {
		count = 1
	} else if count > 100 {
		count = 100
	}
	pg.limitCount = count
	return pg
}

// Distance 设置距离阈值
func (pg *PGVector) Distance(dist float64) *PGVector {
	pg.distance = dist
	return pg
}

// Rerank 启用rerank重排序
func (pg *PGVector) Rerank(score ...float64) *PGVector {
	pg.useRerank = true
	if len(score) > 0 {
		pg.rerankScore = score[0]
	}
	return pg
}

// Data 设置要保存的数据
func (pg *PGVector) Data(data w.Map) *PGVector {
	pg.dataMap = data
	return pg
}

// SplitLength 设置内容分割长度（100-3000）
func (pg *PGVector) SplitLength(length int) *PGVector {
	if length < 100 {
		length = 100
	} else if length > 3000 {
		length = 3000
	}
	pg.splitLength = length
	return pg
}

// EmbeddingModel 设置embedding模型
func (pg *PGVector) EmbeddingModel(model string) *PGVector {
	pg.embeddingModel = model
	return pg
}

// _getTableNames 获取表名配置
func (pg *PGVector) _getTableNames() *TableNames {
	base := pg.tableName
	if base == "" {
		base = "pgvector_search" // 默认生产环境表名
	}

	return &TableNames{
		Base:    base,
		Content: base + "_content",
		Split:   base + "_split",
	}
}

// _buildWhereDB 构建带where条件的数据库对象
func (pg *PGVector) _buildWhereDB(tableName string) *vant2.DBModel {
	db := vant2.DB(tableName, pg.dbName)

	// 应用where条件
	for _, condition := range pg.whereConditions {
		if condStr, ok := condition["condition"]; ok {
			// 字符串条件
			if args, hasArgs := condition["args"]; hasArgs {
				db = db.Where(condStr, args)
			} else {
				db = db.Where(condStr)
			}
		} else {
			// Map条件
			db = db.Where(condition)
		}
	}

	return db
}

// _validateRequired 验证必需参数
func (pg *PGVector) _validateRequired() error {
	if pg.tableName == "" {
		return errors.New("表名不能为空")
	}
	return nil
}
