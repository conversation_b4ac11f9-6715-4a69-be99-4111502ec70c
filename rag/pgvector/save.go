package pgvector

import (
	"assistant/rag/embedding"
	"errors"
	"regexp"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// ExtractTextFromMultimodal 从多模态内容中提取纯文本
func ExtractTextFromMultimodal(content interface{}) string {
	if content == nil {
		return ""
	}

	// 如果是字符串，直接返回
	if str, ok := content.(string); ok {
		return str
	}

	// 如果是数组（多模态格式）
	if arr, ok := content.([]interface{}); ok {
		var textParts []string
		for _, part := range arr {
			if partMap := gconv.Map(part); partMap != nil {
				if partType := gconv.String(partMap["type"]); partType == "text" {
					if text := gconv.String(partMap["text"]); text != "" {
						textParts = append(textParts, text)
					}
				}
			}
		}
		if len(textParts) > 0 {
			return strings.Join(textParts, " ")
		}
	}

	// 转换为通用 Map 格式处理
	contentMap := gconv.Map(content)
	if contentMap == nil {
		return gconv.String(content)
	}

	// 检查是否是包含 text 字段的对象
	if text := gconv.String(contentMap["text"]); text != "" {
		return text
	}

	// 检查是否是包含 content 字段的对象
	if innerContent := contentMap["content"]; innerContent != nil {
		return ExtractTextFromMultimodal(innerContent)
	}

	// 其他情况转换为字符串
	return gconv.String(content)
}

// Save 保存数据（插入或更新）
func (pg *PGVector) Save() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector Save 验证失败:", err)
		return false
	}

	if pg.dataMap == nil || len(pg.dataMap) == 0 {
		vant2.Error("PGVector Save 数据不能为空")
		return false
	}

	// 检查是插入还是更新
	if len(pg.whereConditions) == 0 {
		return pg._insert()
	} else {
		return pg._update()
	}
}

// SaveModel 保存模型数据（根据model和aid判断插入或更新）
func (pg *PGVector) SaveModel() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector SaveModel 验证失败:", err)
		return false
	}

	if pg.dataMap == nil || len(pg.dataMap) == 0 {
		vant2.Error("PGVector SaveModel 数据不能为空")
		return false
	}

	model := vant2.LodashGetString(pg.dataMap, "model")
	aid := gconv.Int64(pg.dataMap["aid"])

	if model == "" || aid == 0 {
		vant2.Error("PGVector SaveModel model和aid不能为空")
		return false
	}

	// 检查记录是否存在（检查基础表和内容表）
	tables := pg._getTableNames()
	baseCount := vant2.DB(tables.Base, pg.dbName).Where(w.Map{
		"model": model,
		"aid":   aid,
	}).Count()
	contentCount := vant2.DB(tables.Content, pg.dbName).Where(w.Map{
		"model": model,
		"aid":   aid,
	}).Count()

	if baseCount > 0 || contentCount > 0 {
		// 更新
		pg.whereConditions = []w.Map{{
			"model": model,
			"aid":   aid,
		}}
		return pg._update()
	} else {
		// 插入
		return pg._insert()
	}
}

// Insert 强制插入数据
func (pg *PGVector) Insert() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector Insert 验证失败:", err)
		return false
	}

	if pg.dataMap == nil || len(pg.dataMap) == 0 {
		vant2.Error("PGVector Insert 数据不能为空")
		return false
	}

	return pg._insert()
}

// Update 强制更新数据
func (pg *PGVector) Update() bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector Update 验证失败:", err)
		return false
	}

	if pg.dataMap == nil || len(pg.dataMap) == 0 {
		vant2.Error("PGVector Update 数据不能为空")
		return false
	}

	if len(pg.whereConditions) == 0 {
		vant2.Error("PGVector Update 必须提供where条件")
		return false
	}

	return pg._update()
}

// _insert 插入数据（3表策略：简单场景用基础表，复杂场景用内容表+分割表）
func (pg *PGVector) _insert() bool {
	// 准备数据
	baseData, contentData, splitData, err := pg._prepareData()
	if err != nil {
		vant2.Error("PGVector _insert 准备数据失败:", err)
		return false
	}

	tables := pg._getTableNames()
	success := true

	// 简单场景：使用基础表
	if baseData != nil {
		if !pg._insertToTable(tables.Base, baseData) {
			success = false
		}
	}

	// 复杂场景：使用内容表+分割表
	if contentData != nil {
		if !pg._insertToTable(tables.Content, contentData) {
			success = false
		}
	}

	if splitData != nil && len(splitData) > 0 {
		for _, splitItem := range splitData {
			if !pg._insertToTable(tables.Split, splitItem) {
				success = false
			}
		}
	}

	return success
}

// _update 更新数据（3表策略：简单场景用基础表，复杂场景用内容表+分割表）
func (pg *PGVector) _update() bool {
	// 分割模式下，从where条件中提取model和aid到dataMap中
	if pg.splitLength > 0 {
		pg._extractModelAidFromWhere()
	}

	// 准备数据
	baseData, contentData, splitData, err := pg._prepareData()
	if err != nil {
		vant2.Error("PGVector _update 准备数据失败:", err)
		return false
	}

	tables := pg._getTableNames()
	success := true

	// 简单场景：更新基础表
	if baseData != nil {
		if !pg._updateTable(tables.Base, baseData) {
			success = false
		}
	}

	// 复杂场景：更新内容表+分割表
	if contentData != nil {
		if !pg._updateTable(tables.Content, contentData) {
			success = false
		}
	}

	if splitData != nil && len(splitData) > 0 {
		// 删除旧的分割数据
		pg._deleteFromTable(tables.Split)

		// 插入新的分割数据
		for _, splitItem := range splitData {
			if !pg._insertToTable(tables.Split, splitItem) {
				success = false
			}
		}
	}

	return success
}

// _prepareData 准备保存数据（完全分离两套逻辑）
func (pg *PGVector) _prepareData() (w.Map, w.Map, []w.Map, error) {
	// 获取内容字段（支持多模态）
	var rawContent interface{}
	if embContent := pg.dataMap["embedding_content"]; embContent != nil {
		rawContent = embContent
	} else {
		rawContent = pg.dataMap[pg.contentName]
	}

	// 提取文本用于处理和embedding
	content := ExtractTextFromMultimodal(rawContent)

	// 清理content字段（删除首尾空白，将p和br转换为换行符，删除HTML标签）
	if content != "" {
		content = pg._cleanContent(content)
	}

	// 判断存储策略：完全分离两套逻辑
	if pg.splitLength > 0 && content != "" && len(content) > pg.splitLength {
		// 分割场景：使用内容表+分割表
		return pg._prepareSplitData(content)
	} else {
		// 简单场景：使用基础表
		return pg._prepareBaseData(content)
	}
}

// _prepareBaseData 准备基础表数据（简单场景）
func (pg *PGVector) _prepareBaseData(content string) (w.Map, w.Map, []w.Map, error) {
	// 生成embedding向量
	embeddingVector, err := pg._generateEmbedding(content)
	if err != nil {
		return nil, nil, nil, err
	}

	// 基础表数据：完整的业务字段+向量
	baseData := pg._copyMap(pg.dataMap)

	// 设置默认值
	if baseData["time_create"] == nil {
		baseData["time_create"] = vant2.Time()
	}
	if baseData["is_delete"] == nil {
		baseData["is_delete"] = 0
	}

	// 设置向量
	baseData[pg.vectorName] = pg._formatVector(embeddingVector)

	return baseData, nil, nil, nil
}

// _prepareSplitData 准备分割数据（复杂场景）
func (pg *PGVector) _prepareSplitData(content string) (w.Map, w.Map, []w.Map, error) {
	// 获取model和aid
	model := vant2.LodashGetString(pg.dataMap, "model")
	aid := gconv.Int64(pg.dataMap["aid"])

	if model == "" || aid == 0 {
		return nil, nil, nil, errors.New("分割场景下model和aid不能为空")
	}

	// 内容表数据：纯业务字段，无vector，让数据库触发器处理time_create
	contentData := w.Map{
		"user_id":     pg.dataMap["user_id"],
		"model":       model,
		"aid":         aid,
		"content_raw": pg.dataMap["content_raw"],
		"title":       pg.dataMap["title"],
		"extra":       pg.dataMap["extra"],
		"channel":     pg.dataMap["channel"],
		"status":      pg.dataMap["status"],
		"group_id":    pg.dataMap["group_id"],
		"tags":        pg.dataMap["tags"],
		"is_delete":   0,
	}

	// 分割内容并准备分割表数据
	parts := pg._splitContent(content)
	splitData := make([]w.Map, len(parts))

	for i, part := range parts {
		// 为每个分割片段生成embedding
		partVector, err := embedding.EmbeddingText(part)
		if err != nil {
			vant2.Error("PGVector 分割片段embedding失败:", err)
			continue
		}

		// 分割表数据：只有核心字段
		splitData[i] = w.Map{
			"vector":      pg._formatVector(partVector),
			"model":       model,
			"aid":         aid,
			"content":     part,
			"split_index": i + 1,
		}
	}

	return nil, contentData, splitData, nil
}

// _formatVector 格式化向量为PostgreSQL字符串
func (pg *PGVector) _formatVector(embeddingVector interface{}) string {
	var embStr string

	switch embValue := embeddingVector.(type) {
	case string:
		if len(embValue) > 0 {
			if embValue[0] == '{' && embValue[len(embValue)-1] == '}' {
				embStr = "[" + embValue[1:len(embValue)-1] + "]"
			} else if embValue[0] == '[' && embValue[len(embValue)-1] == ']' {
				embStr = embValue
			} else {
				embStr = "[" + embValue + "]"
			}
		}
	case []interface{}:
		embStr = "["
		for i, v := range embValue {
			if i > 0 {
				embStr += ", "
			}
			embStr += gconv.String(v)
		}
		embStr += "]"
	case []float64:
		embStr = "["
		for i, v := range embValue {
			if i > 0 {
				embStr += ", "
			}
			embStr += gconv.String(v)
		}
		embStr += "]"
	default:
		embStr = gconv.String(embeddingVector)
		if len(embStr) > 0 && embStr[0] != '[' {
			embStr = "[" + embStr + "]"
		}
	}

	return embStr
}

// _insertToTable 插入到指定表
func (pg *PGVector) _insertToTable(tableName string, data w.Map) bool {
	if data == nil || len(data) == 0 {
		return true
	}

	db := vant2.DB(tableName, pg.dbName)
	result, err := db.Data(data).Insert()

	if err != nil {
		vant2.Error("PGVector _insertToTable 插入失败:", tableName, err)
		return false
	}

	affected, err := result.RowsAffected()
	if err != nil {
		vant2.Error("PGVector _insertToTable 获取影响行数失败:", tableName, err)
		return false
	}

	vant2.Primary("PGVector _insertToTable 插入成功:", tableName, "影响行数:", affected)
	return affected > 0
}

// _updateTable 更新指定表
func (pg *PGVector) _updateTable(tableName string, data w.Map) bool {
	if data == nil || len(data) == 0 {
		return true
	}

	// 检查表是否存在，如果不存在就跳过
	if !pg._tableExists(tableName) {
		return true
	}

	db := pg._buildWhereDB(tableName)
	result := db.Data(data).Update()

	vant2.Primary("PGVector _updateTable 更新成功:", tableName, "结果:", result)
	return result
}

// _generateSummary 生成内容摘要
func (pg *PGVector) _generateSummary(content string) string {
	// 简单截取前200字符作为摘要
	runes := []rune(content)
	if len(runes) <= 200 {
		return content
	}
	return string(runes[:200]) + "..."
}

// _copyMap 复制Map
func (pg *PGVector) _copyMap(original w.Map) w.Map {
	copied := make(w.Map)
	for key, value := range original {
		copied[key] = value
	}
	return copied
}

// SaveFlag 快速更新标志字段（更新基础表或内容表）
func (pg *PGVector) SaveFlag(flag w.Map) bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector SaveFlag 验证失败:", err)
		return false
	}

	if flag == nil || len(flag) == 0 {
		vant2.Error("PGVector SaveFlag 标志数据不能为空")
		return false
	}

	if len(pg.whereConditions) == 0 {
		vant2.Error("PGVector SaveFlag 必须提供where条件")
		return false
	}

	tables := pg._getTableNames()
	success := true

	// 尝试更新基础表
	baseDB := pg._buildWhereDB(tables.Base)
	if !baseDB.Data(flag).Update() {
		success = false
	}

	// 尝试更新内容表
	contentDB := pg._buildWhereDB(tables.Content)
	if !contentDB.Data(flag).Update() {
		success = false
	}

	return success
}

// SaveFlagByModel 根据model和aid更新标志字段
func (pg *PGVector) SaveFlagByModel(model string, aid int64, flag w.Map) bool {
	pg.whereConditions = []w.Map{{
		"model": model,
		"aid":   aid,
	}}
	return pg.SaveFlag(flag)
}

// UpdateStatus 更新状态
func (pg *PGVector) UpdateStatus(status string) bool {
	return pg.SaveFlag(w.Map{"status": status})
}

// UpdateTags 更新标签
func (pg *PGVector) UpdateTags(tags string) bool {
	return pg.SaveFlag(w.Map{"tags": tags})
}

// UpdateChannel 更新频道
func (pg *PGVector) UpdateChannel(channel string) bool {
	return pg.SaveFlag(w.Map{"channel": channel})
}

// BatchInsert 批量插入数据
func (pg *PGVector) BatchInsert(dataList []w.Map) bool {
	if err := pg._validateRequired(); err != nil {
		vant2.Error("PGVector BatchInsert 验证失败:", err)
		return false
	}

	if dataList == nil || len(dataList) == 0 {
		vant2.Error("PGVector BatchInsert 数据列表不能为空")
		return false
	}

	success := true
	for _, data := range dataList {
		// 创建新的实例避免状态干扰
		newPG := NewDB().
			TableName(pg.tableName).
			DBName(pg.dbName).
			VectorName(pg.vectorName).
			ContentName(pg.contentName).
			EmbeddingModel(pg.embeddingModel).
			SplitLength(pg.splitLength).
			Data(data)

		if !newPG.Insert() {
			success = false
		}
	}

	return success
}

// CloneWithData 复制实例并设置新数据
func (pg *PGVector) CloneWithData(data w.Map) *PGVector {
	return &PGVector{
		tableName:       pg.tableName,
		dbName:          pg.dbName,
		vectorName:      pg.vectorName,
		contentName:     pg.contentName,
		limitCount:      pg.limitCount,
		embeddingModel:  pg.embeddingModel,
		splitLength:     pg.splitLength,
		dataMap:         data,
		whereConditions: make([]w.Map, 0),
	}
}

// _splitContent 分割内容
func (pg *PGVector) _splitContent(content string) []string {
	if pg.splitLength <= 0 || len(content) <= pg.splitLength {
		return []string{content}
	}

	var parts []string
	runes := []rune(content) // 使用rune切片来正确处理中文字符

	for i := 0; i < len(runes); i += pg.splitLength {
		end := i + pg.splitLength
		if end > len(runes) {
			end = len(runes)
		}
		parts = append(parts, string(runes[i:end]))
	}

	return parts
}

// _cleanContent 清理content字段：删除首尾空白，将p和br转换为换行符，删除HTML标签
func (pg *PGVector) _cleanContent(content string) string {
	if content == "" {
		return content
	}

	// 1. 删除首尾空白
	content = strings.TrimSpace(content)

	// 2. 将p和br标签转换为换行符
	// 先处理自闭合的br标签
	content = regexp.MustCompile(`<br\s*/?>`).ReplaceAllString(content, "\n")
	content = regexp.MustCompile(`<BR\s*/?>`).ReplaceAllString(content, "\n")

	// 处理p标签的开始和结束
	content = regexp.MustCompile(`<p[^>]*>`).ReplaceAllString(content, "")
	content = regexp.MustCompile(`</p>`).ReplaceAllString(content, "\n")
	content = regexp.MustCompile(`<P[^>]*>`).ReplaceAllString(content, "")
	content = regexp.MustCompile(`</P>`).ReplaceAllString(content, "\n")

	// 3. 删除所有HTML标签
	content = regexp.MustCompile(`<[^>]*>`).ReplaceAllString(content, "")

	// 4. 清理多余的换行符和空白
	content = regexp.MustCompile(`\n\s*\n`).ReplaceAllString(content, "\n") // 多个换行变成一个
	content = regexp.MustCompile(`\n\s+`).ReplaceAllString(content, "\n")   // 换行后的空格删除
	content = strings.TrimSpace(content)                                    // 再次删除首尾空白

	return content
}

// _generateEmbedding 生成embedding向量
func (pg *PGVector) _generateEmbedding(content string) (interface{}, error) {
	if pg.embedding != nil {
		return pg.embedding, nil
	}

	if content == "" {
		return nil, errors.New("内容为空，无法生成embedding")
	}

	// 限制内容长度
	if len(content) > 8000 {
		content = content[:8000]
	}

	vector, err := embedding.EmbeddingText(content)
	if err != nil {
		return nil, errors.New("embedding生成失败: " + err.Error())
	}

	return vector, nil
}

// _extractModelAidFromWhere 从where条件中提取model和aid到dataMap中（用于分割模式更新）
func (pg *PGVector) _extractModelAidFromWhere() {
	if pg.dataMap == nil {
		pg.dataMap = make(w.Map)
	}

	// 遍历where条件，提取model和aid
	for _, condition := range pg.whereConditions {
		if condition == nil {
			continue
		}

		// 检查是否有model和aid字段
		if model, exists := condition["model"]; exists && model != nil {
			if pg.dataMap["model"] == nil { // 如果dataMap中没有设置model，则使用where中的
				pg.dataMap["model"] = model
			}
		}

		if aid, exists := condition["aid"]; exists && aid != nil {
			if pg.dataMap["aid"] == nil { // 如果dataMap中没有设置aid，则使用where中的
				pg.dataMap["aid"] = aid
			}
		}
	}
}
