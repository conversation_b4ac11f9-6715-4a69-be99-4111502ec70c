-- PGVector三表架构SQL创建脚本
--
-- 表1: pgvector_search - 基础表，存储核心信息和向量索引
-- 表2: pgvector_search_content - 内容表，存储文档业务信息（无vector）
-- 表3: pgvector_search_split - 分割表，存储分割内容和向量（只有核心字段）

-- ==========================================
-- 基础表：pgvector_search
-- ==========================================
CREATE TABLE IF NOT EXISTS pgvector_search (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,                    -- 向量字段
    user_id INTEGER NOT NULL,                        -- 用户ID
    time_create INTEGER NOT NULL,                    -- 创建时间(10位时间戳)
    time_delete INTEGER DEFAULT 0,                   -- 删除时间(10位时间戳)
    is_delete INTEGER DEFAULT 0,                     -- 软删除标识 0=正常 1=已删除
    model VARCHAR(255) NOT NULL,                     -- 关联表名
    aid INTEGER NOT NULL,                            -- 关联表ID
    content TEXT,                                    -- 向量对应的内容
    content_raw TEXT,                                -- 用户显示内容
    title VARCHAR(500) DEFAULT '',                   -- 标题
    extra JSONB DEFAULT '{}',                        -- 扩展字段(JSON格式)
    channel VARCHAR(255) DEFAULT '',                 -- 分组频道
    status VARCHAR(255) DEFAULT 'active',            -- 状态
    group_id INTEGER DEFAULT 0,                      -- 分组ID
    tags TEXT DEFAULT '',                            -- 标签
    embedding_content TEXT,                          -- 用于embedding的内容（优先级最高）

    -- 索引
    CONSTRAINT pgvector_search_model_aid_key UNIQUE(model, aid)
);

-- 基础表索引
CREATE INDEX IF NOT EXISTS idx_pgvector_search_vector ON pgvector_search USING hnsw (vector vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_user_id ON pgvector_search (user_id);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_model_aid ON pgvector_search (model, aid);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_status ON pgvector_search (status);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_channel ON pgvector_search (channel);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_group_id ON pgvector_search (group_id);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_is_delete ON pgvector_search (is_delete);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_time_create ON pgvector_search (time_create);

-- ==========================================
-- 内容表：pgvector_search_content - 存储文档业务信息（无vector）
-- ==========================================
CREATE TABLE IF NOT EXISTS pgvector_search_content (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,                        -- 用户ID
    time_create INTEGER NOT NULL,                    -- 创建时间(10位时间戳)
    time_delete INTEGER DEFAULT 0,                   -- 删除时间(10位时间戳)
    is_delete INTEGER DEFAULT 0,                     -- 软删除标识 0=正常 1=已删除
    model VARCHAR(255) NOT NULL,                     -- 关联表名
    aid INTEGER NOT NULL,                            -- 关联表ID
    content_raw TEXT,                                -- 用户显示内容（文档标题等）
    title VARCHAR(500) DEFAULT '',                   -- 标题
    extra JSONB DEFAULT '{}',                        -- 扩展字段(JSON格式)
    channel VARCHAR(255) DEFAULT '',                 -- 分组频道
    status VARCHAR(255) DEFAULT 'active',            -- 状态
    group_id INTEGER DEFAULT 0,                      -- 分组ID
    tags TEXT DEFAULT '',                            -- 标签

    -- 索引
    CONSTRAINT pgvector_search_content_model_aid_key UNIQUE(model, aid)
);

-- 内容表索引
CREATE INDEX IF NOT EXISTS idx_pgvector_search_content_user_id ON pgvector_search_content (user_id);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_content_model_aid ON pgvector_search_content (model, aid);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_content_is_delete ON pgvector_search_content (is_delete);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_content_channel ON pgvector_search_content (channel);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_content_status ON pgvector_search_content (status);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_content_group_id ON pgvector_search_content (group_id);

-- ==========================================
-- 分割表：pgvector_search_split - 存储分割内容和向量（只有核心字段）
-- ==========================================
CREATE TABLE IF NOT EXISTS pgvector_search_split (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,                    -- 向量字段
    model VARCHAR(255) NOT NULL,                     -- 关联表名
    aid INTEGER NOT NULL,                            -- 关联表ID
    content TEXT NOT NULL,                           -- 分割片段内容
    split_index INTEGER NOT NULL DEFAULT 1,          -- 分割片段索引

    -- 外键约束：关联到内容表
    FOREIGN KEY (model, aid) REFERENCES pgvector_search_content(model, aid) ON DELETE CASCADE
);

-- 分割表索引
CREATE INDEX IF NOT EXISTS idx_pgvector_search_split_vector ON pgvector_search_split USING hnsw (vector vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_split_model_aid ON pgvector_search_split (model, aid);
CREATE INDEX IF NOT EXISTS idx_pgvector_search_split_index ON pgvector_search_split (split_index);

-- ==========================================
-- 通用函数和触发器
-- ==========================================

-- 更新时间戳函数 (确保返回10位时间戳)
CREATE OR REPLACE FUNCTION update_time_create()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.time_create IS NULL OR NEW.time_create = 0 THEN
        -- 确保返回10位时间戳(秒级别，不是毫秒)
        NEW.time_create = FLOOR(EXTRACT(EPOCH FROM NOW()))::INTEGER;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 基础表时间戳触发器
CREATE TRIGGER trigger_pgvector_search_time_create
    BEFORE INSERT ON pgvector_search
    FOR EACH ROW
    EXECUTE FUNCTION update_time_create();

-- 内容表时间戳触发器
CREATE TRIGGER trigger_pgvector_search_content_time_create
    BEFORE INSERT ON pgvector_search_content
    FOR EACH ROW
    EXECUTE FUNCTION update_time_create();

-- 分割表不需要时间戳触发器（没有time_create字段）

-- ==========================================
-- QA问答表：pgvector_qa - 专门用于问答库存储
-- ==========================================
CREATE TABLE IF NOT EXISTS pgvector_qa (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,                    -- 向量字段(基于q|a合并内容生成)
    user_id INTEGER NOT NULL,                        -- 用户ID
    time_create INTEGER NOT NULL,                    -- 创建时间(10位时间戳)
    time_delete INTEGER DEFAULT 0,                   -- 删除时间(10位时间戳)
    is_delete INTEGER DEFAULT 0,                     -- 软删除标识 0=正常 1=已删除
    model VARCHAR(255) NOT NULL,                     -- 关联表名
    aid INTEGER NOT NULL,                            -- 关联表ID
    q TEXT NOT NULL,                                 -- 问题内容
    a TEXT NOT NULL,                                 -- 答案内容
    remark TEXT DEFAULT '',                          -- 备注说明
    title VARCHAR(500) DEFAULT '',                   -- 标题
    extra JSONB DEFAULT '{}',                        -- 扩展字段(JSON格式)
    channel VARCHAR(255) DEFAULT '',                 -- 分组频道
    status VARCHAR(255) DEFAULT 'active',            -- 状态
    group_id INTEGER DEFAULT 0,                      -- 分组ID
    tags TEXT DEFAULT '',                            -- 标签

    -- 索引
    CONSTRAINT pgvector_qa_model_aid_key UNIQUE(model, aid)
);

-- QA表索引
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_vector ON pgvector_qa USING hnsw (vector vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_user_id ON pgvector_qa (user_id);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_model_aid ON pgvector_qa (model, aid);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_status ON pgvector_qa (status);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_channel ON pgvector_qa (channel);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_group_id ON pgvector_qa (group_id);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_is_delete ON pgvector_qa (is_delete);
CREATE INDEX IF NOT EXISTS idx_pgvector_qa_time_create ON pgvector_qa (time_create);

-- QA表时间戳触发器
CREATE TRIGGER trigger_pgvector_qa_time_create
    BEFORE INSERT ON pgvector_qa
    FOR EACH ROW
    EXECUTE FUNCTION update_time_create();

-- ==========================================
-- 使用说明
-- ==========================================

/*
生产环境表结构说明：

1. 三表设计（智能存储策略）：
   - pgvector_search：基础表，简单内容直接存储，一个表搞定
   - pgvector_search_content：内容表，复杂内容的业务信息，无vector字段
   - pgvector_search_split：分割表，复杂内容的分割片段和vector

2. 存储策略：
   - 简单场景（无splitLength或内容短）：只使用基础表
   - 复杂场景（有splitLength且内容长）：使用内容表+分割表
   - 业务字段在基础表/内容表，向量检索在基础表/分割表

3. 查询策略：
   - 策略1 SelectSplit(true)：返回去重文档，每个文档附带最相似段落
   - 策略2 SelectSplit(false)：返回所有匹配段落，同一文档可能多个段落

4. API使用示例：
   // 简单内容保存（使用基础表）
   pg.Data(g.Map{"content": "短文本", "model": "demo", "aid": 1}).SaveModel()

   // 复杂内容保存（使用内容表+分割表）
   pg.SplitLength(500).Data(g.Map{"content": "长文本...", "model": "demo", "aid": 2}).SaveModel()

   // 策略1：查询文档列表（去重）
   results := pg.Where(g.Map{"model": "book"}).Query("人工智能").SelectSplit(true)

   // 策略2：查询所有段落（可重复文档）
   results := pg.Where(g.Map{"model": "book"}).Query("深度学习").SelectSplit(false)

5. 向量维度：默认1024，可根据embedding模型调整

6. 索引说明：
   - vector索引使用HNSW算法，适合向量相似度搜索
   - 业务字段索引在文档表，向量索引在分割表
   - model+aid组合唯一约束确保数据一致性

7. QA问答表说明：
   - pgvector_qa：专门用于问答库存储
   - q字段存储问题，a字段存储答案，remark字段存储备注
   - vector字段基于"q|a"合并内容生成（取前8000字符）
   - 支持链式调用：NewQA().Where().Query().Limit().Rerank().List()
   - 使用示例：
     // 保存QA
     pg.NewQA().Data(g.Map{"q": "什么是AI?", "a": "人工智能...", "model": "faq", "aid": 1}).SaveQA()

     // 查询QA
     results := pg.NewQA().Where(g.Map{"model": "faq"}).Query("人工智能").Limit(5).List()
*/