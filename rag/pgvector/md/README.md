# PGVector 向量数据库操作包

## 📖 简介

PGVector是一个专为PostgreSQL向量数据库设计的Go操作包，提供完整的CRUD操作、语义搜索、智能分割存储等功能。支持链式调用，使用简单而功能强大。

## 📋 API概览

### 🔧 配置方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `NewDB()` | - | 创建实例 | `pgvector.NewDB()` |
| `TableName(name)` | string | 设置表名前缀 | `pg.TableName("my_search")` |
| `DBName(name)` | string | 设置数据库名 | `pg.DBName("custom_db")` |
| `VectorName(name)` | string | 设置向量字段名 | `pg.VectorName("embedding")` |
| `ContentName(name)` | string | 设置内容字段名 | `pg.ContentName("text")` |
| `EmbeddingModel(model)` | string | 设置embedding模型 | `pg.EmbeddingModel("BAAI/bge-m3")` |

### 🎯 查询条件方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `Where(condition, args...)` | interface{}, ...interface{} | 设置查询条件 | `pg.Where(g.Map{"status": "active"})` |
| `Query(text)` | string | 语义搜索文本 | `pg.Query("人工智能")` |
| `Embedding(emb)` | interface{} | 手动设置向量 | `pg.Embedding(customVector)` |
| `Limit(count)` | int | 限制结果数量(1-100) | `pg.Limit(10)` |
| `Distance(dist)` | float64 | 设置距离阈值 | `pg.Distance(0.8)` |
| `Rerank(score...)` | ...float64 | 启用智能重排序 | `pg.Rerank(0.1)` |

### 🎨 数据方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `Data(data)` | w.Map | 设置保存数据 | `pg.Data(g.Map{"content": "文本"})` |
| `SplitLength(length)` | int | 启用分割存储(100-3000) | `pg.SplitLength(500)` |

### 💾 CRUD操作方法
| 方法 | 参数 | 返回值 | 说明 | 示例 |
|------|------|--------|------|------|
| `Save()` | - | bool | 智能保存(插入/更新) | `pg.Data(data).Save()` |
| `Insert()` | - | bool | 强制插入 | `pg.Data(data).Insert()` |
| `SaveModel()` | - | bool | 根据model+aid智能保存 | `pg.Data(data).SaveModel()` |
| `Update()` | - | bool | 强制更新 | `pg.Where(condition).Data(data).Update()` |
| `List()` | - | w.SliceMap | 查询列表 | `pg.Where(condition).List()` |
| `SelectSplit(uniqueDoc)` | bool | w.SliceMap | 分割表查询 | `pg.Query("AI").SelectSplit(true)` |
| `One()` | - | w.Map | 查询单条 | `pg.Where(g.Map{"id": 1}).One()` |
| `Count()` | - | int | 统计数量 | `pg.Where(condition).Count()` |
| `Delete()` | - | bool | 物理删除 | `pg.Where(condition).Delete()` |
| `Remove()` | - | bool | 软删除 | `pg.Where(condition).Remove()` |

### 🚀 便捷方法
| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `SaveFlag(flag)` | w.Map | bool | 快速更新标志字段 |
| `SaveFlagByModel(model, aid, flag)` | string, int64, w.Map | bool | 根据model+aid更新标志 |
| `DeleteById(id)` | int64 | bool | 根据ID物理删除 |
| `RemoveById(id)` | int64 | bool | 根据ID软删除 |
| `BatchInsert(dataList)` | []w.Map | bool | 批量插入 |

### 📊 核心数据字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `content` | string | ✓ | 主要内容(用于向量化) |
| `model` | string | ✓ | 关联表名 |
| `aid` | int | ✓ | 关联表ID |
| `content_raw` | string | - | 显示标题/摘要 |
| `title` | string | - | 标题 |
| `extra` | json | - | 扩展字段(JSON格式) |
| `user_id` | int | - | 用户ID |
| `channel` | string | - | 分组频道 |
| `status` | string | - | 状态标识 |
| `group_id` | int | - | 分组ID |
| `tags` | string | - | 标签字符串 |
| `embedding_content` | string | - | 专用embedding内容(优先级最高) |

## 🚀 快速开始

### 基础使用
```go
import "openai/rag/pgvector"

// 创建实例
pg := pgvector.NewDB()

// 简单保存
success := pg.Data(g.Map{
    "content": "这是一段测试内容",
    "title":   "测试文档",
    "extra":   `{"litpic":"https://example.com/demo.jpg","writer":"作者"}`,
    "model":   "demo",
    "aid":     1,
}).SaveModel()
```

### 在线测试
访问 `/pgvector` 路径可以看到完整的CRUD测试界面，所有功能都可以在线测试。

## 🏗️ 架构设计

### 三表存储策略
PGVector采用智能三表存储架构：

1. **基础表** (`pgvector_search`) - 存储简单内容，一表搞定
2. **内容表** (`pgvector_search_content`) - 存储复杂内容的业务信息
3. **分割表** (`pgvector_search_split`) - 存储分割片段和向量

**存储规则**：
- 简单内容(无分割) → 只使用基础表
- 复杂内容(有分割) → 使用内容表 + 分割表

## 📚 完整API参考

### 🔧 基础配置方法

#### NewDB() *PGVector
创建新的PGVector实例
```go
pg := pgvector.NewDB()
```

#### TableName(name string) *PGVector
设置表名前缀
```go
pg.TableName("my_search") // 生成: my_search, my_search_content, my_search_split
```

#### DBName(name string) *PGVector
设置数据库名
```go
pg.DBName("custom_db") // 默认: vsearch
```

#### VectorName(name string) *PGVector
设置向量字段名
```go
pg.VectorName("embedding") // 默认: vector
```

#### ContentName(name string) *PGVector
设置内容字段名
```go
pg.ContentName("text") // 默认: content
```

#### EmbeddingModel(model string) *PGVector
设置embedding模型
```go
pg.EmbeddingModel("BAAI/bge-m3") // 默认模型
```

### 🎯 查询条件方法

#### Where(condition interface{}, args ...interface{}) *PGVector
添加查询条件
```go
// Map形式
pg.Where(g.Map{"user_id": 123, "status": "active"})

// 字符串形式
pg.Where("user_id = ? AND status = ?", 123, "active")
```

#### Query(text string) *PGVector
设置语义搜索文本(自动生成embedding)
```go
pg.Query("人工智能和机器学习")
```

#### Embedding(emb interface{}) *PGVector
设置预设embedding向量(手动传入)
```go
embedding := []float64{0.1, 0.2, 0.3...} // 1024维向量
pg.Embedding(embedding)
```

#### Limit(count int) *PGVector
设置查询数量限制(1-100)
```go
pg.Limit(10) // 最多返回10条
```

#### Distance(dist float64) *PGVector
设置向量距离阈值
```go
pg.Distance(0.8) // 只返回距离小于0.8的结果
```

#### Rerank(score ...float64) *PGVector
启用智能重排序
```go
pg.Rerank()        // 使用默认分数
pg.Rerank(0.1)     // 设置最小重排序分数
```

### 🎨 数据操作方法

#### Data(data w.Map) *PGVector
设置要保存的数据
```go
pg.Data(g.Map{
    "content":     "文章内容",
    "content_raw": "文章标题",
    "title":       "AI技术详解",
    "extra":       `{"litpic":"https://example.com/cover.jpg","writer":"张三","category":"技术"}`,
    "user_id":     123,
    "model":       "article",
    "aid":         456,
    "channel":     "tech",
    "status":      "active",
    "group_id":    1,
    "tags":        "AI,ML",
})
```

**核心字段说明**：
- `content` - 主要内容(用于向量化)
- `content_raw` - 显示标题/摘要
- `model` - 关联表名(必填)
- `aid` - 关联表ID(必填)
- `user_id` - 用户ID
- `channel` - 分组频道
- `status` - 状态标识
- `group_id` - 分组ID
- `tags` - 标签字符串
- `embedding_content` - 专用于embedding的内容(优先级最高)

#### SplitLength(length int) *PGVector
启用内容分割存储(100-3000字符)
```go
pg.SplitLength(500) // 每500字符分割一次
```

## 💾 CRUD操作详解

### ➕ 新增操作

#### Save() bool
智能保存(有where条件=更新，无=插入)
```go
success := pg.Data(data).Save()
```

#### Insert() bool
强制插入
```go
success := pg.Data(data).Insert()
```

#### SaveModel() bool
根据model+aid智能保存
```go
success := pg.Data(g.Map{
    "model": "article",
    "aid":   123,
    "content": "新内容",
}).SaveModel()
```

#### BatchInsert(dataList []w.Map) bool
批量插入
```go
dataList := []w.Map{
    {"content": "内容1", "model": "test", "aid": 1},
    {"content": "内容2", "model": "test", "aid": 2},
}
success := pg.BatchInsert(dataList)
```

### 🔍 查询操作

#### List() w.SliceMap
查询列表(支持向量搜索)
```go
// 普通查询
results := pg.Where(g.Map{"model": "article"}).List()

// 语义搜索
results := pg.Where(g.Map{"model": "article"}).Query("人工智能").List()

// 手动embedding搜索
results := pg.Embedding(customVector).List()

// 高级搜索(带重排序)
results := pg.Query("AI").Rerank(0.1).Limit(5).List()
```

#### SelectSplit(uniqueDoc bool) w.SliceMap
分割表专用查询
```go
// 策略1: 去重文档(每个文档只返回最相似的段落)
results := pg.Query("深度学习").SelectSplit(true)

// 策略2: 所有段落(返回所有匹配的段落)
results := pg.Query("深度学习").SelectSplit(false)
```

#### One() w.Map
查询单条记录
```go
record := pg.Where(g.Map{"id": 123}).One()
```

#### Count() int
统计数量
```go
count := pg.Where(g.Map{"status": "active"}).Count()
```

### ✏️ 更新操作

#### Update() bool
强制更新
```go
success := pg.Where(g.Map{"id": 123}).Data(g.Map{
    "status": "updated",
}).Update()
```

#### SaveFlag(flag w.Map) bool
快速更新标志字段
```go
success := pg.Where(g.Map{"id": 123}).SaveFlag(g.Map{
    "status": "published",
    "tags":   "热门",
})
```

#### SaveFlagByModel(model string, aid int64, flag w.Map) bool
根据model+aid更新标志
```go
success := pg.SaveFlagByModel("article", 456, g.Map{
    "status": "archived",
})
```

### 🗑️ 删除操作

#### Delete() bool
物理删除(删除三表数据)
```go
success := pg.Where(g.Map{"id": 123}).Delete()
```

#### Remove() bool
软删除(设置is_delete=1)
```go
success := pg.Where(g.Map{"id": 123}).Remove()
```

#### DeleteById(id int64) bool
根据ID物理删除
```go
success := pg.DeleteById(123)
```

#### RemoveById(id int64) bool
根据ID软删除
```go
success := pg.RemoveById(123)
```

## 🎪 高级功能

### 智能分割存储
当内容长度超过设定的分割长度时，自动启用三表存储：

```go
longContent := "这是一篇很长的文章..." // 超过500字符

success := pg.SplitLength(500).Data(g.Map{
    "content":     longContent,
    "content_raw": "文章标题",
    "model":       "article",
    "aid":         123,
}).SaveModel()

// 自动存储到：
// 1. content表 - 业务信息
// 2. split表 - 多个分割片段，每个都有独立向量
```

### 语义搜索与重排序
```go
// 基础语义搜索
results := pg.Query("人工智能发展趋势").Limit(10).List()

// 带重排序的高精度搜索
results := pg.Query("深度学习算法").Rerank(0.1).Limit(5).List()

// 分割表精确搜索
results := pg.Query("神经网络").SelectSplit(true).Limit(3)
```

### 手动Embedding搜索
```go
// 使用预先计算好的向量
customEmbedding := []float64{0.1, 0.2, ...} // 1024维
results := pg.Embedding(customEmbedding).Limit(5).List()
```

## 🔧 在线测试工具

访问 `/pgvector` 可以使用完整的测试界面：

### 🔧 初始化功能
- **初始化测试数据** - 准备测试环境
- **清理数据** - 清除测试数据

### ➕ 新增功能
- **基础新增** - 简单内容保存
- **新增带分割** - 长内容自动分割保存

### 🔍 查询功能
- **普通查询** - 基础条件查询
- **语义搜索** - 文本语义向量搜索
- **手动Embedding搜索** - 使用预设向量搜索
- **Rerank高级查询** - 智能重排序搜索

### 📋 高级查询
- **策略1:去重文档** - 基于分割表检索，返回去重文档
- **策略2:所有段落** - 基于分割表检索，返回所有匹配段落

### ✏️ 修改删除
- **修改数据** - 更新基础数据
- **修改带分割数据** - 更新长内容(重新分割)
- **删除数据** - 物理删除记录

### 🔧 调试工具
- **查看三表数据** - 查看所有表的原始数据

## 📊 数据表结构

### 基础表 (pgvector_search)
```sql
CREATE TABLE pgvector_search (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,           -- 向量字段
    user_id INTEGER NOT NULL,               -- 用户ID
    time_create INTEGER NOT NULL,           -- 创建时间(时间戳)
    time_delete INTEGER DEFAULT 0,          -- 删除时间
    is_delete INTEGER DEFAULT 0,            -- 软删除标识
    model VARCHAR(255) NOT NULL,            -- 关联表名
    aid INTEGER NOT NULL,                   -- 关联表ID
    content TEXT,                           -- 内容
    content_raw TEXT,                       -- 显示标题
    title VARCHAR(500) DEFAULT '',          -- 标题
    extra JSONB DEFAULT '{}',               -- 扩展字段(JSON格式)
    channel VARCHAR(255) DEFAULT '',        -- 频道
    status VARCHAR(255) DEFAULT 'active',   -- 状态
    group_id INTEGER DEFAULT 0,             -- 分组ID
    tags TEXT DEFAULT '',                   -- 标签
    embedding_content TEXT,                 -- 专用embedding内容

    UNIQUE(model, aid)
);
```

### 内容表 (pgvector_search_content)
```sql
CREATE TABLE pgvector_search_content (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,               -- 用户ID
    time_create INTEGER NOT NULL,           -- 创建时间
    time_delete INTEGER DEFAULT 0,          -- 删除时间
    is_delete INTEGER DEFAULT 0,            -- 软删除标识
    model VARCHAR(255) NOT NULL,            -- 关联表名
    aid INTEGER NOT NULL,                   -- 关联表ID
    content_raw TEXT,                       -- 显示标题
    title VARCHAR(500) DEFAULT '',          -- 标题
    extra JSONB DEFAULT '{}',               -- 扩展字段(JSON格式)
    channel VARCHAR(255) DEFAULT '',        -- 频道
    status VARCHAR(255) DEFAULT 'active',   -- 状态
    group_id INTEGER DEFAULT 0,             -- 分组ID
    tags TEXT DEFAULT '',                   -- 标签

    UNIQUE(model, aid)
);
```

### 分割表 (pgvector_search_split)
```sql
CREATE TABLE pgvector_search_split (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,           -- 向量字段
    model VARCHAR(255) NOT NULL,            -- 关联表名
    aid INTEGER NOT NULL,                   -- 关联表ID
    content TEXT NOT NULL,                  -- 分割片段内容
    split_index INTEGER NOT NULL DEFAULT 1, -- 分割索引

    FOREIGN KEY (model, aid) REFERENCES pgvector_search_content(model, aid) ON DELETE CASCADE
);
```

## ⚡ 性能优化建议

### 1. 向量索引
确保向量字段有HNSW索引：
```sql
CREATE INDEX idx_vector ON pgvector_search USING hnsw (vector vector_cosine_ops);
```

### 2. 查询优化
- 使用适当的Limit限制结果数量
- 合理设置Distance阈值过滤无关结果
- 分割长文档以提高搜索精度

### 3. 分割策略
- 一般文档：300-800字符分割
- 技术文档：200-500字符分割
- 对话内容：100-300字符分割

## 🚨 常见问题

### Q: 为什么会出现"model和aid不能为空"错误？
**A**: 在使用分割存储时，model和aid是必填字段。确保在Data()中提供这两个字段。

### Q: 分割表查询和普通查询有什么区别？
**A**: 分割表查询(`SelectSplit`)专门针对长文档的分割片段进行搜索，精度更高；普通查询(`List`)在基础表中搜索完整文档。

### Q: 如何选择合适的分割长度？
**A**: 建议根据内容类型调整：代码片段200-400字符，一般文章400-800字符，长文档800-1500字符。

### Q: Rerank功能什么时候使用？
**A**: 当需要更精确的语义匹配时使用，特别是在候选结果较多的情况下，可以进一步提升搜索精度。

## 🔗 相关链接

- **项目地址**: [openai/rag/pgvector](.)
- **在线测试**: `/pgvector`
- **Embedding服务**: [openai/rag/embedding](../embedding)
- **数据库配置**: [tables.sql](tables.sql)

---

📝 **最后更新**: 2025年1月
🏷️ **版本**: v1.0.0
👨‍💻 **维护者**: PGVector团队