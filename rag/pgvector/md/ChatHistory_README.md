# ChatHistory 聊天历史管理包

## 📖 简介

ChatHistory是专为聊天系统设计的Go操作包，提供智能历史记录管理、语义搜索、完整对话扩展等功能。支持链式调用，使用简单而功能强大。

## 📋 API概览

### 🔧 配置方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `ChatHistoryDB()` | - | 创建实例 | `pgvector.ChatHistoryDB()` |
| `DBName(name)` | string | 设置数据库名 | `ch.DBName("custom_db")` |

### 🎯 查询条件方法
| 方法 | 参数 | 说明 | 示例 |
|------|------|------|------|
| `Where(condition)` | w.Map | 设置查询条件 | `ch.Where(w.Map{"user_id": 123})` |
| `Query(text, extraLimit...)` | string, ...int | 语义搜索(文本, 相似对话数量) | `ch.Query("人工智能", 5)` |
| `Embedding(emb)` | interface{} | 手动设置向量 | `ch.Embedding(customVector)` |
| `Distance(dist)` | float64 | 设置距离阈值 | `ch.Distance(0.8)` |
| `Rerank(score)` | float64 | 启用重排序 | `ch.Rerank(0.1)` |
| `FullChat()` | - | 扩展完整对话 | `ch.FullChat()` |

### 💾 CRUD操作方法
| 方法 | 参数 | 返回值 | 说明 | 示例 |
|------|------|--------|------|------|
| `List(recentCount...)` | ...int | w.SliceMap | 查询对话列表 | `ch.List(5)` |
| `ListAndMessages(recentCount...)` | ...int | []Message | 查询并生成消息格式 | `ch.ListAndMessages(3)` |
| `MessageList()` | - | []Message | 生成大模型上下文 | `ch.MessageList()` |
| `Save()` | - | bool | 智能保存 | `ch.Data(data).Save()` |
| `Delete()` | - | bool | 软删除 | `ch.Where(condition).Delete()` |
| `DeleteById(id)` | int64 | bool | 根据ID删除 | `ch.DeleteById(123)` |

### 🚀 便捷方法
| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `SaveMessage(userId, channelId, conversationId, role, content, extra...)` | int64, int64, int64, string, string, ...string | bool | 快速保存消息 |
| `GetRecentConversations(userId, channelId, limit)` | int64, int64, int | w.SliceMap | 获取最近对话组 |
| `Data(data)` | w.Map | *ChatHistory | 设置保存数据 |

### 📊 核心数据字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `user_id` | int64 | ✓ | 用户ID |
| `channel_id` | int64 | ✓ | 频道ID |
| `conversation_id` | int64 | ✓ | 对话组ID |
| `role` | string | ✓ | 角色(user/assistant/system) |
| `content` | string | ✓ | 对话内容 |
| `extra` | json | - | 扩展信息(JSON格式) |
| `tokens` | int | - | token数量 |

## 🚀 快速开始

### 基础使用
```go
import "openai/rag/pgvector"

// 创建实例
ch := pgvector.ChatHistoryDB()

// 保存用户消息
success := ch.SaveMessage(
    123,   // user_id
    456,   // channel_id
    789,   // conversation_id
    "user",
    "什么是人工智能？",
    `{"model":"gpt-4"}`, // 可选扩展信息
)

// 保存助手回复
success = ch.SaveMessage(123, 456, 789, "assistant", "人工智能是...")
```

### 在线测试
访问 `/pgvector/chat` 路径可以看到完整的测试界面，所有功能都可以在线测试。

## 📚 完整API参考

### 🔧 基础配置方法

#### ChatHistoryDB() *ChatHistory
创建新的ChatHistory实例
```go
ch := pgvector.ChatHistoryDB()
```

#### DBName(name string) *ChatHistory
设置数据库名
```go
ch.DBName("custom_db") // 默认: vsearch
```

### 🎯 查询条件方法

#### Where(condition w.Map) *ChatHistory
添加查询条件
```go
ch.Where(w.Map{"user_id": 123, "channel_id": 456})
```

#### Query(text string, extraLimit ...int) *ChatHistory
设置语义搜索文本
```go
ch.Query("人工智能")       // 默认相似对话数量5
ch.Query("人工智能", 3)    // 相似对话数量3
```

#### Embedding(emb interface{}) *ChatHistory
设置预设embedding向量
```go
embedding := []float64{0.1, 0.2, 0.3...} // 1024维向量
ch.Embedding(embedding)
```

#### Distance(dist float64) *ChatHistory
设置向量距离阈值
```go
ch.Distance(0.8) // 只返回距离小于0.8的结果
```

#### Rerank(score float64) *ChatHistory
启用智能重排序
```go
ch.Rerank(0.1) // 设置最小重排序分数
```

#### FullChat() *ChatHistory
启用完整对话扩展(user-assistant配对)
```go
ch.FullChat() // 自动扩展为完整对话对
```

### 💾 CRUD操作详解

#### List(recentCount ...int) w.SliceMap
查询对话列表
```go
// 最近5条对话
results := ch.List(5)

// 条件查询最近3条
results := ch.Where(w.Map{"user_id": 123}).List(3)

// 智能检索: 最近3条 + 相似2条
results := ch.Where(w.Map{"user_id": 123}).Query("AI", 2).List(3)

// 高级查询: 相似+重排序+完整对话
results := ch.Query("深度学习").Rerank(0.1).FullChat().List(5)
```

#### ListAndMessages(recentCount ...int) []Message
一步完成查询并生成消息格式
```go
// 直接生成大模型上下文
messages := ch.Where(w.Map{"user_id": 123}).Query("AI").FullChat().ListAndMessages(3)
```

#### MessageList() []Message
生成大模型消息格式
```go
ch.List(5) // 先查询
messages := ch.MessageList() // 再生成消息格式

// 返回格式:
// [
//   {"role": "system", "content": "对话辅助记忆系统..."},
//   {"role": "user", "content": "用户问题"},
//   {"role": "assistant", "content": "AI回答"},
// ]
```

#### Save() bool
智能保存(有ID=更新，无ID=插入)
```go
success := ch.Data(w.Map{
    "user_id":         123,
    "channel_id":      456,
    "conversation_id": 789,
    "role":            "user",
    "content":         "什么是AI？",
    "extra":           `{"model":"gpt-4"}`,
}).Save()
```

#### SaveMessage(userId, channelId, conversationId, role, content, extra) bool
快速保存消息
```go
success := ch.SaveMessage(
    123,    // user_id
    456,    // channel_id
    789,    // conversation_id
    "user", // role
    "什么是人工智能？", // content
    `{"model":"gpt-4"}`, // extra(可选)
)
```

#### Delete() bool
软删除(条件删除)
```go
success := ch.Where(w.Map{"user_id": 123, "conversation_id": 789}).Delete()
```

#### DeleteById(id int64) bool
根据ID软删除
```go
success := ch.DeleteById(123)
```

#### GetRecentConversations(userId, channelId, limit) w.SliceMap
获取最近对话组
```go
conversations := ch.GetRecentConversations(123, 456, 10)
// 返回: conversation_id, last_time, message_count
```

## 🎪 核心功能详解

### 智能检索(最新+相似)
系统会同时获取最新对话和相似历史对话，并自动去重：
```go
// 最新3条 + 相似2条 = 最多5条(去重后)
results := ch.Where(w.Map{"user_id": 123}).Query("人工智能", 2).List(3)
```

### 完整对话扩展
基于ID关系自动扩展user-assistant配对：
```go
// 如果相似度召回了 {"id":5, "role":"user"}
// 系统会自动查找 {"id":6, "role":"assistant"}
// 如果召回了 {"id":8, "role":"assistant"}
// 系统会自动查找 {"id":7, "role":"user"}
results := ch.Query("深度学习").FullChat().List(5)
```

### 大模型上下文生成
自动生成符合大模型标准的消息格式：
```go
messages := ch.Query("AI").FullChat().ListAndMessages(3)

// 生成结果:
// 1. 如有相似对话 -> system角色提示
// 2. 最新对话 -> 按时间正序的user/assistant对话
```

## 📊 数据表结构

### pgvector_chat_history
```sql
CREATE TABLE pgvector_chat_history (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,              -- 向量字段
    user_id BIGINT NOT NULL,                   -- 用户ID
    channel_id BIGINT NOT NULL DEFAULT 0,      -- 频道ID
    conversation_id BIGINT NOT NULL DEFAULT 0, -- 对话组ID
    role VARCHAR(20) NOT NULL DEFAULT 'user',  -- 角色
    content TEXT NOT NULL,                     -- 对话内容
    time_create BIGINT NOT NULL,               -- 创建时间(10位时间戳)
    is_delete SMALLINT DEFAULT 0,              -- 软删除标识
    tokens INTEGER DEFAULT 0,                  -- token数量
    extra JSONB DEFAULT '{}'                   -- 扩展信息
);
```

### 重要索引
```sql
-- 向量索引(相似度搜索)
CREATE INDEX idx_chat_history_vector ON pgvector_chat_history USING hnsw (vector vector_cosine_ops);

-- 复合索引(常用查询)
CREATE INDEX idx_chat_history_user_channel_time ON pgvector_chat_history (user_id, channel_id, time_create DESC) WHERE is_delete = 0;
```

## 🔧 在线测试工具

访问 `/pgvector/chat` 可以使用完整的测试界面：

### 🔧 数据管理
- **清空聊天数据** - 清除测试数据
- **初始化20条对话** - 创建测试数据(10轮user-assistant对话)
- **查看原始数据** - 查看数据库原始数据

### 🔍 基础查询
- **基础List查询** - `ChatHistoryDB().List(5)`
- **条件Where查询** - `ChatHistoryDB().Where({...}).List(3)`
- **最近对话组** - `GetRecentConversations()`

### 🧠 智能检索
- **Query相似性查询** - 最新+相似对话
- **自定义Embedding查询** - 使用预设向量
- **设置距离阈值查询** - 过滤无关结果

### 🎯 高级功能
- **Rerank重排序** - 智能重排序
- **FullChat完整对话** - user-assistant配对
- **MessageList上下文** - 生成大模型格式
- **复杂查询演示** - 完整链式调用

### 💾 CRUD操作
- **保存用户消息** - SaveMessage示例
- **保存助手回复** - SaveMessage示例
- **修改消息** - 更新已存在消息
- **删除消息** - 软删除示例

## ⚡ 性能优化建议

### 1. 查询优化
- 合理设置最新对话数量(建议≤10)
- 使用Distance阈值过滤无关结果
- 适当使用Rerank提高精度

### 2. 索引利用
- user_id + channel_id 组合查询性能最佳
- 时间戳索引支持快速获取最新对话
- 向量索引支持高速相似度搜索

### 3. 数据管理
- 定期清理软删除数据
- conversation_id 合理分组管理
- extra字段存储适量扩展信息

## 🚨 常见问题

### Q: 如何确保user-assistant配对完整？
**A**: 使用`FullChat()`方法，系统会自动基于ID关系(±1)扩展完整对话对。

### Q: 相似度搜索和最新对话有重复怎么办？
**A**: 系统自动去重，相似度搜索会排除最新对话中已存在的记录。

### Q: MessageList生成的格式是什么？
**A**: 标准的大模型消息格式，包含system提示(如有相似对话)和user/assistant对话序列。

### Q: 如何选择合适的参数？
**A**:
- 最新对话数量: 3-10条
- 相似对话数量: 2-5条
- 距离阈值: 0.6-1.2
- Rerank分数: 0.05-0.2

### Q: conversation_id如何管理？
**A**: 同一轮对话使用相同conversation_id，新对话开始时生成新的conversation_id。

## 🔗 相关链接

- **项目地址**: [openai/rag/pgvector](.)
- **在线测试**: `/pgvector/chat`
- **数据库配置**: [pgvector_chat_history.sql](pgvector_chat_history.sql)
- **PGVector包**: [README.md](README.md)

---

📝 **最后更新**: 2025年1月
🏷️ **版本**: v1.0.0
👨‍💻 **维护者**: ChatHistory团队