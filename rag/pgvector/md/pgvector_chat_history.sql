-- PGVector聊天历史记录表
-- 基于pgvector理念重新设计，简化字段，提高效率

CREATE TABLE IF NOT EXISTS pgvector_chat_history (
    id SERIAL PRIMARY KEY,
    vector vector(1024) NOT NULL,                    -- 向量字段（用于相似度检索）
    user_id BIGINT NOT NULL,                         -- 用户ID
    channel_id BIGINT NOT NULL DEFAULT 0,            -- 分组/频道ID
    conversation_id BIGINT NOT NULL DEFAULT 0,       -- 对话组ID（同一轮对话的唯一标识）
    role VARCHAR(20) NOT NULL DEFAULT 'user',        -- 角色：user|assistant|system
    content TEXT NOT NULL,                           -- 对话内容
    time_create BIGINT NOT NULL,                     -- 创建时间(10位时间戳)
    is_delete SMALLINT DEFAULT 0,                    -- 软删除标识 0=正常 1=已删除

    -- 简化后的token字段（可选，用于统计）
    tokens INTEGER DEFAULT 0,                        -- token数量（合并prompt+completion）

    -- 扩展字段（存储模型、温度等配置）
    extra JSONB DEFAULT '{}'                         -- 扩展信息：{"model":"gpt-4","temperature":0.7}
);

-- 索引设计（基于查询模式优化）
CREATE INDEX IF NOT EXISTS idx_chat_history_vector ON pgvector_chat_history USING hnsw (vector vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_chat_history_user_channel ON pgvector_chat_history (user_id, channel_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_conversation ON pgvector_chat_history (conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_time_create ON pgvector_chat_history (time_create DESC);
CREATE INDEX IF NOT EXISTS idx_chat_history_is_delete ON pgvector_chat_history (is_delete);
CREATE INDEX IF NOT EXISTS idx_chat_history_role ON pgvector_chat_history (role);

-- 复合索引（优化常见查询）
CREATE INDEX IF NOT EXISTS idx_chat_history_user_channel_time ON pgvector_chat_history (user_id, channel_id, time_create DESC) WHERE is_delete = 0;
CREATE INDEX IF NOT EXISTS idx_chat_history_user_channel_role ON pgvector_chat_history (user_id, channel_id, role) WHERE is_delete = 0;

-- 时间戳触发器（确保10位时间戳）
CREATE OR REPLACE FUNCTION update_chat_time_create()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.time_create IS NULL OR NEW.time_create = 0 THEN
        NEW.time_create = FLOOR(EXTRACT(EPOCH FROM NOW()))::BIGINT;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_chat_history_time_create
    BEFORE INSERT ON pgvector_chat_history
    FOR EACH ROW
    EXECUTE FUNCTION update_chat_time_create();



-- 示例数据注释
/*
典型的聊天记录结构：
{
  "id": 1,
  "user_id": 123,
  "channel_id": 456,
  "conversation_id": 1642723200001,
  "role": "user",
  "content": "什么是人工智能？",
  "time_create": 1642723200,
  "extra": {"model": "gpt-4", "temperature": 0.7}
}

完整对话示例：
conversation_id=1001: user问题 -> assistant回答
conversation_id=1002: user问题 -> assistant回答
conversation_id=1003: user问题 -> assistant回答

查询逻辑：
1. 最新对话：按time_create DESC排序
2. 相似对话：基于vector相似度检索
3. 完整对话：确保user-assistant配对完整
4. 上下文生成：转换为大模型标准格式
*/