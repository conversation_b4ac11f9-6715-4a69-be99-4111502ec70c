package docparse

// API配置常量
const (
	// Sophnet API配置
	SOPHNET_PROJECT_ID = "6jjKubtS4IaA6kQio7459f" // 项目ID
	SOPHNET_API_KEY    = "9WmhyHEzsX7LPvUhHSaDpZGXRQkEjvy9V6s0wZXTPgX8Q0-kMFznDYZ4VQv9I1so5eav0DjvH8hwczKT_Q3zRA"
	SOPHNET_EASYLLM_ID = "14l3Ur6xQjwhW3eef6gE0b" // Easyllm ID
	SOPHNET_OCR_ID     = "3SxSX2dDRwu9Obv32yZmY2" // Easyllm ID

	// PDF解析API地址
	PDF_PARSE_API_URL = "https://www.sophnet.com/api/open-apis/projects/" + SOPHNET_PROJECT_ID + "/easyllms/doc-parse"

	// 图片OCR API地址
	IMG_OCR_API_URL = "https://www.sophnet.com/api/open-apis/projects/" + SOPHNET_PROJECT_ID + "/easyllms/image-ocr"

	// 请求超时时间（秒）
	REQUEST_TIMEOUT = 30

	// 支持的文件格式
	MAX_FILE_SIZE = 6 * 1024 * 1024 // 6MB
)

// 支持的文件格式映射
var SUPPORTED_FORMATS = map[string]bool{
	// 文档格式
	"md":   true,
	"pdf":  true,
	"doc":  true,
	"docx": true,
	"txt":  true,
	"xlsx": true,
	"xls":  true,
	"ppt":  true,
	"pptx": true,
	// 图片格式
	"jpg":  true,
	"jpeg": true,
	"png":  true,
	"bmp":  true,
	"gif":  true,
	"webp": true,
}

// 解析结果结构
type ParseResult struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	TextResult string `json:"textResult"` // 纯文本结果
	HtmlResult string `json:"htmlResult"` // HTML格式结果
	RawContent string `json:"rawContent"` // 原始内容（保留格式）
	Format     string `json:"format"`     // 文件格式
	FileSize   int64  `json:"fileSize"`   // 文件大小
	ParseType  string `json:"parseType"`  // 解析类型：local/api
}
