package docparse

import (
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// PdfParser PDF解析器
type PdfParser struct{}

// ParsePdf 解析PDF文件
func (p *PdfParser) ParsePdf(filePath string) ParseResult {
	result := ParseResult{
		Format:    "pdf",
		ParseType: "api",
	}

	// 读取PDF文件
	fileData, err := ioutil.ReadFile(filePath)
	if err != nil {
		result.Message = fmt.Sprintf("读取PDF文件失败: %v", err)
		return result
	}

	return p.ParsePdfFromBytes(fileData, "document.pdf")
}

// ParsePdfFromBytes 从字节数据解析PDF
func (p *PdfParser) ParsePdfFromBytes(data []byte, filename string) ParseResult {
	result := ParseResult{
		Format:    "pdf",
		ParseType: "api",
		FileSize:  int64(len(data)),
	}

	// 检查文件大小限制
	if len(data) > MAX_FILE_SIZE {
		result.Message = fmt.Sprintf("文件大小超过限制，最大支持%.1fMB", float64(MAX_FILE_SIZE)/(1024*1024))
		return result
	}

	// 调用Sophnet API解析PDF
	apiResult := p._callPdfParseAPI(data, filename)
	if !apiResult.Success {
		result.Message = apiResult.Message
		return result
	}

	// API返回的是Markdown格式，需要转换为纯文本和HTML
	markdownContent := apiResult.TextResult

	// 使用Markdown解析器转换格式
	mdParser := &MdParser{}
	mdResult := mdParser.ParseMdFromContent(markdownContent)

	if mdResult.Success {
		result.RawContent = markdownContent // 保存原始Markdown内容
		result.TextResult = mdResult.TextResult
		result.HtmlResult = mdResult.HtmlResult
		result.Success = true
		result.Message = "PDF解析成功"
	} else {
		// 如果Markdown解析失败，直接使用原始文本
		result.RawContent = markdownContent // 保存原始内容
		result.TextResult = markdownContent
		result.HtmlResult = p._simpleTextToHtml(markdownContent)
		result.Success = true
		result.Message = "PDF解析成功（简单格式）"
	}

	vant2.Warning(g.Map{
		"file_size":     result.FileSize,
		"text_length":   len(result.TextResult),
		"html_length":   len(result.HtmlResult),
		"markdown_size": len(markdownContent),
	}, "=== PDF解析完成 ===")

	return result
}

// _callPdfParseAPI 调用PDF解析API
func (p *PdfParser) _callPdfParseAPI(fileData []byte, filename string) ParseResult {
	result := ParseResult{
		Format:    "pdf",
		ParseType: "api",
	}

	vant2.Warning(g.Map{
		"api_url":    PDF_PARSE_API_URL,
		"file_size":  len(fileData),
		"filename":   filename,
		"easyllm_id": SOPHNET_EASYLLM_ID,
	}, "=== 调用PDF解析API ===")

	// 创建multipart表单
	var body bytes.Buffer
	writer := multipart.NewWriter(&body)

	// 添加文件
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		result.Message = fmt.Sprintf("创建表单文件失败: %v", err)
		return result
	}
	_, err = io.Copy(part, bytes.NewReader(fileData))
	if err != nil {
		result.Message = fmt.Sprintf("写入文件数据失败: %v", err)
		return result
	}

	// 添加其他字段
	writer.WriteField("easyllm_id", SOPHNET_EASYLLM_ID)
	writer.Close()

	// 创建请求
	req, err := http.NewRequest("POST", PDF_PARSE_API_URL, &body)
	if err != nil {
		result.Message = fmt.Sprintf("创建请求失败: %v", err)
		return result
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+SOPHNET_API_KEY)

	// 发送请求
	client := &http.Client{Timeout: time.Duration(REQUEST_TIMEOUT) * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		result.Message = fmt.Sprintf("API请求失败: %v", err)
		return result
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Message = fmt.Sprintf("读取响应失败: %v", err)
		return result
	}

	vant2.Warning(string(responseBody), "=== PDF API响应 ===")

	// 解析JSON响应
	var responseMap map[string]interface{}
	err = gconv.Scan(string(responseBody), &responseMap)
	if err != nil {
		result.Message = fmt.Sprintf("解析响应JSON失败: %v", err)
		return result
	}

	// 检查是否有错误信息
	if errorMsg := gconv.String(responseMap["error"]); errorMsg != "" {
		result.Message = fmt.Sprintf("API调用失败: %s", errorMsg)
		return result
	}

	// 获取解析结果
	data := gconv.String(responseMap["data"])
	if data == "" {
		result.Message = "API返回数据为空"
		return result
	}

	result.TextResult = data
	result.Success = true
	result.Message = "PDF API解析成功"

	return result
}

// _simpleTextToHtml 简单的文本转HTML
func (p *PdfParser) _simpleTextToHtml(text string) string {
	if text == "" {
		return ""
	}

	html := `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        p { margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
<pre>` + text + `</pre>
</body>
</html>`

	return html
}
