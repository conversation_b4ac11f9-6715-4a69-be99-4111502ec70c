package docparse

import (
	"fmt"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// Controller 文档解析控制器
type Controller struct {
	parser *DocumentParser
}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{
		parser: NewDocumentParser(),
	}
}

// ParseIndex 文档解析测试首页 - 显示上传界面
func (c *Controller) ParseIndex(r *ghttp.Request) {
	html := `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>文档解析服务</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #007cba;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background: #f8feff;
            margin: 20px 0;
        }
        .upload-area:hover {
            background: #f0f8ff;
            border-color: #005a87;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            font-size: 16px;
        }
        .upload-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .upload-btn:hover {
            background: #005a87;
        }
        .format-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .result {
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #007cba;
            display: none;
        }
        .result.show {
            display: block;
        }
        .result.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .result.success {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        .tab-buttons {
            margin: 10px 0;
        }
        .tab-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            cursor: pointer;
            margin-right: 5px;
        }
        .tab-btn.active {
            background: #007cba;
        }
        .tab-content {
            display: none;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background: white;
        }
        .tab-content.active {
            display: block;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #007cba;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 智能文档解析服务</h1>

        <div class="format-info">
            <h3>🚀 支持的文件格式：</h3>
            <p><strong>文档类型：</strong> MD, PDF, DOC, DOCX, TXT, XLS, XLSX, PPT, PPTX</p>
            <p><strong>图片类型：</strong> JPG, JPEG, PNG, BMP, GIF, WEBP</p>
            <p><strong>文件大小：</strong> 最大支持 6MB</p>
            <p><strong>解析结果：</strong> 提供纯文本和HTML两种格式</p>
        </div>

        <div class="upload-area">
            <h3>📁 选择文件进行解析</h3>
            <input type="file" id="fileInput" accept=".md,.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.bmp,.gif,.webp" onchange="checkFileSize(this)">
            <br>
            <button class="upload-btn" onclick="uploadFile()">🔄 开始解析</button>
            <button class="upload-btn" onclick="clearResult()" style="background: #6c757d;">🗑️ 清空结果</button>
        </div>

        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="result" id="result">
            <h3 id="resultTitle">解析结果</h3>
            <div id="resultInfo"></div>

            <div class="tab-buttons">
                <button class="tab-btn active" onclick="showTab('raw', this)">📄 原始内容</button>
                <button class="tab-btn" onclick="showTab('text', this)">📝 纯文本</button>
                <button class="tab-btn" onclick="showTab('html', this)">🌐 HTML预览</button>
                <button class="tab-btn" onclick="showTab('info', this)">ℹ️ 详细信息</button>
            </div>

            <div class="tab-content active" id="rawContent">
                <pre id="rawResult" style="background: #f8f9fa; padding: 15px; border-radius: 4px; border: 1px solid #e9ecef;"></pre>
            </div>

            <div class="tab-content" id="textContent">
                <pre id="textResult"></pre>
            </div>

            <div class="tab-content" id="htmlContent">
                <iframe id="htmlResult" style="width: 100%; height: 400px; border: 1px solid #ddd;"></iframe>
            </div>

            <div class="tab-content" id="infoContent">
                <pre id="infoResult"></pre>
            </div>
        </div>
    </div>

    <script>
        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择文件');
                return;
            }

            // 检查文件大小 (6MB = 6 * 1024 * 1024)
            const maxSize = 6 * 1024 * 1024;
            if (file.size > maxSize) {
                const fileSize = formatFileSize(file.size);
                const errorMsg = '文件太大！当前文件大小: ' + fileSize + '，最大支持: 6MB';
                showError(errorMsg);
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            showProgress();

            fetch('/docparse/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data); // 添加调试日志
                hideProgress();
                showResult(data);
            })
            .catch(error => {
                console.error('Upload error:', error); // 添加调试日志
                hideProgress();
                showError('上传失败: ' + error.message);
            });
        }

        function showProgress() {
            document.getElementById('progress').style.display = 'block';
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 90) {
                    clearInterval(interval);
                }
                document.getElementById('progressBar').style.width = progress + '%';
            }, 200);
        }

        function hideProgress() {
            document.getElementById('progress').style.display = 'none';
            document.getElementById('progressBar').style.width = '100%';
            setTimeout(() => {
                document.getElementById('progressBar').style.width = '0%';
            }, 500);
        }

        function showResult(data) {
            console.log('Processing result:', data); // 调试日志
            const result = document.getElementById('result');
            const title = document.getElementById('resultTitle');

            // 检查响应结构
            if (data && data.back === 1) {
                result.className = 'result success show';
                title.textContent = '✅ 解析成功';

                // 安全地获取数据
                const resultData = data.data || {};
                const rawContent = resultData.rawContent || '无原始内容';
                const textResult = resultData.textResult || '无文本内容';
                const htmlResult = resultData.htmlResult || '<p>无HTML内容</p>';

                document.getElementById('rawResult').textContent = rawContent;
                document.getElementById('textResult').textContent = textResult;

                const htmlFrame = document.getElementById('htmlResult');
                const htmlContent = htmlResult;
                htmlFrame.src = 'data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent);

                const info = {
                    文件格式: resultData.format || '未知',
                    解析方式: resultData.parseType || '未知',
                    文件大小: formatFileSize(resultData.fileSize || 0),
                    原始长度: rawContent.length,
                    文本长度: textResult.length,
                    HTML长度: htmlResult.length,
                    成功状态: resultData.success ? '是' : '否',
                    解析消息: resultData.message || '无消息'
                };
                document.getElementById('infoResult').textContent = JSON.stringify(info, null, 2);

            } else {
                // 处理错误情况
                result.className = 'result error show';
                title.textContent = '❌ 解析失败';
                const errorMsg = (data && data.msg) || '解析失败，未知错误';
                document.getElementById('textResult').textContent = errorMsg;

                // 显示错误详情
                if (data && data.data) {
                    const errorInfo = {
                        错误代码: data.code || '未知',
                        错误消息: data.msg || '无消息',
                        详细信息: data.data
                    };
                    document.getElementById('infoResult').textContent = JSON.stringify(errorInfo, null, 2);
                }
            }

            // 重置到原始内容标签页
            try {
                const rawBtn = document.querySelector('.tab-btn');
                if (rawBtn) {
                    showTab('raw', rawBtn);
                }
            } catch (e) {
                console.error('Error setting active tab:', e);
            }
        }

        function showError(message) {
            const result = document.getElementById('result');
            const title = document.getElementById('resultTitle');

            result.className = 'result error show';
            title.textContent = '❌ 错误';
            document.getElementById('textResult').textContent = message;
        }

        function clearResult() {
            document.getElementById('result').className = 'result';
            document.getElementById('fileInput').value = '';
        }

        function showTab(tabName, buttonElement) {
            // 隐藏所有标签页
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName + 'Content').classList.add('active');

            // 高亮当前按钮
            if (buttonElement) {
                buttonElement.classList.add('active');
            }
        }

        function getTabDisplayName(tabName) {
            switch(tabName) {
                case 'raw': return '📄 原始内容';
                case 'text': return '📝 纯文本';
                case 'html': return '🌐 HTML预览';
                case 'info': return 'ℹ️ 详细信息';
                default: return '';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function checkFileSize(input) {
            const file = input.files[0];
            if (file) {
                const maxSize = 6 * 1024 * 1024; // 6MB
                const fileSize = formatFileSize(file.size);

                if (file.size > maxSize) {
                    alert('⚠️ 文件太大！\\n\\n当前文件: ' + fileSize + '\\n最大支持: 6MB\\n\\n请选择更小的文件。');
                    input.value = ''; // 清空文件选择
                } else {
                    console.log('✅ 文件大小检查通过: ' + fileSize);
                }
            }
        }
    </script>
</body>
</html>`

	r.Response.Header().Set("Content-Type", "text/html; charset=utf-8")
	r.Response.Write(html)
}

// ParseUpload 文件上传解析接口
func (c *Controller) ParseUpload(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	// 简单检查Content-Length大小限制(6MB)
	contentLength := r.GetHeader("Content-Length")
	if contentLength != "" {
		if size := gconv.Int64(contentLength); size > MAX_FILE_SIZE {
			_back.ApiError(-1, fmt.Sprintf("文件大小超过限制，最大支持%.1fMB",
				float64(MAX_FILE_SIZE)/(1024*1024)))
			return
		}
	}

	// 获取上传的文件 - GoFrame自动处理multipart解析
	uploadFile := r.GetUploadFile("file")
	if uploadFile == nil {
		_back.ApiError(-1, "未检测到上传文件")
		return
	}

	vant2.Warning(g.Map{
		"filename": uploadFile.Filename,
		"size":     uploadFile.Size,
		"header":   uploadFile.Header,
	}, "=== 文件上传信息 ===")

	// 检查文件大小
	if uploadFile.Size > MAX_FILE_SIZE {
		_back.ApiError(-1, fmt.Sprintf("文件大小超过限制，最大支持%.1fMB", float64(MAX_FILE_SIZE)/(1024*1024)))
		return
	}

	// 检查文件名
	if uploadFile.Filename == "" {
		_back.ApiError(-1, "文件名为空")
		return
	}

	// 读取文件内容
	fileContent, err := uploadFile.Open()
	if err != nil {
		_back.ApiError(-1, fmt.Sprintf("打开文件失败: %v", err))
		return
	}
	defer fileContent.Close()

	// 读取文件数据
	fileData := make([]byte, uploadFile.Size)
	_, err = fileContent.Read(fileData)
	if err != nil {
		_back.ApiError(-1, fmt.Sprintf("读取文件数据失败: %v", err))
		return
	}

	// 解析文档
	result := c.parser.ParseFromBytes(fileData, uploadFile.Filename)

	// 转换结果格式
	responseData := _back.ToSnake(g.Map{
		"success":    result.Success,
		"message":    result.Message,
		"textResult": result.TextResult,
		"htmlResult": result.HtmlResult,
		"rawContent": result.RawContent, // 新增原始内容字段
		"format":     result.Format,
		"fileSize":   result.FileSize,
		"parseType":  result.ParseType,
	})

	if result.Success {
		_back.ApiSuccess("文档解析成功", responseData)
	} else {
		_back.ApiError(-1, result.Message, responseData)
	}
}

// ParseFormats 获取支持的格式列表接口
func (c *Controller) ParseFormats(r *ghttp.Request) {
	_back := vant2.InitBack(r)

	formats := c.parser.GetSupportedFormats()

	// 分类格式
	formatGroups := g.Map{
		"documents": []string{"md", "pdf", "doc", "docx", "txt", "xls", "xlsx", "ppt", "pptx"},
		"images":    []string{"jpg", "jpeg", "png", "bmp", "gif", "webp"},
		"all":       formats,
	}

	_back.ApiSuccess("获取支持的格式成功", _back.ToSnake(formatGroups))
}

// ParseCheck 检查文件格式是否支持
func (c *Controller) ParseCheck(r *ghttp.Request) {
	_input := vant2.Input(r)
	_back := vant2.InitBack(r)

	format := _input.GetString("format")
	if format == "" {
		_back.ApiError(-1, "格式参数不能为空")
		return
	}

	// 转为小写并检查
	format = strings.ToLower(format)
	supported := c.parser.IsFormatSupported(format)

	result := g.Map{
		"format":    format,
		"supported": supported,
	}

	if supported {
		_back.ApiSuccess("格式检查完成", _back.ToSnake(result))
	} else {
		_back.ApiError(-1, "不支持的格式", _back.ToSnake(result))
	}
}
