package docparse

import (
	"fmt"
	"io/ioutil"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/russross/blackfriday/v2"
)

// MdParser Markdown解析器
type MdParser struct{}

// ParseMd 解析Markdown文件
func (p *MdParser) ParseMd(filePath string) ParseResult {
	result := ParseResult{
		Format:    "md",
		ParseType: "local",
	}

	// 读取文件内容
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		result.Message = fmt.Sprintf("读取文件失败: %v", err)
		return result
	}

	// 获取文件大小
	result.FileSize = int64(len(content))

	// 原始内容（保留完整Markdown格式）
	textContent := string(content)
	result.RawContent = textContent

	// 纯文本结果（保留基本格式）
	result.TextResult = p._cleanMarkdownToText(textContent)

	// HTML结果（使用blackfriday转换）
	htmlBytes := blackfriday.Run(content)
	result.HtmlResult = string(htmlBytes)

	result.Success = true
	result.Message = "Markdown解析成功"

	vant2.Warning(g.Map{
		"file_size":   result.FileSize,
		"text_length": len(result.TextResult),
		"html_length": len(result.HtmlResult),
		"raw_length":  len(result.RawContent),
	}, "=== Markdown解析完成 ===")

	return result
}

// ParseMdFromContent 从内容解析Markdown
func (p *MdParser) ParseMdFromContent(content string) ParseResult {
	result := ParseResult{
		Format:    "md",
		ParseType: "local",
		FileSize:  int64(len(content)),
	}

	// 原始内容（保留完整Markdown格式）
	result.RawContent = content

	// 纯文本结果
	result.TextResult = p._cleanMarkdownToText(content)

	// HTML结果
	htmlBytes := blackfriday.Run([]byte(content))
	result.HtmlResult = string(htmlBytes)

	result.Success = true
	result.Message = "Markdown内容解析成功"

	return result
}

// _cleanMarkdownToText 清理Markdown标记，转为纯文本（完全去除格式）
func (p *MdParser) _cleanMarkdownToText(content string) string {
	text := content

	// 移除标题标记
	text = strings.ReplaceAll(text, "# ", "")
	text = strings.ReplaceAll(text, "## ", "")
	text = strings.ReplaceAll(text, "### ", "")
	text = strings.ReplaceAll(text, "#### ", "")
	text = strings.ReplaceAll(text, "##### ", "")
	text = strings.ReplaceAll(text, "###### ", "")

	// 移除粗体和斜体标记
	text = strings.ReplaceAll(text, "**", "")
	text = strings.ReplaceAll(text, "*", "")
	text = strings.ReplaceAll(text, "__", "")
	text = strings.ReplaceAll(text, "_", "")

	// 移除代码块标记
	text = strings.ReplaceAll(text, "```", "")
	text = strings.ReplaceAll(text, "`", "")

	// 移除链接标记，保留链接文本
	for strings.Contains(text, "[") && strings.Contains(text, "](") {
		start := strings.Index(text, "[")
		if start == -1 {
			break
		}
		linkEnd := strings.Index(text[start:], "](")
		if linkEnd == -1 {
			break
		}
		urlEnd := strings.Index(text[start+linkEnd+2:], ")")
		if urlEnd == -1 {
			break
		}

		linkText := text[start+1 : start+linkEnd]
		text = text[:start] + linkText + text[start+linkEnd+2+urlEnd+1:]
	}

	// 移除图片标记，保留alt文本
	for strings.Contains(text, "![") && strings.Contains(text, "](") {
		start := strings.Index(text, "![")
		if start == -1 {
			break
		}
		linkEnd := strings.Index(text[start:], "](")
		if linkEnd == -1 {
			break
		}
		urlEnd := strings.Index(text[start+linkEnd+2:], ")")
		if urlEnd == -1 {
			break
		}

		altText := text[start+2 : start+linkEnd]
		text = text[:start] + altText + text[start+linkEnd+2+urlEnd+1:]
	}

	// 移除引用标记
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		if strings.HasPrefix(strings.TrimSpace(line), "> ") {
			lines[i] = strings.TrimSpace(line)[2:]
		}
	}
	text = strings.Join(lines, "\n")

	// 移除列表标记
	lines = strings.Split(text, "\n")
	for i, line := range lines {
		trimmed := strings.TrimSpace(line)
		// 移除无序列表标记
		if strings.HasPrefix(trimmed, "- ") || strings.HasPrefix(trimmed, "* ") || strings.HasPrefix(trimmed, "+ ") {
			lines[i] = strings.TrimSpace(trimmed[2:])
		}
		// 移除有序列表标记
		if len(trimmed) > 0 && trimmed[0] >= '0' && trimmed[0] <= '9' {
			dotIndex := strings.Index(trimmed, ". ")
			if dotIndex > 0 && dotIndex < 4 {
				lines[i] = strings.TrimSpace(trimmed[dotIndex+2:])
			}
		}
	}
	text = strings.Join(lines, "\n")

	// 清理多余的空行
	text = strings.ReplaceAll(text, "\n\n\n", "\n\n")

	return strings.TrimSpace(text)
}
