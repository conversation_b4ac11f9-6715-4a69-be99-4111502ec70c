package docparse

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"path/filepath"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// ImgParser 图片解析器
type ImgParser struct{}

// ParseImg 解析图片文件
func (p *ImgParser) ParseImg(filePath string) ParseResult {
	result := ParseResult{
		Format:    "img",
		ParseType: "api",
	}

	// 读取图片文件
	fileData, err := ioutil.ReadFile(filePath)
	if err != nil {
		result.Message = fmt.Sprintf("读取图片文件失败: %v", err)
		return result
	}

	// 获取文件扩展名判断图片格式
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != "" {
		ext = ext[1:] // 去掉点
	}

	return p.ParseImgFromBytes(fileData, ext)
}

// ParseImgFromBytes 从字节数据解析图片
func (p *ImgParser) ParseImgFromBytes(data []byte, imgFormat string) ParseResult {
	result := ParseResult{
		Format:    "img",
		ParseType: "api",
		FileSize:  int64(len(data)),
	}

	// 检查文件大小限制
	if len(data) > MAX_FILE_SIZE {
		result.Message = fmt.Sprintf("文件大小超过限制，最大支持%.1fMB", float64(MAX_FILE_SIZE)/(1024*1024))
		return result
	}

	// 检查图片格式
	if imgFormat == "" {
		imgFormat = "jpeg" // 默认格式
	}

	// 统一格式名称
	switch imgFormat {
	case "jpg":
		imgFormat = "jpeg"
	}

	// 调用OCR API解析图片
	return p._callImgOcrAPI(data, imgFormat)
}

// _callImgOcrAPI 调用图片OCR API
func (p *ImgParser) _callImgOcrAPI(imageData []byte, imageFormat string) ParseResult {
	result := ParseResult{
		Format:    "img",
		ParseType: "api",
	}

	// 将图片数据转换为base64
	base64Data := base64.StdEncoding.EncodeToString(imageData)
	base64URL := fmt.Sprintf("data:image/%s;base64,%s", imageFormat, base64Data)

	vant2.Warning(g.Map{
		"api_url":      IMG_OCR_API_URL,
		"image_size":   len(imageData),
		"image_format": imageFormat,
		"base64_size":  len(base64Data),
		"easyllm_id":   SOPHNET_OCR_ID,
	}, "=== 调用图片OCR API ===")

	// 严格按照API文档构建请求数据
	requestData := map[string]interface{}{
		"easyllm_id": SOPHNET_OCR_ID, // 图片OCR专用ID
		"type":       "image_url",    // 固定为"image_url"
		"image_url": map[string]interface{}{
			"url": base64URL, // base64格式图片
		},
		"use_doc_ori":  1, // 使用文档方向分类
		"use_table":    1, // 使用表格识别
		"use_html_out": 1, // 使用HTML输出（表格识别时必须为1）
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		result.Message = fmt.Sprintf("序列化请求数据失败: %v", err)
		return result
	}

	// 打印实际发送的请求数据（去掉base64内容以便查看）
	debugData := map[string]interface{}{
		"easyllm_id":   SOPHNET_OCR_ID,
		"type":         "image_url",
		"use_doc_ori":  1,
		"use_table":    1,
		"use_html_out": 1,
		"image_url": map[string]interface{}{
			"url": fmt.Sprintf("data:image/%s;base64,[%d bytes]", imageFormat, len(base64Data)),
		},
	}
	vant2.Warning(debugData, "=== OCR请求数据结构 ===")

	// 创建HTTP请求
	req, err := http.NewRequest("POST", IMG_OCR_API_URL, bytes.NewBuffer(jsonData))
	if err != nil {
		result.Message = fmt.Sprintf("创建HTTP请求失败: %v", err)
		return result
	}

	// 设置请求头 - 严格按照API文档
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+SOPHNET_API_KEY)

	// 打印请求详情
	vant2.Warning(g.Map{
		"url":          IMG_OCR_API_URL,
		"content_type": "application/json",
		"auth_header":  "Bearer " + SOPHNET_API_KEY[:20] + "...",
		"request_size": len(jsonData),
	}, "=== OCR HTTP请求详情 ===")

	// 创建HTTP客户端并发送请求
	client := &http.Client{
		Timeout: time.Duration(REQUEST_TIMEOUT) * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		result.Message = fmt.Sprintf("发送HTTP请求失败: %v", err)
		return result
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	vant2.Warning(g.Map{
		"status_code": resp.StatusCode,
		"status_text": resp.Status,
	}, "=== OCR HTTP响应状态 ===")

	if resp.StatusCode != 200 {
		// 读取错误响应体
		errorBody, _ := io.ReadAll(resp.Body)
		vant2.Error(g.Map{
			"status_code": resp.StatusCode,
			"error_body":  string(errorBody),
		}, "=== OCR API错误响应 ===")
		result.Message = fmt.Sprintf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(errorBody))
		return result
	}

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		result.Message = fmt.Sprintf("读取响应体失败: %v", err)
		return result
	}

	vant2.Warning(g.Map{
		"response_size":    len(responseBody),
		"response_preview": string(responseBody),
	}, "=== 图片OCR API完整响应 ===")

	// 解析JSON响应
	var responseMap map[string]interface{}
	err = json.Unmarshal(responseBody, &responseMap)
	if err != nil {
		result.Message = fmt.Sprintf("解析响应JSON失败: %v, 原始响应: %s", err, string(responseBody))
		return result
	}

	// 检查API状态
	status := gconv.Int(responseMap["status"])
	message := gconv.String(responseMap["message"])

	vant2.Warning(g.Map{
		"api_status":  status,
		"api_message": message,
	}, "=== OCR API业务状态 ===")

	if status != 0 {
		result.Message = fmt.Sprintf("OCR识别失败 (status=%d): %s", status, message)
		return result
	}

	// 获取识别结果
	resultArray := gconv.SliceMap(responseMap["result"])
	if len(resultArray) == 0 {
		result.Message = "OCR识别结果为空"
		return result
	}

	vant2.Warning(g.Map{
		"result_count": len(resultArray),
		"results":      resultArray,
	}, "=== OCR识别结果详情 ===")

	// 合并所有识别的文本
	var textBuilder strings.Builder
	var htmlBuilder strings.Builder

	// 开始HTML文档
	htmlBuilder.WriteString(`<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        .ocr-segment { margin: 10px 0; }
        .text-segment { border-left: 3px solid #007cba; padding-left: 10px; margin: 5px 0; }
        .table-segment { border: 1px solid #ddd; margin: 10px 0; padding: 10px; }
        .html-segment { background: #f9f9f9; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
`)

	for i, segment := range resultArray {
		label := gconv.String(segment["label"])
		texts := gconv.String(segment["texts"])
		position := gconv.SliceAny(segment["position"])

		if texts != "" {
			// 纯文本结果
			textBuilder.WriteString(texts)
			textBuilder.WriteString("\n")

			// HTML结果，根据类型分别处理
			switch label {
			case "table":
				htmlBuilder.WriteString(fmt.Sprintf(`<div class="ocr-segment table-segment">
					<h4>表格内容 %d</h4>
					<div>%s</div>
				</div>`, i+1, texts))
			case "html":
				htmlBuilder.WriteString(fmt.Sprintf(`<div class="ocr-segment html-segment">
					<h4>HTML内容 %d</h4>
					<div>%s</div>
				</div>`, i+1, texts))
			default:
				htmlBuilder.WriteString(fmt.Sprintf(`<div class="ocr-segment text-segment">
					<p>%s</p>
				</div>`, texts))
			}
		}

		// 记录位置信息（调试用）
		if len(position) >= 1 {
			vant2.Warning(g.Map{
				"segment":     i + 1,
				"label":       label,
				"position":    position,
				"text_length": len(texts),
			}, "=== OCR段落信息 ===")
		}
	}

	// 结束HTML文档
	htmlBuilder.WriteString("</body>\n</html>")

	result.TextResult = strings.TrimSpace(textBuilder.String())
	result.HtmlResult = htmlBuilder.String()
	result.RawContent = result.TextResult // 对于OCR，原始内容就是识别的文本
	result.Success = true
	result.Message = "图片OCR识别成功"

	vant2.Warning(g.Map{
		"segments":    len(resultArray),
		"text_length": len(result.TextResult),
		"html_length": len(result.HtmlResult),
	}, "=== 图片OCR解析完成 ===")

	return result
}
