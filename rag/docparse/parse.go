package docparse

import (
	"path/filepath"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
)

// DocumentParser 文档解析器主入口
type DocumentParser struct {
	mdParser  *MdParser
	pdfParser *PdfParser
	imgParser *ImgParser
}

// NewDocumentParser 创建文档解析器实例
func NewDocumentParser() *DocumentParser {
	return &DocumentParser{
		mdParser:  &MdParser{},
		pdfParser: &PdfParser{},
		imgParser: &ImgParser{},
	}
}

// ParseFile 解析文件 - 根据文件扩展名自动选择解析器
func (p *DocumentParser) ParseFile(filePath string) ParseResult {
	// 获取文件扩展名并转为小写
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != "" && ext[0] == '.' {
		ext = ext[1:] // 去掉点
	}

	vant2.Warning(g.Map{
		"file_path": filePath,
		"extension": ext,
	}, "=== 开始解析文档 ===")

	// 检查是否支持该格式
	if !SUPPORTED_FORMATS[ext] {
		return ParseResult{
			Success: false,
			Message: "不支持的文件格式: " + ext,
			Format:  ext,
		}
	}

	// 根据文件格式选择相应的解析器
	switch ext {
	case "md":
		return p.mdParser.ParseMd(filePath)
	case "doc", "docx", "pdf", "xlsx", "xls", "ppt", "pptx":
		// 所有Office格式和PDF都通过PDF API处理
		return p.pdfParser.ParsePdf(filePath)
	case "jpg", "jpeg", "png", "bmp", "gif", "webp":
		return p.imgParser.ParseImg(filePath)
	case "txt":
		// 简单的文本文件处理
		return p._parseTxtFile(filePath)
	default:
		return ParseResult{
			Success: false,
			Message: "暂不支持该文件格式: " + ext,
			Format:  ext,
		}
	}
}

// ParseFromBytes 从字节数据解析文档
func (p *DocumentParser) ParseFromBytes(data []byte, filename string) ParseResult {
	// 从文件名获取扩展名
	ext := strings.ToLower(filepath.Ext(filename))
	if ext != "" && ext[0] == '.' {
		ext = ext[1:]
	}

	vant2.Warning(g.Map{
		"filename":  filename,
		"extension": ext,
		"size":      len(data),
	}, "=== 开始解析文档数据 ===")

	// 检查是否支持该格式
	if !SUPPORTED_FORMATS[ext] {
		return ParseResult{
			Success: false,
			Message: "不支持的文件格式: " + ext,
			Format:  ext,
		}
	}

	// 根据文件格式选择相应的解析器
	switch ext {
	case "md":
		return p.mdParser.ParseMdFromContent(string(data))
	case "doc", "docx", "pdf", "xlsx", "xls", "ppt", "pptx":
		// 所有Office格式和PDF都通过PDF API处理
		return p.pdfParser.ParsePdfFromBytes(data, filename)
	case "jpg", "jpeg", "png", "bmp", "gif", "webp":
		return p.imgParser.ParseImgFromBytes(data, ext)
	case "txt":
		// 简单的文本文件处理
		return p._parseTxtFromBytes(data)
	default:
		return ParseResult{
			Success: false,
			Message: "暂不支持该文件格式: " + ext,
			Format:  ext,
		}
	}
}

// GetSupportedFormats 获取支持的文件格式列表
func (p *DocumentParser) GetSupportedFormats() []string {
	formats := make([]string, 0, len(SUPPORTED_FORMATS))
	for format := range SUPPORTED_FORMATS {
		formats = append(formats, format)
	}
	return formats
}

// IsFormatSupported 检查是否支持指定格式
func (p *DocumentParser) IsFormatSupported(format string) bool {
	format = strings.ToLower(format)
	return SUPPORTED_FORMATS[format]
}

// _parseTxtFile 解析文本文件
func (p *DocumentParser) _parseTxtFile(filePath string) ParseResult {
	result := ParseResult{
		Format:    "txt",
		ParseType: "local",
	}

	// 读取文件内容
	content := vant2.GetLocalFile(filePath)
	if content == "" {
		result.Message = "读取文本文件失败或文件为空"
		return result
	}

	result.RawContent = content // 原始内容
	result.TextResult = content
	result.HtmlResult = p._textToHtml(content)
	result.FileSize = int64(len(content))
	result.Success = true
	result.Message = "文本文件解析成功"

	return result
}

// _parseTxtFromBytes 从字节数据解析文本
func (p *DocumentParser) _parseTxtFromBytes(data []byte) ParseResult {
	result := ParseResult{
		Format:    "txt",
		ParseType: "local",
		FileSize:  int64(len(data)),
	}

	content := string(data)
	result.RawContent = content // 原始内容
	result.TextResult = content
	result.HtmlResult = p._textToHtml(content)
	result.Success = true
	result.Message = "文本内容解析成功"

	return result
}

// _textToHtml 将纯文本转换为HTML格式
func (p *DocumentParser) _textToHtml(text string) string {
	if text == "" {
		return ""
	}

	// HTML转义
	html := text
	html = strings.ReplaceAll(html, "&", "&amp;")
	html = strings.ReplaceAll(html, "<", "&lt;")
	html = strings.ReplaceAll(html, ">", "&gt;")
	html = strings.ReplaceAll(html, "\"", "&quot;")
	html = strings.ReplaceAll(html, "'", "&#39;")

	// 转换换行符为<br>
	html = strings.ReplaceAll(html, "\n", "<br>\n")

	// 包装在HTML文档中
	htmlDoc := `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
<pre>` + html + `</pre>
</body>
</html>`

	return htmlDoc
}
