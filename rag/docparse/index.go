package docparse

// 文档解析包 - 支持多种格式的文档解析服务
//
// 支持的格式：
// - 文档类型：MD, PDF, DOC, DOCX, TXT, XLS, XLSX, PPT, PPTX
// - 图片类型：JPG, JPEG, PNG, BMP, GIF, WEBP
//
// 功能特点：
// - 自动格式识别
// - 统一解析接口
// - 支持纯文本和HTML两种输出格式
// - 本地解析和API解析相结合
// - 完善的错误处理

// 导出主要的结构体和接口
var (
	// DefaultParser 默认文档解析器实例
	DefaultParser = NewDocumentParser()

	// DefaultController 默认控制器实例
	DefaultController = NewController()
)

// ParseFile 解析文件的便捷方法
func ParseFile(filePath string) ParseResult {
	return DefaultParser.ParseFile(filePath)
}

// ParseFromBytes 从字节数据解析文档的便捷方法
func ParseFromBytes(data []byte, filename string) ParseResult {
	return DefaultParser.ParseFromBytes(data, filename)
}

// GetSupportedFormats 获取支持的格式列表
func GetSupportedFormats() []string {
	return DefaultParser.GetSupportedFormats()
}

// IsFormatSupported 检查格式是否支持
func IsFormatSupported(format string) bool {
	return DefaultParser.IsFormatSupported(format)
}
