package ragchat

import (
	"assistant/rag/pgvector"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
)

// 记忆辅助
func _memory(emd interface{}) string {
	ch := pgvector.ChatHistoryDB()
	_prompt := ch.Where(g.Map{"user_id": 1001}).Embedding(emd).FullChat().VectorListSystem(3, 1)
	vant2.Success(_prompt)
	return _prompt
}

// 移除元素
func _removeElement(slice []string, element string) []string {
	for i, v := range slice {
		if v == element {
			// 使用 append 和切片操作移除元素
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice // 如果没有找到元素，返回原切片
}
