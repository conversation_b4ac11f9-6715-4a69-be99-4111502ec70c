package ragchat

import (
	"assistant/rag/embedding"
	"slices"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// MemoryParams 记忆查询参数
type MemoryParams struct {
	UserID             int64   `json:"user_id"`             // 用户ID
	ChannelID          int64   `json:"channel_id"`          // 频道ID
	Query              string  `json:"query"`               // 查询内容
	Messages           []g.Map `json:"messages"`            // 当前消息列表
	OpenContext        int     `json:"open_context"`        // 上下文记忆开关
	GlobalGroupMemory  bool    `json:"global_group_memory"` // 全分组记忆
	ContextCompression bool    `json:"context_compression"` // 上下文压缩
}

// MemoryResult 记忆查询结果
type MemoryResult struct {
	Messages  []g.Map     `json:"messages"`  // 增强后的消息列表
	Embedding interface{} `json:"embedding"` // 查询向量
}

// GetMemoryEnhancedMessages 获取记忆增强的消息列表
func GetMemoryEnhancedMessages(params *MemoryParams) (*MemoryResult, error) {
	vant2.Primary("开始记忆增强处理", params.OpenContext, params.Query)

	result := &MemoryResult{
		Messages: params.Messages,
	}

	// 如果未开启上下文记忆，直接返回原始消息
	if params.OpenContext != 1 {
		vant2.Primary("记忆功能未开启，直接返回")
		return result, nil
	}

	// 如果没有查询内容，直接返回原始消息
	if params.Query == "" {
		vant2.Primary("查询内容为空，直接返回")
		return result, nil
	}

	// 生成查询向量
	var queryText string
	if params.ContextCompression {
		// 使用关键词提取方式生成查询条件
		keywords, err := ExtractKeywords(params.Messages)
		if err != nil {
			vant2.Warning("关键词提取失败:", err)
			queryText = params.Query
		} else if keywords != "" {
			queryText = params.Query + "|" + keywords
		} else {
			queryText = params.Query
		}
	} else {
		// 直接使用查询内容
		queryText = params.Query
	}

	// 生成embedding向量
	vant2.Primary("开始生成embedding向量:", queryText)
	embedding, err := embedding.EmbeddingText(queryText)
	if err != nil {
		vant2.Warning("向量生成失败:", err)
		// 向量生成失败时，返回原始消息，不影响对话
		return result, nil
	}
	vant2.Primary("embedding向量生成成功")
	result.Embedding = embedding

	// 获取当前消息中的ID列表，排除这些消息不参与检索
	excludeIds := getMessageIds(params.Messages)

	// 构建查询条件
	whereConditions := w.Map{
		"user_id":   params.UserID,
		"is_delete": 0,
	}

	// 根据globalGroupMemory决定是否传入channel_id
	if !params.GlobalGroupMemory {
		whereConditions["channel_id"] = params.ChannelID
	}

	// 查询相似记忆数据
	vant2.Primary("开始查询记忆数据:", whereConditions)
	memoryData, err := queryMemoryData(whereConditions, excludeIds, embedding)
	if err != nil {
		vant2.Warning("记忆数据查询失败:", err)
		return result, nil
	}
	vant2.Primary("记忆数据查询完成，找到记录数:", len(memoryData))

	// 如果没有找到相关记忆，直接返回原始消息
	if len(memoryData) == 0 {
		vant2.Primary("没有找到相关记忆，直接返回")
		return result, nil
	}

	// 将记忆数据插入到消息列表中
	result.Messages = insertMemoryIntoMessages(params.Messages, memoryData)

	return result, nil
}

// getMessageIds 从消息列表中提取ID
func getMessageIds(messages []g.Map) []int {
	var ids []int
	for _, msg := range messages {
		if id := vant2.LodashGetInt(msg, "id"); id > 0 {
			ids = append(ids, id)
		}
	}
	return ids
}

// queryMemoryData 查询记忆数据
func queryMemoryData(whereConditions w.Map, excludeIds []int, embedding interface{}) (w.SliceMap, error) {
	db := vant2.DB("pgvector_chat_history", "vsearch")

	// 构建向量查询字段
	fields := "id,content,role,"
	fields += db.VectorFields(&w.VectorAttr{
		FieldName: "vector",
		Embedding: embedding,
	})

	query := db.Where(whereConditions).Fields(fields)

	// 排除当前消息ID
	if len(excludeIds) > 0 {
		query = query.Where("id not in (?)", excludeIds)
	}

	// 按相似度和时间排序，限制返回数量
	result, err := query.Order("distance asc, time_create desc").Limit(4).All()
	if err != nil {
		return nil, err
	}

	return gconv.SliceMap(result), nil
}

// insertMemoryIntoMessages 将记忆数据插入到消息列表中
func insertMemoryIntoMessages(messages []g.Map, memoryData w.SliceMap) []g.Map {
	// 找到最后一个system消息的位置
	maxSystemIndex := -1
	for i, msg := range messages {
		if vant2.LodashGetString(msg, "role") == "system" {
			maxSystemIndex = i
		}
	}

	// 构建记忆内容
	memoryContent := "## 记忆增强(从聊天历史中召回相似内容) \n ### 召回记忆数据：\n\n"
	for _, memory := range memoryData {
		role := vant2.LodashGetString(memory, "role")
		content := vant2.LodashGetString(memory, "content")
		memoryContent += role + ":" + content + "\n"
	}

	// 创建记忆消息
	memoryMessage := w.Map{
		"role":    "system",
		"content": memoryContent,
	}

	// 在最后一个system消息后插入记忆消息
	insertIndex := maxSystemIndex + 1
	if maxSystemIndex == -1 {
		// 如果没有system消息，插入到开头
		insertIndex = 0
	}

	return slices.Insert(messages, insertIndex, memoryMessage)
}
