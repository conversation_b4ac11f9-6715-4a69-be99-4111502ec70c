package ragchat

import (
	"assistant/rag/openai"
	"vant2"
)

// qa处理QA问答，基于召回的QA数据回答问题
func qa(params *ActionParams) *openai.ToolResult {
	systemPrompt := `你是知识库问答专家，根据提供的QA问答库数据为用户提供准确的回答。`
	llm := openai.New(openai.LLMKey("minimax-text-01")).System(systemPrompt).Messages(params.Messages)

	// 添加QA召回数据的system消息
	if len(params.QAList) > 0 {
		qaSystemContent := "当前QA问答召回数据：\n\n"
		for _, qa := range params.QAList {
			id := vant2.LodashGetString(qa, "id")
			content := vant2.LodashGetString(qa, "content")
			qaSystemContent += "#QA问答召回数据[id：" + id + "]：\n" + content + "\n\n"
		}
		llm = llm.System(qaSystemContent)
	}

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "qa")
	}

	return llm.Chat()
}

// doc处理文档问答，基于召回的文档数据回答问题
func doc(params *ActionParams) *openai.ToolResult {
	systemPrompt := `你是知识库问答专家，根据提供的文档数据为用户提供准确的回答。`
	llm := openai.New(openai.LLMKey("minimax-text-01")).System(systemPrompt).Messages(params.Messages)

	// 添加QA召回数据的system消息
	if len(params.QAList) > 0 {
		qaSystemContent := "当前QA问答召回数据：\n\n"
		for _, qa := range params.QAList {
			id := vant2.LodashGetString(qa, "id")
			content := vant2.LodashGetString(qa, "content")
			qaSystemContent += "#QA问答召回数据[id：" + id + "]：\n" + content + "\n\n"
		}
		llm = llm.System(qaSystemContent)
	}

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "qa")
	}

	return llm.Chat()
}
