package ragchat

import (
	chats "assistant/pods/chat/service"
	"assistant/rag/embedding"
	"assistant/rag/openai"
	"assistant/rag/pgvector"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// ChatRequest OpenAI标准聊天请求结构
type ChatRequest struct {
	Model    string  `json:"model"`
	Messages []g.Map `json:"messages"`
	Stream   bool    `json:"stream"`

	// 基础参数
	ChannelId int64  `json:"channel_id"` // 会话ID
	RefreshId *int64 `json:"refreshId"`  // 刷新ID，如果大于1则删除该ID之后的所有对话

	// 模式相关参数
	Mode      string   `json:"mode"`      // 对话模式: chat, agents
	Agents    []string `json:"agents"`    // 智能工具列表
	Thinking  bool     `json:"thinking"`  // 是否开启思考
	WebSearch bool     `json:"webSearch"` // 是否开启Web搜索

	// 配置参数
	MaxTokens          int     `json:"max_tokens"`         // 最大输出token数
	Temperature        float64 `json:"temperature"`        // 内容温度
	OpenContext        int     `json:"openContext"`        // 上下文记忆开关，1=开启，0=关闭
	GlobalGroupMemory  bool    `json:"globalGroupMemory"`  // 全分组记忆
	ContextCompression bool    `json:"contextCompression"` // 上下文压缩
}

// Chat 处理聊天请求的主入口
func Chat(r *ghttp.Request) {
	vant2.Primary("Chat函数开始执行")
	var req ChatRequest
	if err := r.Parse(&req); err != nil {
		vant2.Error("请求解析失败:", err)
		WriteError(r, "请求参数解析失败: "+err.Error(), req.Stream, "chat")
		return
	}
	vant2.Primary("请求解析成功:", req)

	// 验证消息是否存在
	if len(req.Messages) == 0 {
		WriteError(r, "消息列表不能为空", req.Stream, "chat")
		return
	}

	// 如果channel_id为0，使用时间戳作为默认值
	if req.ChannelId == 0 {
		req.ChannelId = vant2.Time()
	}

	// 获取用户认证信息
	authToken := r.Header.Get("Authorization")
	if authToken == "" {
		vant2.Warning("认证失败: 缺少Authorization头")
		WriteError(r, "认证失败: 缺少Authorization头", req.Stream, "chat")
		return
	}

	// 使用认证函数获取用户ID
	userId := _authKey(authToken)
	if userId == 0 {
		vant2.Warning("认证失败: 无效的token")
		WriteError(r, "认证失败: 无效的token", req.Stream, "chat")
		return
	}

	vant2.Primary("用户认证成功, userId:", userId)

	// 处理refreshId参数 - 删除指定ID之后的对话历史
	if req.RefreshId != nil && *req.RefreshId > 1 {
		if err := _handleRefreshDelete(userId, req.ChannelId, *req.RefreshId); err != nil {
			WriteError(r, "删除对话历史失败: "+err.Error(), req.Stream, "chat")
			return
		}
	}

	// 获取最后一条用户输入
	lastUserInput := getLastUserInput(req.Messages)
	if lastUserInput == "" {
		WriteError(r, "未找到有效的用户输入", req.Stream, "chat")
		return
	}

	// 处理记忆增强
	var memoryResult *MemoryResult
	if req.OpenContext == 1 {
		vant2.Primary("开始记忆增强处理")
		memoryParams := &MemoryParams{
			UserID:             userId,
			ChannelID:          req.ChannelId,
			Query:              lastUserInput,
			Messages:           req.Messages,
			OpenContext:        req.OpenContext,
			GlobalGroupMemory:  req.GlobalGroupMemory,
			ContextCompression: req.ContextCompression,
		}

		var err error
		memoryResult, err = GetMemoryEnhancedMessages(memoryParams)
		if err != nil {
			vant2.Warning("记忆增强失败:", err)
			memoryResult = &MemoryResult{Messages: req.Messages}
		}
		vant2.Primary("记忆增强完成，消息数量:", len(memoryResult.Messages))

		// 更新请求中的消息列表
		req.Messages = memoryResult.Messages
	} else {
		vant2.Primary("记忆功能未开启")
		memoryResult = &MemoryResult{Messages: req.Messages}
	}

	vant2.Primary("准备进入对话处理，模式:", req.Mode)
	if req.Mode == "agents" {
		vant2.Primary("进入agents模式")
		_agentChat(req, r, userId, nil)
	} else {
		vant2.Primary("进入chat模式")
		_chat(req, r, userId, nil)
	}
	vant2.Primary("对话处理完成")
}

// 直接正常对话
func _chat(req ChatRequest, r *ghttp.Request, userId int64, embeddingVector interface{}) {
	vant2.Primary("_chat函数开始执行")
	var _result *openai.ToolResult
	_args := &ActionParams{
		Messages:  req.Messages,
		Request:   r,
		IsStream:  req.Stream,
		UserID:    userId,
		ChannelID: req.ChannelId,
		Embedding: embeddingVector,
	}
	vant2.Primary("ActionParams构建完成")

	_model := openai.LLMKey("kimi-k2-fast")
	if req.Thinking {
		_model = openai.LLMKey("qwen3-235b-a22b-fast")
	} else if req.WebSearch {
		_model = openai.LLMKey("deepseek-v3-vol-search")
	}
	vant2.Primary("选择的模型:", _model)

	vant2.Primary("开始调用_onlyChat")
	_result = _onlyChat(_args, _model) // 纯粹仅会话
	vant2.Primary("_onlyChat调用完成")

	// 保存对话数据到数据库
	go _saveChatData(req, userId, _result)

	if !req.Stream { // api直接返回
		HandleAPIResult(r, _result, "chat")
	}
}

// who处理用户身份询问
func _onlyChat(params *ActionParams, model openai.LLMConfig) *openai.ToolResult {
	vant2.Primary("_onlyChat函数开始执行")
	vant2.Primary("消息数量:", len(params.Messages))
	vant2.Primary("是否流式:", params.IsStream)

	// llm := openai.New(_GetDemoLLm()).
	llm := openai.New(model).
		Messages(params.Messages)

	if params.IsStream {
		vant2.Primary("设置SSE流式输出")
		SetupSSEStream(llm, params.Request, "chat")
	}

	vant2.Primary("开始调用llm.Chat()")
	result := llm.Chat()
	vant2.Primary("llm.Chat()调用完成")
	return result
}

func _agentChat(req ChatRequest, r *ghttp.Request, userId int64, embeddingSlice interface{}) {
	// 第一步 封装查询条件 扩展查询条件
	_lastMsg := getLastUserInput(req.Messages) // 最后对话输入内容
	_query := _lastMsg

	// 如果启用了上下文压缩，使用关键词提取
	if req.ContextCompression {
		keywords, _ := ExtractKeywords(req.Messages) // 提取搜索关键词
		if keywords != "" {
			_query += "|" + keywords
		}
	}
	vant2.Primary(_query)

	// 第二步 获取向量 (优先使用传入的embedding)
	var _embedding interface{}
	if embeddingSlice != nil {
		_embedding = embeddingSlice
	} else {
		_embedding, _ = embedding.EmbeddingText(_query) //  查询的向量组合
	}
	// 根据agents参数决定使用哪些工具
	tools := req.Agents
	if len(tools) == 0 {
		// 如果没有指定agents，使用默认工具集
		tools = []string{"qa", "who", "math", "openUrl", "task", "accounting", "search"}
	}

	_docList, _qaList := w.SliceMap{}, w.SliceMap{}
	// 第三步 检查qa、doc等工具
	if vant2.InArray2("qa", tools) { // 召回qa
		pg := pgvector.NewDB()
		_qaList = pg.Where(g.Map{"user_id": userId}).
			Query(_query).
			Embedding(_embedding).
			Limit(10).
			List()
	}
	if vant2.InArray2("doc", tools) { // 召回doc
		_doc := pgvector.NewDB()
		_docList = _doc.Where(g.Map{"user_id": userId}).
			Query(_query).
			Embedding(_embedding).
			Limit(10).
			SelectSplit(false) // false查询的  是不重复的。
	}
	// 第4步 封装新的tools结果
	vant2.Primary(_docList, _qaList)
	if len(_docList) == 0 {
		tools = _removeElement(tools, "doc")
	}
	if len(_qaList) == 0 {
		tools = _removeElement(tools, "qa")
	}

	// 检查有没有http:或者https:
	if !strings.Contains(_lastMsg, "http:") && !strings.Contains(_lastMsg, "https:") {
		tools = _removeElement(tools, "openUrl")
	}

	vant2.Error(tools)
	// 加载问题分类器
	_action := category(tools, _lastMsg, req.Messages, _qaList, _docList)
	vant2.Error(_action)

	var _result *openai.ToolResult
	_args := &ActionParams{
		Messages:  req.Messages,
		Embedding: _embedding,
		Request:   r,
		IsStream:  req.Stream,
		UserID:    userId,
		ChannelID: req.ChannelId,
		QAList:    _qaList,
		DocList:   _docList,
	}
	// 完成分发
	switch _action {
	case "spark": // 星火spark 纯粹因为快速查天气等原因使用
		_result = spark(_args)
	case "who": // 问询AI归属
		_result = who(_args)
	case "math": // 数学计算
		_result = math(_args)
	case "openUrl": // 打开网页
		_result = openUrl(_args)
	case "search": // 搜索
		_result = search(_args)
		// ----- 知识问答相关 -----
	case "qa": // 问答
		_result = qa(_args)
		// ----- 自定义任务相关 -----
	case "task": // 任务
		_result = task(_args)
	case "accounting": // 账目
		_result = accounting(_args)
	default:
		_result = chat(_args) // 正常对话
	}

	// 保存对话数据到数据库
	go _saveChatData(req, userId, _result)

	if !req.Stream { // api直接返回
		HandleAPIResult(r, _result, _action)
	}
}

// _saveChatData 保存对话数据到数据库
func _saveChatData(req ChatRequest, userId int64, result *openai.ToolResult) {
	if result == nil {
		return
	}

	// 获取最后一条用户输入
	lastUserInput := getLastUserInput(req.Messages)
	if lastUserInput == "" {
		return
	}

	// 生成用户消息的embedding
	userEmbedding, _ := embedding.EmbeddingText(lastUserInput)

	// 保存用户消息
	userModel := &chats.ChatSaveModel{
		Model:           req.Model,
		Content:         lastUserInput,
		Role:            "user",
		UserId:          userId,
		GroupId:         0, // 暂时设为0
		ChannelId:       req.ChannelId,
		TimeCreate:      vant2.Time(),
		TokenPrompt:     0,
		TokenCompletion: 0,
		TokenTotal:      0,
		Vector:          userEmbedding,
	}
	chats.Save(userModel)

	// 获取AI回复内容
	assistantContent := result.GetContent()
	if assistantContent == "" {
		return
	}

	// 生成AI回复的embedding
	assistantEmbedding, _ := embedding.EmbeddingText(assistantContent)

	// 保存AI回复
	tokenUsage := result.GetTokenUsage()
	assistantModel := &chats.ChatSaveModel{
		Model:           req.Model,
		Content:         assistantContent,
		Role:            "assistant",
		UserId:          userId,
		GroupId:         0, // 暂时设为0
		ChannelId:       req.ChannelId,
		TimeCreate:      vant2.Time(),
		TokenPrompt:     tokenUsage.PromptTokens,
		TokenCompletion: tokenUsage.CompletionTokens,
		TokenTotal:      tokenUsage.TotalTokens,
		Vector:          assistantEmbedding,
		ContextMsg:      vant2.JsonEncoder(req.Messages),
	}
	chats.Save(assistantModel)
}

// _handleRefreshDelete 处理refreshId删除操作
// userId: 用户ID
// channelId: 频道ID
// refreshId: 刷新ID，删除所有大于此ID的对话历史
// 返回值: error - 删除失败时返回错误信息
func _handleRefreshDelete(userId, channelId, refreshId int64) error {
	vant2.Primary("开始处理refreshId删除操作", "userId:", userId, "channelId:", channelId, "refreshId:", refreshId)

	// 物理删除当前用户当前channelId下所有大于refreshId的对话历史
	deleteResult, err := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"user_id":    userId,
		"channel_id": channelId,
	}).Where("id >= ?", refreshId).Delete()

	if err != nil {
		vant2.Warning("删除对话历史失败:", err)
		return err
	}

	rowsAffected, _ := deleteResult.RowsAffected()
	vant2.Primary("refreshId删除操作完成, 删除记录数:", rowsAffected)

	return nil
}
