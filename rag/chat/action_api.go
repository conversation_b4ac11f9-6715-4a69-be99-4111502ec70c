package ragchat

// 主要是针对自己的任务  比如： 待办、支出等

import (
	"assistant/rag/openai"
	"vant2"
)

// task处理搜索
func task(params *ActionParams) *openai.ToolResult {

	// tools调用工具 抽取关键词
	_tool := vant2.GetLocalFileJson("prompt/funcs/task.json")

	llm := openai.New(openai.LLMKey("deepseek-v3-siliconflow")).
		Messages(params.Messages).
		Tools(vant2.LodashGetSliceMap(_tool, "func"))

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "task")
	}

	return llm.Chat()
}

// accounting处理账目
func accounting(params *ActionParams) *openai.ToolResult {

	// tools调用工具 抽取关键词
	_tool := vant2.GetLocalFileJson("prompt/funcs/accounting.json")

	llm := openai.New(openai.LLMKey("deepseek-v3-siliconflow")).
		Messages(params.Messages).
		Tools(vant2.LodashGetSliceMap(_tool, "func"))

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "accounting")
	}

	return llm.Chat()
}
