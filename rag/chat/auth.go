package ragchat

import (
	authm "assistant/pods/auth/model"
	"strings"
	"vant2"
)

// 统一认证入口 - 检查apikey是否有效并返回用户ID
// apikey: 传入的认证key (ut_开头的userToken 或 sk-开头的apiToken)
// 返回值: userId - 0表示认证失败
func _authKey(apikey string) int64 {
	if apikey == "" {
		vant2.Warning("认证失败: apikey为空")
		return 0
	}

	// 清理token格式
	cleanKey := strings.ToLower(strings.TrimSpace(apikey))
	if strings.HasPrefix(cleanKey, "bearer ") {
		cleanKey = strings.ReplaceAll(cleanKey, "bearer ", "")
	}

	// 判断key类型并分发到对应的认证方法
	if strings.HasPrefix(cleanKey, "ut_") {
		// 用户Token认证
		vant2.Primary("检测到用户Token，开始认证:", cleanKey)
		return _authUser(cleanKey)

	} else if strings.HasPrefix(cleanKey, "sk-") {
		// API Token认证
		vant2.Primary("检测到API Token，开始认证:", cleanKey)
		return _authApiKey(cleanKey)

	} else {
		// 未知格式，直接返回错误
		vant2.Warning("认证失败: 未知的key格式，必须以ut_或sk-开头:", cleanKey)
		return 0
	}
}

// 检查用户Token是否有效 (ut_开头)
func _authUser(userToken string) int64 {
	// 使用pods/auth中的认证逻辑
	tokenModel := &authm.TokenModel{}
	userId := tokenModel.CheckToken(userToken)

	if userId > 0 {
		vant2.Primary("用户Token认证成功, userId:", userId)
		return userId
	} else {
		vant2.Warning("用户Token认证失败:", userToken)
		return 0
	}
}

// 检查API Key是否有效 (sk-开头，暂时预留)
func _authApiKey(apikey string) int64 {
	// TODO: 实现API Key验证逻辑
	vant2.Primary("API Key认证暂未实现:", apikey)
	return 0
}
