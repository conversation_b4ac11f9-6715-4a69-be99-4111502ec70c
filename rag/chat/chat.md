# 前端调用chat方法 提交的参数

## 接口地址
`POST /assistant/chat`

## 请求参数说明

### 基础参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `stream` | boolean | 是 | true | 是否启用流式传输 |
| `messages` | array | 是 | - | 对话消息数组，包含历史对话内容 |
| `channel_id` | number | 是 | - | 会话ID，用于标识当前对话会话 |

### 模式相关参数

| 参数名 | 类型 | 必填 | 默认值 | 可选值 | 说明 |
|--------|------|------|--------|--------|------|
| `mode` | string | 否 | 'chat' | 'chat', 'agents' | 对话模式 |
| `agents` | array | 否 | [] | 见下方agents选项 | 智能工具列表（仅在agents模式下生效） |
| `thinking` | boolean | 否 | false | true, false | 深度思考模式（仅在chat模式下生效） |
| `webSearch` | boolean | 否 | false | true, false | 联网搜索模式（仅在chat模式下生效） |

### 配置参数

| 参数名 | 类型 | 必填 | 默认值 | 取值范围 | 说明 |
|--------|------|------|--------|----------|------|
| `max_tokens` | number | 否 | 0 | 0-4098 | 最大输出token数，0表示不限制 |
| `temperature` | number | 否 | 0 | 0.0-1.0 | 内容温度，控制回答的随机性 |
| `openContext` | number | 否 | 1 | 0, 1 | 上下文记忆开关，1=开启，0=关闭 |
| `globalGroupMemory` | boolean | 否 | false | true, false | 全分组记忆，跨会话组共享记忆内容 |
| `contextCompression` | boolean | 否 | false | true, false | 上下文压缩，自动压缩长对话上下文 |

## 详细说明

### 对话模式 (mode)

#### chat 模式
- **说明**: 传统的对话模式，纯文本交流
- **特点**: 简洁高效，支持深度思考和联网搜索
- **适用场景**: 日常对话、问答、创作等

#### agents 模式
- **说明**: 智能工具辅助模式，可调用多种专业工具
- **特点**: 功能丰富，支持多种智能工具协作
- **适用场景**: 复杂任务处理、专业领域咨询等

### 智能工具选项 (agents)

当 `mode` 为 'agents' 时，可选择以下智能工具：

| 工具代码 | 工具名称 | 图标 | 功能描述 |
|----------|----------|------|----------|
| `search` | 搜索工具 | 🔎 | 网络搜索和信息查询 |
| `openUrl` | 网页访问 | 🌍 | 打开和分析网页内容 |
| `doc` | 知识文档库 | 📚 | 知识文档库查询和检索 |
| `qa` | 问答系统 | 💬 | 智能问答和知识库 |
| `task` | 任务管理 | ✅ | 任务创建和管理功能 |
| `accounting` | 财务助手 | 💰 | 财务计算和分析工具 |

**示例**:
```json
{
  "agents": ["search", "doc", "qa"]
}
```

### 消息格式 (messages)

消息数组中每个消息对象的格式：

```json
{
  "role": "user|assistant",
  "content": "消息内容",
  "id": "消息ID（可选）"
}
```

**说明**:
- `role`: 消息角色，'user' 表示用户消息，'assistant' 表示AI回复
- `content`: 消息的具体内容
- `id`: 消息的唯一标识符（可选）

### 参数组合规则

1. **chat 模式下**:
   - `thinking` 和 `webSearch` 可以使用
   - `agents` 参数会被忽略
   - `contextCompression` 不显示给用户

2. **agents 模式下**:
   - `agents` 参数生效，至少需要选择一个工具
   - `thinking` 和 `webSearch` 会被忽略
   - `contextCompression` 可以使用

3. **记忆相关**:
   - `globalGroupMemory` 只有在 `openContext = 1` 时才能启用
   - 当 `openContext = 0` 时，`globalGroupMemory` 自动为 false

### 客户端本地控制参数

以下参数仅在客户端使用，不会传递给服务端：

| 参数名 | 类型 | 默认值 | 取值范围 | 说明 |
|--------|------|--------|----------|------|
| `length` | number | 4 | 1-8 | 历史轮数，控制发送给服务端的历史对话条数 |

**说明**: `length` 参数用于控制客户端从本地消息历史中截取多少条消息发送给服务端，例如 `length=6` 表示发送最近6轮对话。

## 请求示例

### Chat 模式示例
```json
{
  "stream": true,
  "messages": [
    {"role": "user", "content": "你好"},
    {"role": "assistant", "content": "您好！有什么可以帮助您的吗？"},
    {"role": "user", "content": "介绍一下人工智能"}
  ],
  "channel_id": 123,
  "mode": "chat",
  "thinking": true,
  "webSearch": false,
  "max_tokens": 2000,
  "temperature": 0.7,
  "openContext": 1,
  "globalGroupMemory": false,
  "contextCompression": false
}
```

### Agents 模式示例
```json
{
  "stream": true,
  "messages": [
    {"role": "user", "content": "帮我搜索最新的AI技术发展"}
  ],
  "channel_id": 123,
  "mode": "agents",
  "agents": ["search", "doc", "qa"],
  "thinking": false,
  "webSearch": false,
  "max_tokens": 0,
  "temperature": 0,
  "openContext": 1,
  "globalGroupMemory": true,
  "contextCompression": true
}
```

## 注意事项

1. **参数优先级**: 显式传递的参数优先于默认值
2. **模式互斥**: `thinking`/`webSearch` 与 `agents` 模式互斥
3. **工具选择**: agents 模式下必须至少选择一个智能工具
4. **记忆依赖**: `globalGroupMemory` 依赖于 `openContext` 的开启
5. **性能考虑**: 启用过多功能可能影响响应速度，建议根据实际需求选择
