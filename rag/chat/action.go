// 这边的解答模式

package ragchat

import (
	"assistant/rag/openai"
	"assistant/rag/openai/openaiplus"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// ActionParams 统一的动作参数类型
type ActionParams struct {
	// 基础参数
	Messages []g.Map        `json:"messages"`  // 消息列表
	IsStream bool           `json:"is_stream"` // 是否流式输出
	Request  *ghttp.Request `json:"-"`         // HTTP请求对象

	// 扩展数据
	QAList     w.SliceMap `json:"qa_list,omitempty"`     // QA问答召回数据
	DocList    w.SliceMap `json:"doc_list,omitempty"`    // 文档召回数据
	MemoryList w.SliceMap `json:"memory_list,omitempty"` // 记忆召回数据

	// 向量相关
	Embedding interface{} `json:"embedding,omitempty"`  // 查询的embedding向量
	QueryText string      `json:"query_text,omitempty"` // 原始查询文本

	// 用户相关
	UserID         int64 `json:"user_id,omitempty"`         // 用户ID
	ChannelID      int64 `json:"channel_id,omitempty"`      // 频道ID
	ConversationID int64 `json:"conversation_id,omitempty"` // 对话ID

	// 其他扩展参数
	Extra g.Map `json:"extra,omitempty"` // 额外参数
}

func _GetDemoLLm(name ...string) openai.LLMConfig {
	_name := "kimi"
	if len(name) > 0 && name[0] != "" {
		_name = name[0]
	}
	switch _name {
	case "deepseek":
		return openai.LLMKey(_name + "-v3-sop") // 默认使用deepseek-v3-sop
	default:
		return openai.LLMKey(_name + "-k2-fast") // 默认使用kimi-k2-fast
	}
}

// math处理算术运算
func math(params *ActionParams) *openai.ToolResult {

	_math, _mathResult := openaiplus.ToolMath(params.Messages)
	vant2.Success(_math, _mathResult)
	llm := openai.New(_GetDemoLLm())
	if _math != "" {
		llm = llm.System(vant2.Temp(vant2.GetLocalFile("prompt/prompt/math.md"), g.Map{
			"msg":        _math,
			"mathResult": _mathResult,
		}))
	}

	llm = llm.Messages(params.Messages)

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "spark")
	}

	return llm.Chat()
}

// openUrl处理打开网页
func openUrl(params *ActionParams) *openai.ToolResult {
	_msg := vant2.LodashGetString(openai.GetLastMessage(params.Messages), "content")
	_urlResult := openaiplus.ToolOpenUrl(_msg)

	llm := openai.New(_GetDemoLLm())
	if _urlResult != "" {
		llm = llm.System(_urlResult)
	}
	llm = llm.Messages(params.Messages)
	if params.IsStream {
		SetupSSEStream(llm, params.Request, "openUrl")
	}
	return llm.Chat()
}

// who处理用户身份询问
func who(params *ActionParams) *openai.ToolResult {
	systemPrompt := `你是一个智能助手，请介绍你的身份、能力和功能。`
	llm := openai.New(_GetDemoLLm()).
		System(systemPrompt).
		// System(_memory(params.Embedding)).
		Messages(params.Messages)

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "who")
	}

	return llm.Chat()
}

// who处理用户身份询问
func chat(params *ActionParams) *openai.ToolResult {
	// llm := openai.New(_GetDemoLLm()).
	llm := openai.New(openai.LLMKey("qwen3-235b-a22b-fast")).
		Messages(params.Messages)

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "chat")
	}

	return llm.Chat()
}

// search处理搜索
func search(params *ActionParams) *openai.ToolResult {
	llm := openai.New(openai.LLMKey("deepseek-v3-vol-search")).Messages(params.Messages)

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "search")
	}

	return llm.Chat()
}

// spark处理天气、诗词、文学作品、股票价格、编写文案等 已经废弃了  感觉没什么意义 不如直接使用搜索
func spark(params *ActionParams) *openai.ToolResult {
	llm := openai.New(openai.LLMKey("spark")).Messages(params.Messages)

	if params.IsStream {
		SetupSSEStream(llm, params.Request, "spark")
	}

	return llm.Chat()
}
