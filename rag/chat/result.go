package ragchat

import (
	"assistant/rag/openai"
	"fmt"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 设置SSE响应头
func SetSSEHeaders(r *ghttp.Request) {
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")
}

// 通用SSE流式处理方法
func SetupSSEStream(llm *openai.LLM, r *ghttp.Request, model string) {
	SetSSEHeaders(r)
	chatId := fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano())
	llm.Stream(func(result *openai.SSEResult) {
		if content := result.Content(); content != "" {
			WriteSSEChunk(r, content, model, chatId)
		}
		if result.IsComplete() {
			WriteSSEEnd(r, model, chatId)
		}
	})
}

// OpenAI格式的SSE数据块
func WriteSSEChunk(r *ghttp.Request, content string, model string, chatId string) {
	sseData := g.Map{
		"id": chatId, "object": "chat.completion.chunk", "created": time.Now().Unix(), "model": model,
		"choices": []g.Map{{"index": 0, "delta": g.Map{"content": content}, "finish_reason": nil}},
	}
	r.Response.Writef("data: %s\n\n", vant2.JsonEncoder(sseData))
	r.Response.Flush()
}

// OpenAI格式的SSE结束信号
func WriteSSEEnd(r *ghttp.Request, model string, chatId string) {
	finishData := g.Map{
		"id": chatId, "object": "chat.completion.chunk", "created": time.Now().Unix(), "model": model,
		"choices": []g.Map{{"index": 0, "delta": g.Map{}, "finish_reason": "stop"}},
	}
	r.Response.Writef("data: %s\n\n", vant2.JsonEncoder(finishData))
	r.Response.Writef("data: [DONE]\n\n")
	r.Response.Flush()
}

// OpenAI格式的API响应
func WriteAPIResult(r *ghttp.Request, content string, model string, usage g.Map) {
	response := g.Map{
		"id": fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()), "object": "chat.completion",
		"created": time.Now().Unix(), "model": model,
		"choices": []g.Map{{"index": 0, "message": g.Map{"role": "assistant", "content": content}, "finish_reason": "stop"}},
		"usage":   usage,
	}
	r.Response.WriteJsonExit(response)
}

// SSE错误处理
func WriteSSEError(r *ghttp.Request, errorMsg string, model string) {
	SetSSEHeaders(r)
	chatId := fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano())

	// 发送错误信息作为内容
	errorContent := fmt.Sprintf("❌ 错误: %s", errorMsg)
	WriteSSEChunk(r, errorContent, model, chatId)

	// 发送结束信号
	WriteSSEEnd(r, model, chatId)
}

// 统一错误处理 - 根据stream参数决定返回SSE还是API错误
func WriteError(r *ghttp.Request, errorMsg string, isStream bool, model string) {
	if isStream {
		WriteSSEError(r, errorMsg, model)
	} else {
		r.Response.WriteJsonExit(g.Map{
			"error": g.Map{
				"message": errorMsg,
				"type":    "invalid_request_error",
			},
		})
	}
}

// 通用的API结果处理
func HandleAPIResult(r *ghttp.Request, result *openai.ToolResult, model string) {
	if result.HasError() {
		r.Response.WriteJsonExit(g.Map{"error": g.Map{"message": result.GetError(), "type": "api_error"}})
	} else {
		usage := result.GetTokenUsage()
		usageMap := g.Map{"prompt_tokens": usage.PromptTokens, "completion_tokens": usage.CompletionTokens, "total_tokens": usage.TotalTokens}
		WriteAPIResult(r, result.GetContent(), model, usageMap)
	}
}
