# RAG Chat 模块整合说明

## 整合概述

本次整合将 `pods/chat` 和 `rag/chat` 两个模块进行了统一，实现了以下功能：

1. **统一入口**: 所有对话操作通过 `/assistant/chat` 接口处理
2. **记忆增强**: 支持上下文记忆和向量检索
3. **数据管理**: pods/chat 负责基础的CRUD操作，rag/chat 负责对话处理
4. **数据存储**: 统一使用 `pgvector_chat_history` 表存储对话数据

## 主要变更

### 1. rag/chat/index.go
- 更新了 `ChatRequest` 结构体，支持完整的参数
- 添加了记忆增强功能
- 集成了数据保存功能
- 支持 chat 和 agents 两种模式

### 2. rag/chat/memory.go (新增)
- 专门处理记忆数据查询
- 支持上下文压缩和关键词提取
- 支持全分组记忆查询
- 自动排除当前对话消息

### 3. pods/chat/service/pgdb.go
- 更新表名为 `pgvector_chat_history`
- 调整字段映射以匹配新的数据库结构
- 将扩展信息存储在 `extra` JSONB 字段中

### 4. pods/chat/curd.go
- 更新所有数据库查询的表名
- 调整字段名以匹配新结构

## 参数说明

### 基础参数
- `stream`: 是否启用流式传输
- `messages`: 对话消息数组
- `channel_id`: 会话ID（必填）

### 模式参数
- `mode`: 对话模式 ('chat' | 'agents')
- `agents`: 智能工具列表（agents模式下使用）
- `thinking`: 深度思考模式
- `webSearch`: 联网搜索模式

### 记忆参数
- `openContext`: 上下文记忆开关 (1=开启, 0=关闭)
- `globalGroupMemory`: 全分组记忆，跨会话组共享记忆
- `contextCompression`: 上下文压缩，使用关键词提取

### 配置参数
- `max_tokens`: 最大输出token数
- `temperature`: 内容温度

## 数据库结构

使用 `pgvector_chat_history` 表，主要字段：
- `id`: 主键
- `vector`: 向量字段（1024维）
- `user_id`: 用户ID
- `channel_id`: 分组/频道ID
- `conversation_id`: 对话组ID
- `role`: 角色 (user|assistant|system)
- `content`: 对话内容
- `time_create`: 创建时间
- `tokens`: token数量
- `extra`: 扩展信息（JSONB格式）

## 工作流程

1. **接收请求**: `/assistant/chat` 接收前端请求
2. **参数解析**: 解析请求参数，验证必填字段
3. **记忆增强**: 根据 `openContext` 参数决定是否启用记忆
4. **向量检索**: 查询相似的历史对话
5. **消息增强**: 将记忆数据插入到消息列表中
6. **模式分发**: 根据 `mode` 参数选择处理方式
7. **AI对话**: 调用相应的AI模型进行对话
8. **数据保存**: 将用户输入和AI回复保存到数据库
9. **返回结果**: 返回对话结果给前端

## 记忆功能详解

### 上下文记忆 (openContext)
- 开启时会查询相似的历史对话
- 自动排除当前提交的消息
- 按相似度排序，返回最相关的4条记录

### 全分组记忆 (globalGroupMemory)
- 开启时跨所有会话组查询记忆
- 关闭时只在当前会话组内查询

### 上下文压缩 (contextCompression)
- 开启时使用关键词提取增强查询
- 关闭时直接使用用户输入作为查询条件

## 保留的功能

pods/chat 模块保留以下管理功能：
- 会话分组管理 (CRUD)
- 对话历史查询
- 数据删除和清理
- 会话配置管理

## 使用示例

```json
{
  "stream": true,
  "messages": [
    {"role": "user", "content": "你好"}
  ],
  "channel_id": 123,
  "mode": "chat",
  "thinking": false,
  "webSearch": false,
  "openContext": 1,
  "globalGroupMemory": false,
  "contextCompression": true,
  "max_tokens": 2000,
  "temperature": 0.7
}
```

## 注意事项

1. 确保数据库中存在 `pgvector_chat_history` 表
2. 向量维度必须为1024维
3. 记忆功能依赖embedding服务
4. 数据保存是异步进行的
5. 用户ID目前硬编码为1001，实际使用时需要从认证中获取
