package ragchat

import (
	"fmt"
	"strings"
	"time"
	"vant2"
	"vant2/tool/w"

	"assistant/rag/openai"

	"github.com/gogf/gf/v2/frame/g"
)

// ExtractKeywords 从对话历史记录中提取搜索关键词
func ExtractKeywords(messages []g.Map) (string, error) {
	if len(messages) < 2 {
		return "", nil // 没有对话 无需执行
	}

	// 获取prompt模板
	promptTemplate := vant2.GetLocalFile("prompt/prompt/search_keyword.md")
	if promptTemplate == "" {
		return "", fmt.Errorf("无法读取搜索关键词提取模板文件")
	}

	// 获取当前用户输入和历史记录
	currentInput := getLastUserInput(messages)
	if currentInput == "" {
		return "", fmt.Errorf("未找到有效的用户输入")
	}

	historyText := buildDialogHistory(messages)

	// 替换时间变量
	now := time.Now()
	prompt := vant2.Temp(promptTemplate, w.Map{
		"dateNow":      now.Format("2006-01-02 15:04:05"),
		"yearNow":      fmt.Sprintf("%d年", now.Year()),
		"LastYearNow":  fmt.Sprintf("%d年", now.Year()-1),
		"LastMonthNow": now.AddDate(0, -1, 0).Format("2006年01月"),
	})

	// 构建完整prompt
	fullPrompt := fmt.Sprintf(`%s

当前用户输入对话： "%s"
当前历史对话内容： %s
返回JSON：`, prompt, currentInput, historyText)

	// 调用LLM
	result := openai.New(_GetDemoLLm()).
		MaxTokens(500).
		System(fullPrompt).
		FormatJson().
		Chat()

	if result.HasError() {
		return "", fmt.Errorf("大模型调用失败: %s", result.GetError())
	}

	// 解析output字段
	tools := result.GetTools()

	return vant2.LodashGetString(tools, "0.output"), nil // 如果解析失败，返回原始内容
}

// getLastUserInput 获取最后一条用户输入
func getLastUserInput(messages []g.Map) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i]["role"] == "user" {
			return openai.ExtractTextFromMultimodalContent(messages[i]["content"])
		}
	}
	return ""
}

// buildDialogHistory 构建对话历史记录
func buildDialogHistory(messages []g.Map) string {
	if len(messages) <= 1 {
		return "无"
	}

	var parts []string
	// 排除最后一条消息（当前用户输入）
	for i := 0; i < len(messages)-1; i++ {
		msg := messages[i]
		content := openai.ExtractTextFromMultimodalContent(msg["content"])
		if content != "" {
			parts = append(parts, fmt.Sprintf("%s: \"%s\"", msg["role"], content))
		}
	}

	if len(parts) == 0 {
		return "无"
	}

	return "[\n    " + strings.Join(parts, "\n    ") + "\n]"
}
