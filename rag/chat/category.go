package ragchat

import (
	"assistant/rag/openai"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
)

// 分组的脚本
func category(tools []string, userInput string, historyMessages []g.Map, qaList w.<PERSON>lice<PERSON>ap, docList w.<PERSON>liceMap) string {

	promptTemplate := vant2.GetLocalFile("prompt/system/category.md")
	if promptTemplate == "" {
		return "chat"
	}

	// 组装工具描述和示例
	var toolDescriptions []string
	var toolNames []string
	var examples []string

	for _, toolName := range tools {
		if toolName == "chat" {
			toolNames = append(toolNames, "\""+toolName+"\"")
			continue
		}

		// 从 prompt/funcs/ 目录读取工具配置
		toolConfig := vant2.GetLocalFileJson("prompt/funcs/" + toolName + ".json")
		if toolConfig == nil {
			continue
		}

		// 提取工具信息
		name := vant2.LodashGetString(toolConfig, "name")
		desc := vant2.LodashGetString(toolConfig, "desc")
		example := vant2.LodashGetString(toolConfig, "example")

		if name != "" && desc != "" {
			// 组装工具描述：name: desc
			toolDescriptions = append(toolDescriptions, name+": "+desc)
			toolNames = append(toolNames, "\""+name+"\"")

			// 如果有示例，添加到examples中
			if example != "" {
				examples = append(examples, example)
			}
		}
	}

	// 组装模版变量
	_prompt := vant2.Temp(promptTemplate, w.Map{
		"tools":     strings.Join(toolDescriptions, "\n"),
		"toolNames": strings.Join(toolNames, ","),
		"example":   strings.Join(examples, "\n\n"),
	})

	// 初始化LLM
	// llm := openai.New(openai.LLMKey("deepseek-v3-sop")).
	llm := openai.New(_GetDemoLLm()).
		MaxTokens(500).
		System(_prompt).
		AddSystemTime()

	// 如果有qa数据，添加qa系统信息
	if len(qaList) > 0 {
		qaSystemContent := "当前QA问答召回数据：\n\n"
		for _, qa := range qaList {
			id := vant2.LodashGetString(qa, "id")
			content := vant2.LodashGetString(qa, "content")
			qaSystemContent += "#QA问答召回数据[id：" + id + "]：\n" + content + "\n\n"
		}
		llm = llm.System(qaSystemContent)
	}

	// 如果有doc数据，添加doc系统信息
	if len(docList) > 0 {
		docSystemContent := "当前文档召回内容：\n\n"
		for _, doc := range docList {
			id := vant2.LodashGetString(doc, "id")
			content := vant2.LodashGetString(doc, "content")
			docSystemContent += "#文档召回内容[id：" + id + "]：\n" + content + "\n\n"
		}
		llm = llm.System(docSystemContent)
	}

	// 组装用户对话内容
	historyContent := ""
	if len(historyMessages) > 0 {
		for _, msg := range historyMessages {
			role := vant2.LodashGetString(msg, "role")
			content := vant2.LodashGetString(msg, "content")
			historyContent += role + ": " + content + "\n"
		}
	} else {
		historyContent = "无"
	}

	// 构建最终的用户输入内容
	finalUserInput := "当前历史对话内容是：\n```\n" + historyContent + "\n```\nuser[当前用户输入内容]： '" + userInput + "'\n，返回的json是："

	// 添加用户消息并调用
	result := llm.
		// FormatJson().
		Chat(finalUserInput)

	_action := vant2.LodashGetString(result.GetTools(), "0.action")

	if _action == "" || !vant2.InArray2(_action, tools) {
		return "chat"
	}
	return _action
}
