你把需求弄错了

你现在 出色的封装了 pgvector 和 llm 功能模块

我现在希望的终极对话  应该是

{"qa","doc","chat"}

1、 用户发起对话
2、 pgvector查询chat对话历史 得到查看用户历史对话记录
3、 打包历史对话记录 调用大模型API抽取检索关键词
4、 将当前用户发送的内容+抽取的检索辅助词 打包 向量化得到向量
5、 从pgvector内 通过得到的 向量 查询之前的向量检索返回查询2 得到相似度最高的内容。
6、 其中 qa和doc（问答和文档查询） 的区别 就是search_content表的channel字段 如果是doc就是文档  如果是qa就是问答
7、 根据返回的qa和doc  打包问题分类器  这里面的判断很多 应该是先定义了1个数组 类似 {"qa","doc","chat"}  然后呢  返回的东西里面 没有qa属性的内容  那就从数组中移除qa  如果没有doc的 直接移除doc  当然 如果数组里面最后什么都没有了  只剩下chat 那就不需要走分类器的逻辑了！
8、分类器 是根据需求 进行的打包操作

这个问题分类器 本质上是3个system
类似
_system := vant2.Temp(vant2.GetLocalFile("prompt/system/category.md"),w.Map{
  tools:xxx,
  toolNames:[],
  example:xxx,
})
// 添加分类器的system
llm = New().System(_system).AddSystemTime()
// 然后 判断有qa  如果有qa  继续 llm.System(`当前QA问答库返回信息：`)
// 然后 判断有doc  如果有doc  继续 llm.System(`当前知识库返回信息：`)
这样一溜烟判断下来的！注意  doc和qa的数组  需要分开的  并且要保留的
比如 当最后发现是qa  那肯定是执行qa问答了
继续  新的对话模式
llm2 = New().System(`您是知识库问答专家.....`).System(`当前QA问答库返回信息：`).Messages(用户对话的上下文放进来).Stream(根据用户需求 看看是否需要stream).Chat()

问题分类器这边 必然有个传入参数 tools-> 用户默认开启分类对象 传入的必然类似slicestr{"doc","qa","chat","weather","who","search"...}
组装的tools 就是这个数组  在根目录的prompt的funcs下面 一定有按照这个起名的  比如 search.md文件 结构如下
{
  "name": "search",
  "desc": "用户查询新闻、时政、娱乐等信息，或者当你的知识不足以完成对话，必须借助搜索引擎查询数据。输出的查询条件要精确（日期转化等）和符合搜索引擎的搜索规则。",
  "example": "\n当前输入对话： \"\"\"查下今年扬州市的gdp是多少\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"search\",\"query\":\"2024 扬州市 gdp总量\"}\n",
  "func": null
}
你可以直接  vant2.GetLocalFileJson("prompt/funcs/"+toolName+".md")直接拿到返回的g.Map对象。  里面的desc 就是介绍 拼接tools  就是 name + ":" + desc
example也是打包集体放到example去的。

这里的所有的一切  都是打包的对话  细节你都需要单独封装私有方法 并且 里面什么Embedding啊  qa查询的结果啊  doc的结果啊  你都要暂存的  下一轮很大概率用得上。



参考另外一个项目的打包后的产物  他的设计 没有层叠感  我建议将doc和qa的关联数据  单独分成一个独立的system来实现 而不是杂糅在一起  其他的 我们应该是差不多的
```
[{"role":"system","content":"作为智能助手，你的任务是根据用户的意图采取相应的行动。\n如果用户对话需要调用城市，则默认城市是扬州市，你可以执行以下操作：\nsearch：用户查询新闻、时政、娱乐等信息，或者当你的知识不足以完成对话，必须借助搜索引擎查询数据。输出的查询条件要精确（日期转化等）和符合搜索引擎的搜索规则。\ndeveloper：用户明确询问的是在编程开发、软件开发、硬件开发类中遇到的技术问题\nopenUrl：用户请求中明确包含http或https的网址，需要调用网页访问工具获取该网址内的全部文字信息进行对话，对话内容必须包含可直接访问的http/https链接！\nwho：用户请求中询问了类似'你是谁','你能做什么','你叫什么名字','你是谁开发的','你是基于什么'之类的内容\nmath：用户的问题明确提问的是和算术运算相关的内容，并且该算术运算可以明确转变为js可执行的代码\nspark：用户查询天气、日期日历万年历、诗歌诗词、文学作品、股票价格信息，或者用户想要编写文章、诗歌、作文、文案等则调用本方法\nqa：用户输入的内容，触发了召回QA问答的数据片段，召回的数据在下面的'QA问答召回数据'中，请根据QA问答的数据片段，判断是否需要使用QA问答并优先使用QA问答你们公司，你们单位，云控，江苏云控，云控软件等等，都是特指单位：江苏云控软件技术有限公司\n\n注意：\n操作名称只能是数组[\"chat\",\"search\",\"developer\",\"openUrl\",\"who\",\"math\",\"spark\",\"qa\"]中的一个\n请根据用户的请求，使用以下格式返回标准json响应：{\"action\":\"操作名称\"}\n\n例如：\n```\n当前输入对话： \"\"\"你是谁\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"who\"}\n\n当前输入对话： \"\"\"查下今年扬州市的gdp是多少\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"search\"}\n\n\n```\n\n\n```\nQA问答预设内容:\"\"\"你们公司，你们单位，云控，江苏云控，云控软件等等，都是特指单位：江苏云控软件技术有限公司\"\"\"\nQA问答召回数据：\n\n#QA问答召回数据[id：362]：\nQ：你们单位在哪？\nA：您好，我们单位的具体地址是： 扬州市广陵区文昌东路12号泰达Y-MSD12栋10层\n\n#QA问答召回数据[id：361]：\nQ：我应该如何去你们单位\nA：您好，我们单位的具体地址是： 扬州市广陵区文昌东路12号泰达Y-MSD12栋10层\n```\n\n\n禁止返回除操作名称（action）外的任何信息\n如果请求不属于以上任何一种，则返回：{\"action\":\"chat\"}\n"},{"role":"user","content":"当前历史对话内容是：\n```\n\n \n```\nuser[当前用户输入内容]： '你是谁啊？'\n，返回的json是："}][0;0m
[36m{"max_tokens":20,"messages":[{"role":"system","content":"作为智能助手，你的任务是根据用户的意图采取相应的行动。当前时间是2025-07-24 11:12:08。\n如果用户对话需要调用城市，则默认城市是扬州市，你可以执行以下操作：\nsearch：用户查询新闻、时政、娱乐等信息，或者当你的知识不足以完成对话，必须借助搜索引擎查询数据。输出的查询条件要精确（日期转化等）和符合搜索引擎的搜索规则。\ndeveloper：用户明确询问的是在编程开发、软件开发、硬件开发类中遇到的技术问题\nopenUrl：用户请求中明确包含http或https的网址，需要调用网页访问工具获取该网址内的全部文字信息进行对话，对话内容必须包含可直接访问的http/https链接！\nwho：用户请求中询问了类似'你是谁','你能做什么','你叫什么名字','你是谁开发的','你是基于什么'之类的内容\nmath：用户的问题明确提问的是和算术运算相关的内容，并且该算术运算可以明确转变为js可执行的代码\nspark：用户查询天气、日期日历万年历、诗歌诗词、文学作品、股票价格信息，或者用户想要编写文章、诗歌、作文、文案等则调用本方法\nqa：用户输入的内容，触发了召回QA问答的数据片段，召回的数据在下面的'QA问答召回数据'中，请根据QA问答的数据片段，判断是否需要使用QA问答并优先使用QA问答你们公司，你们单位，云控，江苏云控，云控软件等等，都是特指单位：江苏云控软件技术有限公司\n\n注意：\n操作名称只能是数组[\"chat\",\"search\",\"developer\",\"openUrl\",\"who\",\"math\",\"spark\",\"qa\"]中的一个\n请根据用户的请求，使用以下格式返回标准json响应：{\"action\":\"操作名称\"}\n\n例如：\n```\n当前输入对话： \"\"\"你是谁\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"who\"}\n\n当前输入对话： \"\"\"查下今年扬州市的gdp是多少\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"search\"}\n\n\n```\n\n\n```\nQA问答预设内容:\"\"\"你们公司，你们单位，云控，江苏云控，云控软件等等，都是特指单位：江苏云控软件技术有限公司\"\"\"\nQA问答召回数据：\n\n#QA问答召回数据[id：362]：\nQ：你们单位在哪？\nA：您好，我们单位的具体地址是： 扬州市广陵区文昌东路12号泰达Y-MSD12栋10层\n\n#QA问答召回数据[id：361]：\nQ：我应该如何去你们单位\nA：您好，我们单位的具体地址是： 扬州市广陵区文昌东路12号泰达Y-MSD12栋10层\n```\n\n\n禁止返回除操作名称（action）外的任何信息\n如果请求不属于以上任何一种，则返回：{\"action\":\"chat\"}\n"},{"role":"user","content":"当前历史对话内容是：\n```\n\n \n```\nuser[当前用户输入内容]： '你是谁啊？'\n，返回的json是："}]
```