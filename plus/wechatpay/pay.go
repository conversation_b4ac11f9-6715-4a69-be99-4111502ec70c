package wechatpay

import (
	"assistant/plus"
	"context"
	"fmt"
	"time"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat/v3"
)

// CreateJSAPIPayment 创建微信JSAPI支付
func CreateJSAPIPayment(req plus.PayRequest) plus.PayResponse {
	client, err := wechat.NewClientV3(MchID, MchCertificateSerialNumber, MchAPIv3Key, MchPrivateKey)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("初始化微信支付客户端失败: %v", err),
		}
	}

	err = client.AutoVerifySign()
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("自动验签失败: %v", err),
		}
	}

	client.DebugSwitch = gopay.DebugOff

	// 设置订单过期时间
	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)

	// 构建订单参数
	bm := make(gopay.BodyMap)
	bm.Set("appid", AppID).
		Set("mchid", MchID).
		Set("description", req.Description).
		Set("out_trade_no", req.OutTradeNo).
		Set("time_expire", expire).
		Set("notify_url", NotifyUrl).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", int(req.Amount*100)). // 转为分
								Set("currency", "CNY")
		}).
		SetBodyMap("payer", func(bm gopay.BodyMap) {
			openid := req.UserOpenID
			if openid == "" {
				openid = OpenID // 使用配置中的默认OpenID
			}
			bm.Set("openid", openid)
		})

	ctx := context.Background()
	wxRsp, err := client.V3TransactionJsapi(ctx, bm)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("创建微信JSAPI订单失败: %v", err),
		}
	}

	// 生成 JSAPI 支付参数
	jsapi, err := client.PaySignOfJSAPI(AppID, wxRsp.Response.PrepayId)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("生成JSAPI支付参数失败: %v", err),
		}
	}

	// 转换为map
	jsapiParams := map[string]interface{}{
		"appId":     jsapi.AppId,
		"timeStamp": jsapi.TimeStamp,
		"nonceStr":  jsapi.NonceStr,
		"package":   jsapi.Package,
		"signType":  jsapi.SignType,
		"paySign":   jsapi.PaySign,
	}

	return plus.PayResponse{
		Success:     true,
		Message:     "微信JSAPI订单创建成功",
		JSAPIParams: jsapiParams,
	}
}

// CreateNativePayment 创建微信Native支付
func CreateNativePayment(req plus.PayRequest) plus.PayResponse {
	client, err := wechat.NewClientV3(MchID, MchCertificateSerialNumber, MchAPIv3Key, MchPrivateKey)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("初始化微信支付客户端失败: %v", err),
		}
	}

	err = client.AutoVerifySign()
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("自动验签失败: %v", err),
		}
	}

	client.DebugSwitch = gopay.DebugOff

	// 设置订单过期时间
	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)

	// 构建订单参数
	bm := make(gopay.BodyMap)
	bm.Set("appid", AppID).
		Set("mchid", MchID).
		Set("description", req.Description).
		Set("out_trade_no", req.OutTradeNo).
		Set("time_expire", expire).
		Set("notify_url", NotifyUrl).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", int(req.Amount*100)). // 转为分
								Set("currency", "CNY")
		})

	ctx := context.Background()
	wxRsp, err := client.V3TransactionNative(ctx, bm)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("创建微信Native订单失败: %v", err),
		}
	}

	return plus.PayResponse{
		Success: true,
		Message: "微信Native订单创建成功",
		CodeUrl: wxRsp.Response.CodeUrl,
	}
}

// CreateH5Payment 创建微信H5支付
func CreateH5Payment(req plus.PayRequest) plus.PayResponse {
	client, err := wechat.NewClientV3(MchID, MchCertificateSerialNumber, MchAPIv3Key, MchPrivateKey)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("初始化微信支付客户端失败: %v", err),
		}
	}

	err = client.AutoVerifySign()
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("自动验签失败: %v", err),
		}
	}

	client.DebugSwitch = gopay.DebugOff

	// 设置订单过期时间
	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339)

	// 构建订单参数
	bm := make(gopay.BodyMap)
	bm.Set("appid", AppID).
		Set("mchid", MchID).
		Set("description", req.Description).
		Set("out_trade_no", req.OutTradeNo).
		Set("time_expire", expire).
		Set("notify_url", NotifyUrl).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", int(req.Amount*100)). // 转为分
								Set("currency", "CNY")
		}).
		SetBodyMap("scene_info", func(bm gopay.BodyMap) {
			bm.SetBodyMap("h5_info", func(bm gopay.BodyMap) {
				bm.Set("type", "Wap") // 手机网页
			})
		})

	ctx := context.Background()
	wxRsp, err := client.V3TransactionH5(ctx, bm)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("创建微信H5订单失败: %v", err),
		}
	}

	return plus.PayResponse{
		Success: true,
		Message: "微信H5订单创建成功",
		PayUrl:  wxRsp.Response.H5Url,
	}
}

// ParseCallback 解析微信支付回调（简化版本）
func ParseCallback(body []byte, headers map[string]string) plus.CallbackResult {
	// 暂时返回功能未完善的信息
	return plus.CallbackResult{
		Success: false,
		Message: "微信支付回调解析功能暂未完善，请使用QueryPayment查询支付状态",
	}
}

// QueryPayment 查询微信支付状态
func QueryPayment(outTradeNo string) plus.CallbackResult {
	client, err := wechat.NewClientV3(MchID, MchCertificateSerialNumber, MchAPIv3Key, MchPrivateKey)
	if err != nil {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("初始化微信支付客户端失败: %v", err),
		}
	}

	err = client.AutoVerifySign()
	if err != nil {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("自动验签失败: %v", err),
		}
	}

	client.DebugSwitch = gopay.DebugOff

	ctx := context.Background()
	rsp, err := client.V3TransactionQueryOrder(ctx, wechat.OutTradeNo, outTradeNo)
	if err != nil {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("查询微信支付订单失败: %v", err),
		}
	}

	if rsp.Code != 200 {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("查询失败: %s", rsp.Error),
		}
	}

	data := plus.CallbackData{
		OutTradeNo:    rsp.Response.OutTradeNo,
		TransactionID: rsp.Response.TransactionId,
		TradeStatus:   rsp.Response.TradeState,
		Amount:        fmt.Sprintf("%.2f", float64(rsp.Response.Amount.Total)/100),
		PayTime:       rsp.Response.SuccessTime,
		PayerInfo:     rsp.Response.Payer.Openid,
	}

	return plus.CallbackResult{
		Success: true,
		Message: "查询成功",
		Data:    data,
	}
}
