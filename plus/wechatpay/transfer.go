package wechatpay

import (
	"assistant/plus"
	"context"
	"fmt"
	"os"
	"strings"
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/pkg/util"
	"github.com/go-pay/gopay/pkg/xlog"
	"github.com/go-pay/gopay/wechat"
)

// 微信支付V2证书配置
var (
	// 微信支付V2证书文件路径（p12格式）
	MchCertP12Path string = "static/wxpay/apiclient_cert.p12"
	// 微信支付私钥文件路径（pem格式）
	MchPrivateKeyPath string = "static/wxpay/apiclient_key.pem"
	// 证书密码（通常是商户号）
	MchCertPassword string = MchID
)

/*
P12证书摘要算法不支持问题的解决方案：

错误信息：pkcs12: unknown digest algorithm: 2.16.840.*********.2.1
这个错误表示P12证书使用了SHA-256摘要算法（OID: 2.16.840.*********.2.1），
但当前Go版本的golang.org/x/crypto/pkcs12包不支持此算法。

解决方案：
1. 使用OpenSSL将P12证书转换为PEM格式：
   openssl pkcs12 -in apiclient_cert.p12 -out apiclient_cert.pem -nodes
   openssl pkcs12 -in apiclient_cert.p12 -out apiclient_key.pem -nocerts -nodes

2. 或者重新生成使用SHA-1摘要算法的P12证书

3. 或者升级Go版本和相关依赖包到支持SHA-256的版本

4. 使用PEM格式的证书文件替代P12格式
*/

// Transfer 微信企业转账到零钱（V2版本）
func Transfer(req plus.TransferRequest) plus.TransferResponse {
	// 打印调试信息 - 配置参数
	vant2.Primary("=== 微信转账V2调试信息 ===", w.Map{
		"AppID":       AppID,
		"MchID":       MchID,
		"MchAPIv2Key": MchAPIv2Key,
		"证书路径":        MchCertP12Path,
		"证书密码":        MchCertPassword,
	})

	// 打印请求参数
	vant2.Primary("=== 转账请求参数 ===", w.Map{
		"OpenID":      req.OpenID,
		"Amount":      req.Amount,
		"Description": req.Description,
		"CheckName":   req.CheckName,
		"RealName":    req.RealName,
		"OutTradeNo":  req.OutTradeNo,
	})

	// 使用V2客户端初始化
	client := wechat.NewClient(AppID, MchID, MchAPIv2Key, true)

	// 检查p12证书文件是否存在
	if _, err := os.Stat(MchCertP12Path); os.IsNotExist(err) {
		vant2.Primary(fmt.Sprintf("警告: P12证书文件不存在: %s", MchCertP12Path))

		// 检查PEM私钥文件是否存在
		if _, err := os.Stat(MchPrivateKeyPath); os.IsNotExist(err) {
			vant2.Primary(fmt.Sprintf("警告: PEM私钥文件也不存在: %s", MchPrivateKeyPath))
			vant2.Primary("尝试不使用证书进行转账...")
		} else {
			vant2.Primary(fmt.Sprintf("找到PEM私钥文件: %s", MchPrivateKeyPath))
			// 读取PEM私钥文件
			keyData, err := os.ReadFile(MchPrivateKeyPath)
			if err != nil {
				vant2.Primary(fmt.Sprintf("读取PEM私钥文件失败: %v", err))
				return plus.TransferResponse{
					Success: false,
					Message: fmt.Sprintf("读取PEM私钥文件失败: %v", err),
				}
			}
			vant2.Primary(fmt.Sprintf("PEM私钥文件大小: %d bytes", len(keyData)))

			// 尝试使用PEM格式的私钥（注意：这可能不完全兼容，但我们先试试）
			err = client.AddCertPemFileContent(keyData, keyData) // 使用同一个文件作为证书和私钥
			if err != nil {
				vant2.Primary(fmt.Sprintf("添加PEM证书失败: %v", err))
				vant2.Primary("继续尝试不使用证书...")
			} else {
				vant2.Primary("PEM证书添加成功")
			}
		}
	} else {
		// 读取p12证书文件
		certData, err := os.ReadFile(MchCertP12Path)
		if err != nil {
			vant2.Primary(fmt.Sprintf("读取P12证书文件失败: %v", err))
			return plus.TransferResponse{
				Success: false,
				Message: fmt.Sprintf("读取P12证书文件失败: %v", err),
			}
		}

		vant2.Primary(fmt.Sprintf("P12证书文件大小: %d bytes", len(certData)))

		// 添加p12证书
		err = client.AddCertPkcs12FileContent(certData)
		if err != nil {
			vant2.Primary(fmt.Sprintf("添加P12证书失败: %v", err))

			// 检查是否是摘要算法不支持的错误
			errStr := fmt.Sprintf("%v", err)
			if strings.Contains(errStr, "unknown digest algorithm: 2.16.840.*********.2.1") ||
				strings.Contains(errStr, "pkcs12: unknown digest algorithm") {
				vant2.Primary("检测到P12证书使用了不支持的SHA-256摘要算法")
				vant2.Primary("建议解决方案：")
				vant2.Primary("1. 运行脚本: bash scripts/convert_p12_to_pem.sh")
				vant2.Primary("2. 或手动使用OpenSSL转换:")
				vant2.Primary("   openssl pkcs12 -in apiclient_cert.p12 -out apiclient_cert.pem -nodes")
				vant2.Primary("   openssl pkcs12 -in apiclient_cert.p12 -out apiclient_key.pem -nocerts -nodes")

				// 尝试查找并使用PEM格式的证书
				vant2.Primary("尝试查找PEM格式的证书文件...")
				certPemPath := "static/wxpay/apiclient_cert.pem"
				keyPemPath := "static/wxpay/apiclient_key.pem"

				if _, err := os.Stat(certPemPath); err == nil {
					if _, err := os.Stat(keyPemPath); err == nil {
						vant2.Primary("找到PEM格式证书，尝试使用...")
						certData, err1 := os.ReadFile(certPemPath)
						keyData, err2 := os.ReadFile(keyPemPath)
						if err1 == nil && err2 == nil {
							err = client.AddCertPemFileContent(certData, keyData)
							if err == nil {
								vant2.Primary("PEM证书添加成功")
							} else {
								vant2.Primary(fmt.Sprintf("PEM证书添加失败: %v", err))
							}
						}
					}
				}

				if err != nil {
					vant2.Primary("尝试不使用证书继续执行...")
				}
			} else {
				return plus.TransferResponse{
					Success: false,
					Message: fmt.Sprintf("添加P12证书失败: %v", err),
				}
			}
		} else {
			vant2.Primary("P12证书添加成功")
		}
	}

	// 参数验证
	if req.OpenID == "" {
		return plus.TransferResponse{
			Success: false,
			Message: "用户OpenID不能为空",
		}
	}

	if req.Amount <= 0 {
		return plus.TransferResponse{
			Success: false,
			Message: "转账金额必须大于0",
		}
	}

	if req.Description == "" {
		return plus.TransferResponse{
			Success: false,
			Message: "转账备注不能为空",
		}
	}

	// 设置默认值
	checkName := req.CheckName
	if checkName == "" {
		checkName = "NO_CHECK" // 默认不校验姓名
	}

	// 生成商户订单号
	outTradeNo := req.OutTradeNo
	if outTradeNo == "" {
		outTradeNo = fmt.Sprintf("TF%d%s", time.Now().Unix(), util.RandomString(8))
	}

	// 生成随机字符串
	nonceStr := util.RandomString(32)

	// 构建V2企业付款参数
	bm := make(gopay.BodyMap)
	bm.Set("nonce_str", nonceStr). // 随机字符串
					Set("partner_trade_no", outTradeNo). // 商户订单号
					Set("openid", req.OpenID).           // 用户openid
					Set("check_name", checkName).        // 校验用户姓名选项
					Set("amount", int(req.Amount*100)).  // 企业付款金额，单位为分
					Set("desc", req.Description).        // 企业付款备注
					Set("spbill_create_ip", "127.0.0.1") // 调用接口的机器IP地址

	// V2版本：如果需要校验姓名，则添加真实姓名参数
	if checkName == "FORCE_CHECK" && req.RealName != "" {
		bm.Set("re_user_name", req.RealName)
	}

	// 打印最终请求参数
	vant2.Console("=== 最终请求参数 ===")
	vant2.Primary(w.Map{
		"nonce_str":        nonceStr,
		"partner_trade_no": outTradeNo,
		"openid":           req.OpenID,
		"check_name":       checkName,
		"amount":           int(req.Amount * 100),
		"desc":             req.Description,
		"spbill_create_ip": "127.0.0.1",
	})
	if checkName == "FORCE_CHECK" && req.RealName != "" {
		vant2.WarningMap(fmt.Sprintf("re_user_name: %s", req.RealName))
	}

	// 调用V2企业转账API
	ctx := context.Background()
	vant2.Primary("=== 开始调用微信转账API ===")
	wxRsp, err := client.Transfer(ctx, bm) // 使用V2的Transfer方法

	// 打印响应信息
	vant2.Primary("=== 微信API响应 ===")
	if wxRsp != nil {
		vant2.Primary(fmt.Sprintf("响应状态码: %s", wxRsp.ReturnCode))
		vant2.Primary(fmt.Sprintf("响应消息: %s", wxRsp.ReturnMsg))
		vant2.Primary(fmt.Sprintf("完整响应: %+v", wxRsp))
	} else {
		vant2.Primary("响应为空")
	}

	if err != nil {
		vant2.Primary(fmt.Sprintf("调用失败错误: %v", err))
		xlog.Error("微信转账失败:", err)
		return plus.TransferResponse{
			Success: false,
			Message: fmt.Sprintf("微信转账失败: %v", err),
		}
	}

	// // 检查响应状态
	// if wxRsp.Co != 200 {
	// 	return plus.TransferResponse{
	// 		Success: false,
	// 		Message: fmt.Sprintf("转账失败，错误码: %d, 错误信息: %s", wxRsp.Code, wxRsp.Error),
	// 	}
	// }

	// 构建返回结果
	return plus.TransferResponse{
		Success:       true,
		Message:       "转账成功",
		OutTradeNo:    outTradeNo,
		TransactionID: outTradeNo, // 使用商户订单号作为交易ID
		Amount:        fmt.Sprintf("%.2f", req.Amount),
		TransferTime:  time.Now().Format("2006-01-02 15:04:05"),
	}
}

/*
使用示例：

// 创建转账请求
transferReq := plus.TransferRequest{
	OpenID:      "用户的OpenID",
	RealName:    "张三",           // 可选，如果CheckName为FORCE_CHECK则必填
	Amount:      100.0,          // 转账金额，单位：元
	Description: "奖励金发放",      // 转账备注
	CheckName:   "NO_CHECK",     // NO_CHECK：不校验姓名，FORCE_CHECK：强校验姓名
	OutTradeNo:  "",            // 可选，不填则自动生成
}

// 调用转账
result := Transfer(transferReq)

if result.Success {
	fmt.Printf("转账成功，订单号：%s，金额：%s元\n", result.OutTradeNo, result.Amount)
} else {
	fmt.Printf("转账失败：%s\n", result.Message)
}
*/
