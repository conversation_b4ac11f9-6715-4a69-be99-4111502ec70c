package wechatpay

// 微信支付配置
var (
	// 微信公众号配置
	AppID     string = "wx11d53dcac7bb3357"
	AppSecret string = "2d692c019f0e13ad8982c810383117ae"

	// 微信支付配置
	MchID                      string = "1646855390"
	MchCertificateSerialNumber string = "3B3E95D6C8C1012CC8AD21BB8F4CB9DDCA929DCB"
	MchAPIv3Key                string = "C44942584F62AD4FF8773CD830D907BA"
	MchAPIv2Key                string = "C44942584F62AD4FF8773CD830D907BA"

	// 商户私钥
	MchPrivateKey string = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	// 回调地址
	NotifyUrl string = "http://chat.rxecs.com/chat/order/payBack"

	// 新增 OpenID
	OpenID string = "oQX-1234567890abcdef"
)
