package plus

// PayRequest 支付请求结构体
type PayRequest struct {
	OutTradeNo  string  `json:"out_trade_no"` // 商户订单号
	Description string  `json:"description"`  // 商品描述
	Amount      float64 `json:"amount"`       // 支付金额(元)
	ReturnUrl   string  `json:"return_url"`   // 同步返回地址(可选)
	UserOpenID  string  `json:"user_openid"`  // 微信用户OpenID(JSAPI支付用)
}

// PayResponse 支付响应结构体
type PayResponse struct {
	Success     bool                   `json:"success"`      // 是否成功
	Message     string                 `json:"message"`      // 消息
	PayUrl      string                 `json:"pay_url"`      // 支付URL(支付宝)
	CodeUrl     string                 `json:"code_url"`     // 二维码URL(微信Native)
	JSAPIParams map[string]interface{} `json:"jsapi_params"` // JSAPI支付参数(微信)
}

// CallbackData 回调数据结构体
type CallbackData struct {
	OutTradeNo    string `json:"out_trade_no"`   // 商户订单号
	TransactionID string `json:"transaction_id"` // 第三方交易号
	TradeStatus   string `json:"trade_status"`   // 交易状态
	Amount        string `json:"amount"`         // 支付金额
	PayTime       string `json:"pay_time"`       // 支付时间
	PayerInfo     string `json:"payer_info"`     // 付款方信息
}

// CallbackResult 回调处理结果
type CallbackResult struct {
	Success bool         `json:"success"` // 验证是否成功
	Message string       `json:"message"` // 错误信息
	Data    CallbackData `json:"data"`    // 回调数据
}

// TransferRequest 转账请求结构体
type TransferRequest struct {
	OpenID      string  `json:"openid"`       // 用户OpenID
	RealName    string  `json:"real_name"`    // 真实姓名(可选)
	Amount      float64 `json:"amount"`       // 转账金额(元)
	Description string  `json:"description"`  // 转账备注
	CheckName   string  `json:"check_name"`   // 校验姓名选项 NO_CHECK：不校验 FORCE_CHECK：强校验
	OutTradeNo  string  `json:"out_trade_no"` // 商户订单号(可选，不传则自动生成)
}

// TransferResponse 转账响应结构体
type TransferResponse struct {
	Success       bool   `json:"success"`        // 是否成功
	Message       string `json:"message"`        // 消息
	OutTradeNo    string `json:"out_trade_no"`   // 商户订单号
	TransactionID string `json:"transaction_id"` // 微信订单号
	Amount        string `json:"amount"`         // 转账金额
	TransferTime  string `json:"transfer_time"`  // 转账时间
}
