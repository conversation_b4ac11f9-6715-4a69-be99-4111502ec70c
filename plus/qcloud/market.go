package qcloud

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	gourl "net/url"
	"strings"
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

type Market struct {
}

// 企业列表
func (d *Market) CompanyList(key string) w.SliceMap {
	// 云市场分配的密钥Id
	_secretId := "AKIDn3F20eG67id9F7FwHK7bpY2dwSd6xsXQosE0"
	// 云市场分配的密钥Key
	_secretKey := "dKDLrMA1iv7858guL2OIb99RyKoFh1c62b2GMpui"
	source := "market"

	// 签名
	auth, datetime, _ := calcAuthorization(source, _secretId, _secretKey)

	// 请求方法
	method := "POST"
	// 请求头
	headers := map[string]string{"X-Source": source, "X-Date": datetime, "Authorization": auth}

	// body参数
	bodyParams := make(map[string]string)
	bodyParams["keyword"] = key
	bodyParams["pageNo"] = "1"
	bodyParams["pageSize"] = "20"
	// url参数拼接
	url := "https://service-la9pcjft-1305308687.sh.apigw.tencentcs.com/release/enterprise/business/query"

	bodyMethods := map[string]bool{"POST": true, "PUT": true, "PATCH": true}
	var body io.Reader = nil
	if bodyMethods[method] {
		body = strings.NewReader(urlencode(bodyParams))
		headers["Content-Type"] = "application/x-www-form-urlencoded"
	}

	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	request, _ := http.NewRequest(method, url, body)
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	response, _ := client.Do(request)
	defer response.Body.Close()

	bodyBytes, _ := ioutil.ReadAll(response.Body)

	var _dataMap map[string]interface{}
	json.Unmarshal([]byte(string(bodyBytes)), &_dataMap)
	vant2.Warning("Map 结构", _dataMap) //
	_list := vant2.Lodash(_dataMap, "data.result")
	if _list != nil {
		return gconv.SliceMap(_list)
	}
	return nil
}

// Weather 获取天气
func (d *Market) Weather() w.Map {
	// 云市场分配的密钥Id
	secretId := "AKID4kxxv21Q6A1eg7ss51oX214DiiGaaA38zFP7"
	// 云市场分配的密钥Key
	secretKey := "dj10lr3lQUDt7589Ajdn88PuBrC65095dc7aGxgz"
	source := "market"

	// 签名
	auth, datetime, _ := calcAuthorization(source, secretId, secretKey)

	// 请求方法
	method := "GET"
	// 请求头
	headers := map[string]string{"X-Source": source, "X-Date": datetime, "Authorization": auth}

	// 查询参数
	queryParams := make(map[string]string)
	queryParams["areaCn"] = "扬州" // 基础数据：http://img.lundear.com/weather_code.xlsx
	//queryParams["areaCode"] = ""
	//queryParams["ip"] = ""
	//queryParams["lat"] = ""
	//queryParams["lng"] = ""
	//queryParams["need1hour"] = ""
	//queryParams["need3hour"] = ""
	//queryParams["needIndex"] = ""
	//queryParams["needObserve"] = ""
	//queryParams["needalarm"] = ""
	// body参数
	bodyParams := make(map[string]string)

	// url参数拼接
	url := "https://service-6drgk6su-1258850945.gz.apigw.tencentcs.com/release/lundear/weather1d"
	if len(queryParams) > 0 {
		url = fmt.Sprintf("%s?%s", url, urlencode(queryParams))
	}

	bodyMethods := map[string]bool{"POST": true, "PUT": true, "PATCH": true}
	var body io.Reader = nil
	if bodyMethods[method] {
		body = strings.NewReader(urlencode(bodyParams))
		headers["Content-Type"] = "application/x-www-form-urlencoded"
	}

	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	request, err := http.NewRequest(method, url, body)
	if err != nil {
		panic(err)
	}
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	bodyBytes, err := ioutil.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	var _dataMap map[string]interface{}
	json.Unmarshal([]byte(string(bodyBytes)), &_dataMap)
	vant2.Warning("Map 结构", _dataMap) //
	return _dataMap
}

func calcAuthorization(source string, secretId string, secretKey string) (auth string, datetime string, err error) {
	timeLocation, _ := time.LoadLocation("Etc/GMT")
	datetime = time.Now().In(timeLocation).Format("Mon, 02 Jan 2006 15:04:05 GMT")
	signStr := fmt.Sprintf("x-date: %s\nx-source: %s", datetime, source)

	// hmac-sha1
	mac := hmac.New(sha1.New, []byte(secretKey))
	mac.Write([]byte(signStr))
	sign := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	auth = fmt.Sprintf("hmac id=\"%s\", algorithm=\"hmac-sha1\", headers=\"x-date x-source\", signature=\"%s\"",
		secretId, sign)

	return auth, datetime, nil
}

func urlencode(params map[string]string) string {
	var p = gourl.Values{}
	for k, v := range params {
		p.Add(k, v)
	}
	return p.Encode()
}
