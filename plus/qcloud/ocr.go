package qcloud

import (
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	ocr "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ocr/v20181119"
)

type Ocr struct {
}

// 生成 Credential 对象
func (d *Ocr) makeCredential(secretId, secretKey string) *common.Credential {
	return common.NewCredential(secretId, secretKey)
}

// OcrGeneral  调用OCR识别图片
func (d *Ocr) OcrGeneral(imgUrl string) (g.Map, error) {

	// 创建认证信息
	credential := d.makeCredential(secretId, secretKey)

	// 创建客户端配置
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "ocr.tencentcloudapi.com"

	// 创建 OCR 客户端
	client, _ := ocr.NewClient(credential, "ap-guangzhou", cpf)

	// 创建 OCR 请求
	request := ocr.NewSmartStructuralOCRV2Request()

	// 设置请求参数
	params := fmt.Sprintf(`{
        "ImageUrl": "%s",
        "IsPdf": false,
        "ConfigId": "General",
        "ReturnFullText": true
    }`, imgUrl)

	err := request.FromJsonString(params)
	if err != nil {
		return nil, err
	}

	// 发送请求
	response, err := client.SmartStructuralOCRV2(request)

	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("An API error has returned: %s", err)
		return nil, err
	}
	if err != nil {
		return nil, err
	}

	// 解析返回结果为 map 类型
	result := make(map[string]interface{})
	resultJson := response.ToJsonString()

	err = json.Unmarshal([]byte(resultJson), &result)
	if err != nil {
		return nil, err
	}
	_res, _ := ParseOcrResult(result)
	return _res, nil
}

// ParseOcrResult 将OCR结果解析为g.Map格式
// ParseOcrResult 将OCR结果解析为g.Map格式
func ParseOcrResult(ocrResult map[string]interface{}) (g.Map, error) {
	result := g.Map{}

	// 检查响应是否有效
	responseObj, ok := ocrResult["Response"]
	if !ok {
		return nil, fmt.Errorf("invalid response format: missing Response field")
	}

	response, ok := responseObj.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format: Response is not an object")
	}

	// 获取StructuralList
	structuralListObj, ok := response["StructuralList"]
	if !ok {
		return nil, fmt.Errorf("missing StructuralList in response")
	}

	structuralList, ok := structuralListObj.([]interface{})
	if !ok {
		return nil, fmt.Errorf("StructuralList is not an array")
	}

	// 遍历StructuralList
	for _, structObj := range structuralList {
		_struct, ok := structObj.(map[string]interface{})
		if !ok {
			continue
		}

		groupsObj, ok := _struct["Groups"]
		if !ok {
			continue
		}

		groups, ok := groupsObj.([]interface{})
		if !ok {
			continue
		}

		// 遍历Groups
		for _, groupObj := range groups {
			group, ok := groupObj.(map[string]interface{})
			if !ok {
				continue
			}

			linesObj, ok := group["Lines"]
			if !ok {
				continue
			}

			lines, ok := linesObj.([]interface{})
			if !ok {
				continue
			}

			// 遍历Lines
			for _, lineObj := range lines {
				line, ok := lineObj.(map[string]interface{})
				if !ok {
					continue
				}

				keyObj, ok := line["Key"]
				if !ok {
					continue
				}

				key, ok := keyObj.(map[string]interface{})
				if !ok {
					continue
				}

				valueObj, ok := line["Value"]
				if !ok {
					continue
				}

				value, ok := valueObj.(map[string]interface{})
				if !ok {
					continue
				}

				// 获取键名和值
				autoName, _ := key["AutoName"].(string)
				autoContent, _ := value["AutoContent"].(string)

				if autoName != "" && autoContent != "" {
					result[autoName] = autoContent
				}

				// 如果需要，也可以使用ConfigName作为键
				configName, _ := key["ConfigName"].(string)
				if configName != "" && autoContent != "" && autoName == "" {
					result[configName] = autoContent
				}
			}
		}
	}

	return result, nil
}
