# 腾讯云ASR一句话识别 使用说明

## 功能概述

这是一个简化、高效的腾讯云ASR（自动语音识别）一句话识别服务封装，支持：

- ✅ 本地音频文件识别
- ✅ 网络音频URL识别
- ✅ 智能输入类型判断
- ✅ 完善的文件验证（格式、大小、时长）
- ✅ 简洁的API接口（只返回文本和错误）
- ✅ 向后兼容旧版接口

## 支持的音频格式

- **mp3** - 推荐格式
- **wav** - 无损格式
- **m4a** - Apple格式
- **aac** - 高质量压缩
- **amr** - 移动设备格式
- **pcm** - 原始音频数据

## 限制条件

- 📏 **文件大小**: 最大 3MB
- ⏱️ **音频时长**: 最大 60秒
- 🌍 **语言支持**: 主要支持中文普通话（默认）

## 快速开始

### 1. 创建ASR实例

```go
import "assistant/plus/qcloud"

// 使用默认配置（推荐）
asr := qcloud.NewASR("", "")

// 或使用自定义配置
asr := qcloud.NewASR("your_secret_id", "your_secret_key")
```

### 2. 识别本地音频文件

```go
// 方法1: 使用专用方法
text, err := asr.RecognizeFromFile("static/demo.mp3")
if err != nil {
    fmt.Printf("识别失败: %v\n", err)
    return
}
fmt.Printf("识别结果: %s\n", text)
```

### 3. 识别网络音频

```go
// 需要提供音频格式
text, err := asr.RecognizeFromURL("https://example.com/audio.mp3", "mp3")
if err != nil {
    fmt.Printf("识别失败: %v\n", err)
    return
}
fmt.Printf("识别结果: %s\n", text)
```

### 4. 智能识别（推荐）

```go
// 自动判断是文件路径还是URL
text, err := asr.QuickRecognize("static/demo.mp3")
// 或者
text, err := asr.QuickRecognize("https://example.com/audio.mp3")

if err != nil {
    fmt.Printf("识别失败: %v\n", err)
    return
}
fmt.Printf("识别结果: %s\n", text)
```

## API参考

### NewASR(secretId, secretKey string) *ASR
创建ASR实例
- `secretId`: 腾讯云SecretId（空字符串使用默认配置）
- `secretKey`: 腾讯云SecretKey（空字符串使用默认配置）

### RecognizeFromFile(filePath string) (string, error)
识别本地音频文件
- `filePath`: 音频文件路径
- 返回: 识别文本, 错误信息

### RecognizeFromURL(audioURL, voiceFormat string) (string, error)
识别网络音频
- `audioURL`: 音频文件URL
- `voiceFormat`: 音频格式（mp3, wav等）
- 返回: 识别文本, 错误信息

### QuickRecognize(input string) (string, error)
智能识别（推荐使用）
- `input`: 文件路径或URL
- 返回: 识别文本, 错误信息

## 错误处理

服务会自动验证以下内容并返回相应错误：

```go
text, err := asr.RecognizeFromFile("audio.mp3")
if err != nil {
    switch {
    case strings.Contains(err.Error(), "不存在"):
        // 文件不存在
    case strings.Contains(err.Error(), "超过3MB"):
        // 文件太大
    case strings.Contains(err.Error(), "超过60秒"):
        // 音频太长
    case strings.Contains(err.Error(), "不支持的音频格式"):
        // 格式不支持
    case strings.Contains(err.Error(), "腾讯云API错误"):
        // API调用错误
    }
}
```

## 运行演示

项目中提供了完整的演示函数：

```go
import "assistant/plus/qcloud"

// 运行完整演示
qcloud.RunASRDemo()

// 或运行简单示例
qcloud.ASRSimpleExample()
```

## 实际使用示例

```go
package main

import (
    "assistant/plus/qcloud"
    "fmt"
    "log"
)

func main() {
    // 创建ASR实例
    asr := qcloud.NewASR("", "")

    // 测试文件: static/demo.mp3
    text, err := asr.QuickRecognize("static/demo.mp3")
    if err != nil {
        log.Fatal(err)
    }

    fmt.Printf("音频内容: %s\n", text)
}
```

## 配置说明

默认使用项目中的腾讯云配置：
```go
// 在 plus/qcloud/index.go 中
var (
    secretId  string = "AKIDc2B3f04rMaXCaiZ9SVHm0wbz6wWprb6K"
    secretKey string = "xe8MK1RbT4OREm34oHTO3SaQ0JL5ZnlS"
)
```

## 向后兼容

为保持兼容性，旧版接口仍然可用：

```go
// 旧版接口（仍然支持）
asr := &qcloud.ASR{}
text, err := asr.SentenceRecognitionSimple("static/demo.mp3")
```

## 性能优化建议

1. **文件大小**: 尽量使用压缩格式（mp3, aac）
2. **网络音频**: 推荐使用腾讯云COS存储，降低延迟
3. **批量处理**: 可复用ASR实例，避免重复创建
4. **错误处理**: 根据错误类型进行相应处理

## 常见问题

**Q: 为什么识别结果为空？**
A: 检查音频质量、格式是否正确，或音频内容是否清晰

**Q: 文件大小限制如何处理？**
A: 可以使用音频压缩工具减小文件大小，或分段处理

**Q: 支持实时语音识别吗？**
A: 当前版本仅支持一句话识别（60秒内），不支持实时流式识别

**Q: 如何处理网络音频？**
A: 确保URL可访问，音频格式正确，推荐使用CDN加速