package qcloud

//腾讯云的一些操作
import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"math/rand"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/os/gfile"
	aiart "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/aiart/v20221229"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	ocr "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ocr/v20181119"
	tmt "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tmt/v20180321"
	tts "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tts/v20190823"
)

type Translate struct {
}

var (
	secretId  string = "AKIDc2B3f04rMaXCaiZ9SVHm0wbz6wWprb6K"
	secretKey string = "xe8MK1RbT4OREm34oHTO3SaQ0JL5ZnlS"
)

// 生成 Credential 对象
func (d *Translate) makeCredential(secretId, secretKey string) *common.Credential {
	return common.NewCredential(secretId, secretKey)
}

// TranslateText  翻译函数
func (d *Translate) TranslateText(text string) (string, error) {

	credential := d.makeCredential(secretId, secretKey)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "tmt.tencentcloudapi.com"
	client, _ := tmt.NewClient(credential, "ap-guangzhou", cpf)

	request := tmt.NewTextTranslateRequest()
	params := fmt.Sprintf(`{"SourceText":"%s","Source":"auto","Target":"en","ProjectId":0}`, text)
	err := request.FromJsonString(params)
	if err != nil {
		return "", err
	}

	response, err := client.TextTranslate(request)

	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("An API error has returned: %s", err)
		return "", err
	}
	if err != nil {
		return "", err
	}

	return *response.Response.TargetText, nil
}

// 文字转声音
func (d *Translate) TTSConvert(text string) (string, string, error) {
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "tts.tencentcloudapi.com"
	client, _ := tts.NewClient(credential, "ap-guangzhou", cpf)

	request := tts.NewTextToVoiceRequest()

	sessionId := make([]byte, 16)
	rand.Read(sessionId)
	// VoiceType 设置说话的人和说话的声音 https://cloud.tencent.com/document/product/1073/92668
	params := fmt.Sprintf(`{"Text":"%s","ModelType":1,"VoiceType":101008,"SessionId":"%s","Volume":5}`, text, hex.EncodeToString(sessionId))
	err := request.FromJsonString(params)
	if err != nil {
		panic(err)
	}
	response, err := client.TextToVoice(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("An API error has returned: %s", err)
		return "", "", err
	}
	if err != nil {
		panic(err)
	}

	// Generate filename
	buf := make([]byte, 2)
	if _, err := rand.Read(buf); err != nil {
		panic(err)
	}
	filename := fmt.Sprintf("%d%s.mp3", time.Now().UnixNano()/int64(time.Millisecond), fmt.Sprintf("%x", buf))

	// Write mp3
	path := fmt.Sprintf("public/qcloud/tts/%s", filename)
	decodedAudio, err := base64.StdEncoding.DecodeString(*response.Response.Audio)
	if err != nil {
		fmt.Println("Error while decoding base64 audio:", err)
		return "", "", err
	}
	err = ioutil.WriteFile(path, decodedAudio, 0644)
	if err != nil {
		fmt.Println("Error while writing mp3 file:", err)
		return "", "", err
	}
	fmt.Println("Mp3 file written to:", path)

	// Convert to base64
	mp3Data := gfile.GetBytes(path)
	base64Audio := base64.StdEncoding.EncodeToString(mp3Data)

	fmt.Println("Finished converting text to speech.")
	return filename, base64Audio, nil
}

// ImageToImage 图生图
// 文档 https://cloud.tencent.com/document/product/1668/88066
func (d *Translate) ImageToImage(imageData []byte) error {
	credential := common.NewCredential(
		secretId,
		secretKey,
	)
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "aiart.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := aiart.NewClient(credential, "ap-guangzhou", cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := aiart.NewImageToImageRequest()
	inputImage := base64.StdEncoding.EncodeToString(imageData)
	// 绘画风格 https://cloud.tencent.com/document/product/1668/86250
	params := fmt.Sprintf(`{"Styles.N":["203"],"Strength":0.65}`)
	err := request.FromJsonString(params)
	request.InputImage = &inputImage

	// 返回的resp是一个ImageToImageResponse的实例，与请求对象对应
	response, err := client.ImageToImage(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("An API error has returned: %s", err)
		return err
	}
	if err != nil {
		panic(err)
	}

	// Generate filename
	buf := make([]byte, 2)
	if _, err := rand.Read(buf); err != nil {
		return err
	}
	filename := fmt.Sprintf("%d%s.jpg", time.Now().UnixNano()/int64(time.Millisecond), fmt.Sprintf("%x", buf))

	// Write image
	path := fmt.Sprintf("public/qcloud/aiart/%s", filename)
	imageBytes, err := base64.StdEncoding.DecodeString(*response.Response.ResultImage)
	err = ioutil.WriteFile(path, imageBytes, 0644)

	if err != nil {
		return err
	}

	fmt.Println("Generated image saved to:", path)
	return nil
}

// ImageToText 图片转文字
//
//	图片转文字
func (d *Translate) ImageToText(base64Pic string) (string, error) {
	if vant2.Str(base64Pic) == "" {
		return "", nil
	}

	credential := d.makeCredential(secretId, secretKey)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "ocr.tencentcloudapi.com"
	client, _ := ocr.NewClient(credential, "ap-guangzhou", cpf)

	request := ocr.NewGeneralBasicOCRRequest()

	params := fmt.Sprintf(`{"ImageBase64":"%s"}`, base64Pic)
	err := request.FromJsonString(params)
	if err != nil {
		return "", err
	}

	response, err := client.GeneralBasicOCR(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("An API error has returned: %s", err)
		return "", err
	}
	if err != nil {
		return "", err
	}

	var result string
	for _, textDetection := range response.Response.TextDetections {
		result += *textDetection.DetectedText + "\n"
	}
	return result, nil
}
