package qcloud

import (
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"

	asr "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/asr/v20190614"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

// ASRKeyPair 腾讯云密钥对
type ASRKeyPair struct {
	SecretId  string
	SecretKey string
	Name      string
}

// ASR 腾讯云语音识别结构体
type ASR struct{}

// 预定义的密钥对列表
var defaultASRKeys = []ASRKeyPair{
	{
		SecretId:  "AKIDc2ISO7f3HJ7mEalkDVaRUSFpUM4Zpy97",
		SecretKey: "pShnkV4RnzznOwJUZXM6BB5rW0LT6lUY",
		Name:      "key_1",
	},
	{
		SecretId:  "AKIDc2B3f04rMaXCaiZ9SVHm0wbz6wWprb6K",
		SecretKey: "xe8MK1RbT4OREm34oHTO3SaQ0JL5ZnlS",
		Name:      "key_2",
	},
	// {
	// 	SecretId:  "AKIDc2B3f04rMaXCaiZ9SVHm0wbz6wWprb6K",
	// 	SecretKey: "xe8MK1RbT4OREm34oHTO3SaQ0JL5ZnlS",
	// 	Name:      "key_2",
	// },
}

// 支持的音频格式
var supportedFormats = map[string]bool{
	".mp3":  true,
	".wav":  true,
	".m4a":  true,
	".aac":  true,
	".amr":  true,
	".pcm":  true,
	".webm": true, // 支持H5录音的webm格式
	".mp4":  true, // 支持H5录音的mp4格式
}

// NewASR 创建ASR实例
func NewASR() *ASR {
	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())
	return &ASR{}
}

// getRandomKeyPair 随机获取密钥对
func (a *ASR) getRandomKeyPair() ASRKeyPair {
	if len(defaultASRKeys) == 0 {
		return ASRKeyPair{
			SecretId:  "AKIDc2ISO7f3HJ7mEalkDVaRUSFpUM4Zpy97",
			SecretKey: "pShnkV4RnzznOwJUZXM6BB5rW0LT6lUY",
			Name:      "default",
		}
	}

	if len(defaultASRKeys) == 1 {
		return defaultASRKeys[0]
	}

	// 随机选择
	idx := rand.Intn(len(defaultASRKeys))
	return defaultASRKeys[idx]
}

// validateAudioFile 验证音频文件
func (a *ASR) validateAudioFile(filePath string) error {
	fmt.Printf("🔍 验证音频文件: %s\n", filePath)

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		fmt.Printf("❌ 文件不存在: %s\n", filePath)
		return fmt.Errorf("音频文件不存在: %s", filePath)
	}
	if err != nil {
		fmt.Printf("❌ 无法读取文件信息: %v\n", err)
		return fmt.Errorf("无法读取文件信息: %v", err)
	}

	fmt.Printf("📁 文件信息: 大小=%.2fMB, 修改时间=%s\n",
		float64(fileInfo.Size())/(1024*1024), fileInfo.ModTime().Format("2006-01-02 15:04:05"))

	// 检查文件大小 (不能超过3MB)
	maxSize := int64(3 * 1024 * 1024) // 3MB
	if fileInfo.Size() > maxSize {
		fmt.Printf("❌ 文件大小超过限制: %.2fMB > 3MB\n", float64(fileInfo.Size())/(1024*1024))
		return fmt.Errorf("音频文件大小超过3MB限制，当前大小: %.2fMB", float64(fileInfo.Size())/(1024*1024))
	}

	// 检查文件格式
	ext := strings.ToLower(filepath.Ext(filePath))
	fmt.Printf("🎵 音频格式: %s\n", ext)
	if !supportedFormats[ext] {
		fmt.Printf("❌ 不支持的音频格式: %s\n", ext)
		return fmt.Errorf("不支持的音频格式: %s，支持格式: mp3, wav, m4a, aac, amr, pcm", ext)
	}

	fmt.Printf("✅ 文件验证通过\n")
	return nil
}

// estimateAudioDuration 估算音频时长（简单估算）
func (a *ASR) estimateAudioDuration(filePath string, fileSize int64) error {
	ext := strings.ToLower(filepath.Ext(filePath))

	// 根据文件格式和大小估算时长
	var estimatedSeconds float64
	switch ext {
	case ".mp3":
		estimatedSeconds = float64(fileSize) / (128 * 1024 / 8) // 128kbps
	case ".wav":
		estimatedSeconds = float64(fileSize) / (44100 * 2 * 2) // 44.1kHz, 16bit, stereo
	case ".aac", ".m4a":
		estimatedSeconds = float64(fileSize) / (128 * 1024 / 8)
	default:
		estimatedSeconds = float64(fileSize) / (64 * 1024 / 8) // 64kbps
	}

	// 检查是否超过60秒限制
	if estimatedSeconds > 60 {
		return fmt.Errorf("音频时长可能超过60秒限制，估算时长: %.1f秒", estimatedSeconds)
	}

	return nil
}

// RecognizeFromFile 通过音频文件进行一句话识别
func (a *ASR) RecognizeFromFile(filePath string) (string, error) {
	// 验证文件
	if err := a.validateAudioFile(filePath); err != nil {
		return "", err
	}

	// 读取音频文件
	audioData, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("读取音频文件失败: %v", err)
	}

	// 估算音频时长
	if err := a.estimateAudioDuration(filePath, int64(len(audioData))); err != nil {
		return "", err
	}

	// 获取文件格式
	ext := strings.ToLower(filepath.Ext(filePath))
	voiceFormat := strings.TrimPrefix(ext, ".")

	// 将音频数据编码为base64
	base64Data := base64.StdEncoding.EncodeToString(audioData)

	// 调用识别API
	return a.recognizeFromData(base64Data, len(audioData), voiceFormat)
}

// RecognizeFromURL 通过音频URL进行一句话识别
func (a *ASR) RecognizeFromURL(audioURL, voiceFormat string) (string, error) {
	if audioURL == "" {
		return "", fmt.Errorf("音频URL不能为空")
	}

	// 默认格式
	if voiceFormat == "" {
		voiceFormat = "mp3"
	}

	// 验证格式
	if !supportedFormats["."+voiceFormat] {
		return "", fmt.Errorf("不支持的音频格式: %s", voiceFormat)
	}

	// 调用识别API
	return a.recognizeFromURL(audioURL, voiceFormat)
}

// recognizeFromData 从base64数据识别（内部方法）
func (a *ASR) recognizeFromData(base64Data string, dataLen int, voiceFormat string) (string, error) {
	// 随机获取密钥对
	keyPair := a.getRandomKeyPair()

	// 打印调试信息
	fmt.Printf("🔑 使用密钥: %s\n", keyPair.Name)
	fmt.Printf("📊 音频信息: 格式=%s, 大小=%d字节, Base64长度=%d\n", voiceFormat, dataLen, len(base64Data))

	// 创建认证信息
	credential := common.NewCredential(keyPair.SecretId, keyPair.SecretKey)

	// 创建客户端配置
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "asr.tencentcloudapi.com"

	// 创建ASR客户端
	client, err := asr.NewClient(credential, "ap-guangzhou", cpf)
	if err != nil {
		return "", fmt.Errorf("创建ASR客户端失败 [%s]: %v", keyPair.Name, err)
	}

	// 创建一句话识别请求
	request := asr.NewSentenceRecognitionRequest()

	// 设置引擎类型为中文通用
	engineType := "16k_zh"
	request.EngSerViceType = &engineType

	// 设置数据来源类型：1表示语音数据（base64编码）
	sourceType := uint64(1)
	request.SourceType = &sourceType

	// 设置音频格式 - 处理webm格式映射
	actualFormat := voiceFormat
	if voiceFormat == "webm" {
		// webm格式通常包含opus编码，腾讯云可能不直接支持
		// 尝试使用wav格式进行识别
		actualFormat = "wav"
		fmt.Printf("🔄 webm格式映射为wav格式进行识别\n")
	} else if voiceFormat == "mp4" {
		// mp4格式映射为m4a
		actualFormat = "m4a"
		fmt.Printf("🔄 mp4格式映射为m4a格式进行识别\n")
	}
	request.VoiceFormat = &actualFormat

	// 设置音频数据
	request.Data = &base64Data

	// 设置数据长度
	dataLength := int64(dataLen)
	request.DataLen = &dataLength

	fmt.Printf("📤 发送请求参数: EngSerViceType=%s, SourceType=%d, VoiceFormat=%s (原始:%s), DataLen=%d\n",
		engineType, sourceType, actualFormat, voiceFormat, dataLength)

	// 发送请求
	response, err := client.SentenceRecognition(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		// 打印详细的SDK错误信息
		if sdkErr, ok := err.(*errors.TencentCloudSDKError); ok {
			fmt.Printf("❌ 腾讯云SDK错误详情:\n")
			fmt.Printf("   Code: %s\n", sdkErr.Code)
			fmt.Printf("   Message: %s\n", sdkErr.Message)
			fmt.Printf("   RequestId: %s\n", sdkErr.RequestId)
		}
		return "", fmt.Errorf("腾讯云API错误 [%s]: %v", keyPair.Name, err)
	}
	if err != nil {
		return "", fmt.Errorf("请求失败 [%s]: %v", keyPair.Name, err)
	}

	// 打印响应信息
	fmt.Printf("📥 收到响应: RequestId=%s\n", *response.Response.RequestId)

	// 提取识别结果
	if response.Response.Result != nil {
		result := *response.Response.Result
		fmt.Printf("✅ 识别成功: %s\n", result)
		return result, nil
	}

	return "", fmt.Errorf("识别结果为空 [%s]", keyPair.Name)
}

// recognizeFromURL 从URL识别（内部方法）
func (a *ASR) recognizeFromURL(audioURL, voiceFormat string) (string, error) {
	// 随机获取密钥对
	keyPair := a.getRandomKeyPair()

	// 创建认证信息
	credential := common.NewCredential(keyPair.SecretId, keyPair.SecretKey)

	// 创建客户端配置
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "asr.tencentcloudapi.com"

	// 创建ASR客户端
	client, err := asr.NewClient(credential, "ap-guangzhou", cpf)
	if err != nil {
		return "", fmt.Errorf("创建ASR客户端失败 [%s]: %v", keyPair.Name, err)
	}

	// 创建一句话识别请求
	request := asr.NewSentenceRecognitionRequest()

	// 设置引擎类型为中文通用
	engineType := "16k_zh"
	request.EngSerViceType = &engineType

	// 设置数据来源类型：0表示语音URL
	sourceType := uint64(0)
	request.SourceType = &sourceType

	// 设置音频格式
	request.VoiceFormat = &voiceFormat

	// 设置音频URL
	request.Url = &audioURL

	// 发送请求
	response, err := client.SentenceRecognition(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		return "", fmt.Errorf("腾讯云API错误 [%s]: %v", keyPair.Name, err)
	}
	if err != nil {
		return "", fmt.Errorf("请求失败 [%s]: %v", keyPair.Name, err)
	}

	// 提取识别结果
	if response.Response.Result != nil {
		return *response.Response.Result, nil
	}

	return "", fmt.Errorf("识别结果为空 [%s]", keyPair.Name)
}

// QuickRecognize 快速识别（自动判断是文件路径还是URL）
func (a *ASR) QuickRecognize(input string) (string, error) {
	if input == "" {
		return "", fmt.Errorf("输入不能为空")
	}

	// 判断是否为URL
	if strings.HasPrefix(strings.ToLower(input), "http://") ||
		strings.HasPrefix(strings.ToLower(input), "https://") {
		// 从URL扩展名推断格式
		voiceFormat := "mp3" // 默认
		if strings.Contains(input, ".wav") {
			voiceFormat = "wav"
		} else if strings.Contains(input, ".m4a") {
			voiceFormat = "m4a"
		} else if strings.Contains(input, ".aac") {
			voiceFormat = "aac"
		}
		return a.RecognizeFromURL(input, voiceFormat)
	} else {
		// 作为文件路径处理
		return a.RecognizeFromFile(input)
	}
}

// 旧版兼容方法（保持向后兼容）
// SentenceRecognitionResult 一句话识别结果
type SentenceRecognitionResult struct {
	Result        string     `json:"result"`         // 识别结果
	AudioDuration int64      `json:"audio_duration"` // 音频时长(ms)
	WordSize      int64      `json:"word_size"`      // 词数量
	WordList      []WordInfo `json:"word_list"`      // 词时间戳列表
	RequestId     string     `json:"request_id"`     // 请求ID
}

// WordInfo 词时间戳信息
type WordInfo struct {
	Word      string `json:"word"`       // 词内容
	StartTime int64  `json:"start_time"` // 开始时间(ms)
	EndTime   int64  `json:"end_time"`   // 结束时间(ms)
}

// makeCredential 生成 Credential 对象（兼容旧版）
func (a *ASR) makeCredential(secretId, secretKey string) *common.Credential {
	return common.NewCredential(secretId, secretKey)
}

// SentenceRecognitionSimple 简化版一句话识别，只返回识别文本（兼容旧版）
func (a *ASR) SentenceRecognitionSimple(filePath string) (string, error) {
	return a.RecognizeFromFile(filePath)
}

// DebugRecognize 调试版本的语音识别，提供详细的调试信息
func (a *ASR) DebugRecognize(filePath string) (string, error) {
	fmt.Printf("\n🚀 开始调试语音识别\n")
	fmt.Printf("==================================================\n")

	// 验证文件
	if err := a.validateAudioFile(filePath); err != nil {
		return "", err
	}

	// 读取音频文件
	fmt.Printf("📖 读取音频文件...\n")
	audioData, err := ioutil.ReadFile(filePath)
	if err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		return "", fmt.Errorf("读取音频文件失败: %v", err)
	}
	fmt.Printf("✅ 文件读取成功，实际大小: %d 字节\n", len(audioData))

	// 检查文件内容（前几个字节）
	if len(audioData) > 0 {
		fmt.Printf("🔍 文件头部字节: ")
		for i := 0; i < 16 && i < len(audioData); i++ {
			fmt.Printf("%02X ", audioData[i])
		}
		fmt.Printf("\n")

		// 检查MP3文件头
		if len(audioData) >= 3 {
			if audioData[0] == 0xFF && (audioData[1]&0xE0) == 0xE0 {
				fmt.Printf("✅ 检测到有效的MP3文件头\n")
			} else if audioData[0] == 0x49 && audioData[1] == 0x44 && audioData[2] == 0x33 {
				fmt.Printf("✅ 检测到ID3标签，这是有效的MP3文件\n")
			} else {
				fmt.Printf("⚠️ 文件头不符合标准MP3格式\n")
				fmt.Printf("   期望: FF Ex 或 ID3 标签\n")
				fmt.Printf("   实际: %02X %02X %02X\n", audioData[0], audioData[1], audioData[2])
			}
		}

		// 检查文件是否全为零（空文件）
		allZero := true
		for i := 0; i < len(audioData) && i < 1000; i++ {
			if audioData[i] != 0 {
				allZero = false
				break
			}
		}
		if allZero {
			fmt.Printf("❌ 警告：文件前1000字节全为零，可能是空音频文件\n")
		}
	}

	// 估算音频时长
	if err := a.estimateAudioDuration(filePath, int64(len(audioData))); err != nil {
		fmt.Printf("⚠️ 时长估算警告: %v\n", err)
		return "", err
	}

	// 获取文件格式
	ext := strings.ToLower(filepath.Ext(filePath))
	voiceFormat := strings.TrimPrefix(ext, ".")
	fmt.Printf("🎵 使用音频格式: %s\n", voiceFormat)

	// 将音频数据编码为base64
	fmt.Printf("🔄 编码为Base64...\n")
	base64Data := base64.StdEncoding.EncodeToString(audioData)
	fmt.Printf("✅ Base64编码完成，长度: %d\n", len(base64Data))

	// 调用识别API
	fmt.Printf("🌐 调用腾讯云API...\n")
	result, err := a.recognizeFromData(base64Data, len(audioData), voiceFormat)

	fmt.Printf("==================================================\n")
	if err != nil {
		fmt.Printf("❌ 识别失败: %v\n", err)
	} else {
		fmt.Printf("🎉 识别完成!\n")
	}

	return result, err
}

// TestAllKeys 测试所有密钥对的可用性
func (a *ASR) TestAllKeys() {
	fmt.Printf("\n🔑 测试所有密钥对的可用性\n")
	fmt.Printf("==================================================\n")

	for i, keyPair := range defaultASRKeys {
		fmt.Printf("测试密钥 %d: %s\n", i+1, keyPair.Name)
		fmt.Printf("SecretId: %s\n", keyPair.SecretId)

		// 创建认证信息
		credential := common.NewCredential(keyPair.SecretId, keyPair.SecretKey)

		// 创建客户端配置
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = "asr.tencentcloudapi.com"

		// 尝试创建ASR客户端
		client, err := asr.NewClient(credential, "ap-guangzhou", cpf)
		if err != nil {
			fmt.Printf("❌ 创建客户端失败: %v\n", err)
		} else {
			fmt.Printf("✅ 客户端创建成功\n")
			_ = client // 避免未使用变量警告
		}
		fmt.Printf("\n")
	}
}

// AnalyzeAudioFile 分析音频文件的详细信息
func (a *ASR) AnalyzeAudioFile(filePath string) {
	fmt.Printf("\n🔬 详细分析音频文件: %s\n", filePath)
	fmt.Printf("==================================================\n")

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		fmt.Printf("❌ 无法访问文件: %v\n", err)
		return
	}

	fmt.Printf("📁 文件基本信息:\n")
	fmt.Printf("   大小: %d 字节 (%.2f KB)\n", fileInfo.Size(), float64(fileInfo.Size())/1024)
	fmt.Printf("   修改时间: %s\n", fileInfo.ModTime().Format("2006-01-02 15:04:05"))

	// 读取文件内容
	audioData, err := ioutil.ReadFile(filePath)
	if err != nil {
		fmt.Printf("❌ 读取文件失败: %v\n", err)
		return
	}

	fmt.Printf("\n🔍 文件内容分析:\n")
	fmt.Printf("   实际读取大小: %d 字节\n", len(audioData))

	if len(audioData) == 0 {
		fmt.Printf("❌ 文件为空!\n")
		return
	}

	// 显示文件头部
	fmt.Printf("   文件头部 (前32字节): ")
	for i := 0; i < 32 && i < len(audioData); i++ {
		fmt.Printf("%02X ", audioData[i])
		if (i+1)%16 == 0 {
			fmt.Printf("\n                              ")
		}
	}
	fmt.Printf("\n")

	// MP3格式检查
	fmt.Printf("\n🎵 MP3格式检查:\n")
	if len(audioData) >= 3 {
		if audioData[0] == 0xFF && (audioData[1]&0xE0) == 0xE0 {
			fmt.Printf("   ✅ 检测到MP3同步字 (FF Ex)\n")
		} else if audioData[0] == 0x49 && audioData[1] == 0x44 && audioData[2] == 0x33 {
			fmt.Printf("   ✅ 检测到ID3v2标签\n")
			if len(audioData) >= 10 {
				// ID3v2标签大小
				size := int(audioData[6])<<21 | int(audioData[7])<<14 | int(audioData[8])<<7 | int(audioData[9])
				fmt.Printf("   ID3标签大小: %d 字节\n", size)
			}
		} else {
			fmt.Printf("   ⚠️ 未检测到标准MP3文件头\n")
			fmt.Printf("   前3字节: %02X %02X %02X\n", audioData[0], audioData[1], audioData[2])
		}
	}

	// 检查是否为静音文件
	fmt.Printf("\n🔇 静音检查:\n")
	zeroCount := 0
	for i := 0; i < len(audioData) && i < 10000; i++ {
		if audioData[i] == 0 {
			zeroCount++
		}
	}
	checkSize := 10000
	if len(audioData) < checkSize {
		checkSize = len(audioData)
	}
	zeroPercent := float64(zeroCount) / float64(checkSize) * 100
	fmt.Printf("   前%d字节中零字节占比: %.1f%%\n", checkSize, zeroPercent)

	if zeroPercent > 90 {
		fmt.Printf("   ❌ 警告: 文件可能是静音或损坏的\n")
	} else if zeroPercent > 50 {
		fmt.Printf("   ⚠️ 注意: 文件可能包含大量静音\n")
	} else {
		fmt.Printf("   ✅ 文件看起来包含有效的音频数据\n")
	}
}
