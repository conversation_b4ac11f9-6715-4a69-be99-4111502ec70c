package alipay

// 支付宝支付配置
var (
	// AppId
	AppId string = "2021004103674085"

	// 私钥（使用服务器日志中的工作私钥）
	PrivateKey string = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCEBO02me2Obb4YjvXTypFq5SB32IGxQW0TRnF0fNskCuO7DOZrzBATScyc4pGbUXokqKuGo/C6LA1xeUQRcuh57ehZNhSlp4QK+wJhDcbeUY5cDSoR8y072P1VeK5W5RWT6q9VtJG3k0tXHt2mXpLzQfe0vt7BXQVKuPcP7PmNg+Uc4uIGka/eQP6CVu9Q5VqJGdlw32qgwqbEHWDX0xoc4FqnPg6YboIhpAp4835uvLWPzgWWyOkNWiWU5YLYTCoF0jgyPogfDRvmFYqlCFFHTVGRlnnmG//JHLCJ5NKU1dLMwdHVcaV0M7MlGUPcgwBtR60khR5IhOo0V1zuexUxAgMBAAECggEBAIATt35tPwYaeX2h0+OwdftaZnFeXhP1ATjd8lusdX02A1EIZtLVmhLcWeEp/VDNbs35xM5OZdB3xGL93od/DwKbR/qkeMttBLESa3B0ySYNwSqUqufXH18cWEl/VsN+Y7yoxYlEMMoD3+eZTjeFMNNGgGWddsXo1KV1ZFrS8BfVGqLwWaheMySm1qXW5PLG/SboIyTKXJLZ6EVSz8KHztI1psfNNKql1jbs91EHr4+hOIhV1mAh4cU/PWwAD0roFB0vGslMNo3rI9JLNsMPeueQk9p+weStyavj7d8QoBm5ve6mcsJVxJSY9BG1SUeBAXTcrNnYj3IP6hvoksRvlbECgYEA6VD7R2iGKIL3R4BIIHOud4m8RW81kkuamI2d5DNq7zZRzh0096DLOoxAkS+PE7ju3lyizem4zmGOMNNtoemoRUrmYYWJFCzeV9Zxf+EJttJTmyi5GKXqNXZd3uXZVdQEffQiBNM35FFaYvmKcgXiZER6kzC30fT8Qj5ttkm1DUMCgYEAkNrCG4ZB620HSB4blbLCkllDKBQZMb8fN48t/NqvUSz8MUtqp/etEYN/mfUEvyOTy4h4nEvlbrrulrOYZh1EyxW2UcjyNEWJ9ySAZVnZV8yf7Pad12NnH3ee+IpEBYulOCTudJG1h/OOb2NsTytyZh4ElB63yraWHqkHiEfFEnsCgYBV6kktqXLFljyCqt1kfdTAFgsrMmFyxr5d8MA0mS6GeeUz1hN8IEzV+XGJRQ8VvyLO5SPaWXeBH/FhT2nuloEtSwB85tRshn01sTTIV1Dhbp/04dEPH4GYQEce3lrivRkOXIjuZ+0L+V0kVynBBSOxsnnEm8sE2IbPyfdSid/OywKBgDr2EFSLzYE6m+ArASdp3/EaDE/f/nFVTiubSCjICtyL5wtZm0xLcFLUp7fPF+OFY79RCWb1LapoxHzrFXlbKIlRiSi5Fn1YYNQMM4cNj35BeEdICu+FMpCIbFjWTuHC+blkrvsb5Dw2Ux/b5FZFZQdT6KEx77W/pFNOkhZSwFDJAoGBAMVRlnHRyCa2nz1ofHKyb/yGdX02aH/5XIA8/FVP0wcDCYgxN3h4H6p1C4DMmvvpQHcI9rvvbXn7IbhTHXEUNknYr5OrRkTw7Q5KMrjUPKIG/F1LTX61HkQXQB8eS2SLOscjw4W/Iqh2T/AaZEsShCYS2Bz7xUk3v5ZKkmOzKG6o"

	// 证书配置（证书模式需要）
	AppCertPath          string = "static/alipay/appCertPublicKey_2021004103674085.crt" // 应用公钥证书路径
	AlipayRootCertPath   string = "static/alipay/alipayRootCert.crt"                    // 支付宝根证书路径
	AlipayPublicCertPath string = "static/alipay/alipayCertPublicKey_RSA2.crt"          // 支付宝公钥证书路径

	// 回调和返回地址配置
	ReturnUrl string = "http://chat.rxecs.com/chat/order/alipayWebBack"
	NotifyUrl string = "http://chat.rxecs.com/chat/order/alipayWebBack"
)
