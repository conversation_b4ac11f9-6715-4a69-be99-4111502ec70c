package alipay

import (
	"assistant/plus"
	"context"
	"fmt"

	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay"
)

// CreatePayment 创建支付宝支付
func CreatePayment(req plus.PayRequest) plus.PayResponse {
	// 使用私钥字符串初始化客户端
	client, err := alipay.NewClient(AppId, PrivateKey, true)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("初始化支付宝客户端失败: %v", err),
		}
	}

	// 设置证书模式
	err = client.SetCertSnByPath(AppCertPath, AlipayRootCertPath, AlipayPublicCertPath)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("设置证书失败: %v", err),
		}
	}

	// 打开Debug开关，输出日志，默认关闭
	client.DebugSwitch = gopay.DebugOn

	client.SetLocation(alipay.LocationShanghai). // 设置时区，不设置或出错均为默认服务器时间
							SetCharset(alipay.UTF8).  // 设置字符编码，不设置默认 utf-8
							SetSignType(alipay.RSA2). // 设置签名类型，不设置默认 RSA2
							SetReturnUrl(ReturnUrl).  // 设置返回URL
							SetNotifyUrl(NotifyUrl)   // 设置异步通知URL

	bm := make(gopay.BodyMap)

	// 设置参数 - 完全按照原代码的方式
	bm.Set("subject", req.Description).
		Set("scene", "bar_code").
		Set("auth_code", req.OutTradeNo).
		Set("out_trade_no", req.OutTradeNo).
		Set("total_amount", req.Amount).
		Set("timeout_express", "2m")

	_ctx := context.Background() // 创建一个空的上下文对象
	aliRsp, err := client.TradePagePay(_ctx, bm)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("创建支付宝订单失败: %v", err),
		}
	}

	return plus.PayResponse{
		Success: true,
		Message: "支付宝订单创建成功",
		PayUrl:  aliRsp,
	}
}

// CreateWapPayment 创建支付宝手机网站支付
func CreateWapPayment(req plus.PayRequest) plus.PayResponse {
	// 初始化
	client, err := alipay.NewClient(AppId, PrivateKey, true)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("初始化支付宝客户端失败: %v", err),
		}
	}

	// 设置证书模式
	err = client.SetCertSnByPath(AppCertPath, AlipayRootCertPath, AlipayPublicCertPath)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("设置证书失败: %v", err),
		}
	}

	client.DebugSwitch = gopay.DebugOff
	client.SetLocation(alipay.LocationShanghai).
		SetCharset(alipay.UTF8).
		SetSignType(alipay.RSA2).
		SetNotifyUrl(NotifyUrl)

	if req.ReturnUrl != "" {
		client.SetReturnUrl(req.ReturnUrl)
	} else {
		client.SetReturnUrl(ReturnUrl)
	}

	bm := make(gopay.BodyMap)
	bm.Set("subject", req.Description).
		Set("out_trade_no", req.OutTradeNo).
		Set("total_amount", req.Amount).
		Set("product_code", "QUICK_WAP_WAY")

	ctx := context.Background()
	payUrl, err := client.TradeWapPay(ctx, bm)
	if err != nil {
		return plus.PayResponse{
			Success: false,
			Message: fmt.Sprintf("创建支付宝手机订单失败: %v", err),
		}
	}

	return plus.PayResponse{
		Success: true,
		Message: "支付宝手机订单创建成功",
		PayUrl:  payUrl,
	}
}

// ParseCallback 解析支付宝回调
func ParseCallback(params map[string]string) plus.CallbackResult {
	if len(params) == 0 {
		return plus.CallbackResult{
			Success: false,
			Message: "未收到支付宝回调参数",
		}
	}

	// 提取关键信息
	data := plus.CallbackData{
		OutTradeNo:    params["out_trade_no"],
		TransactionID: params["trade_no"],
		TradeStatus:   params["trade_status"],
		Amount:        params["total_amount"],
		PayTime:       params["gmt_payment"],
		PayerInfo:     params["buyer_logon_id"],
	}

	return plus.CallbackResult{
		Success: true,
		Message: "支付宝回调解析成功",
		Data:    data,
	}
}

// QueryPayment 查询支付状态
func QueryPayment(outTradeNo string) plus.CallbackResult {
	client, err := alipay.NewClient(AppId, PrivateKey, false)
	if err != nil {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("初始化支付宝客户端失败: %v", err),
		}
	}

	client.DebugSwitch = gopay.DebugOff
	client.SetLocation(alipay.LocationShanghai).
		SetCharset(alipay.UTF8).
		SetSignType(alipay.RSA2)

	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", outTradeNo)

	ctx := context.Background()
	rsp, err := client.TradeQuery(ctx, bm)
	if err != nil {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("查询支付宝订单失败: %v", err),
		}
	}

	if rsp.Response.Code != "10000" {
		return plus.CallbackResult{
			Success: false,
			Message: fmt.Sprintf("查询失败: %s", rsp.Response.Msg),
		}
	}

	data := plus.CallbackData{
		OutTradeNo:    rsp.Response.OutTradeNo,
		TransactionID: rsp.Response.TradeNo,
		TradeStatus:   rsp.Response.TradeStatus,
		Amount:        rsp.Response.TotalAmount,
		PayTime:       rsp.Response.SendPayDate,
		PayerInfo:     rsp.Response.BuyerLogonId,
	}

	return plus.CallbackResult{
		Success: true,
		Message: "查询成功",
		Data:    data,
	}
}
