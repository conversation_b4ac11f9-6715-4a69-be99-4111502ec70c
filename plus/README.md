# Plus - 第三方支付模块

纯净的第三方支付工具库，专注于支付接入和完成支付功能。

## 特点

- 🎯 **职责单一** - 只负责支付接入，不包含任何业务逻辑
- 🚀 **完全独立** - 不依赖任何框架，可在任意Go项目中使用
- 📦 **统一接口** - 支付宝和微信支付使用相同的数据结构
- 🔧 **简单易用** - API设计简洁，方法签名清晰

## 目录结构

```
plus/
├── pay.go              # 统一数据结构定义
├── alipay/             # 支付宝支付模块
│   ├── config.go       # 支付宝配置
│   └── pay.go          # 支付宝支付方法
├── wechatpay/          # 微信支付模块
│   ├── config.go       # 微信支付配置
│   └── pay.go          # 微信支付方法
├── example/            # 使用示例
│   └── demo.go         # GoFrame Demo
└── README.md           # 说明文档
```

## 数据结构

### PayRequest - 支付请求
```go
type PayRequest struct {
    OutTradeNo  string  `json:"out_trade_no"`  // 商户订单号
    Description string  `json:"description"`   // 商品描述
    Amount      float64 `json:"amount"`        // 支付金额(元)
    ReturnUrl   string  `json:"return_url"`    // 同步返回地址(可选)
}
```

### PayResponse - 支付响应
```go
type PayResponse struct {
    Success     bool                   `json:"success"`      // 是否成功
    Message     string                 `json:"message"`      // 消息
    PayUrl      string                 `json:"pay_url"`      // 支付URL(支付宝)
    CodeUrl     string                 `json:"code_url"`     // 二维码URL(微信Native)
    JSAPIParams map[string]interface{} `json:"jsapi_params"` // JSAPI支付参数(微信)
}
```

### CallbackData - 回调数据
```go
type CallbackData struct {
    OutTradeNo    string `json:"out_trade_no"`    // 商户订单号
    TransactionID string `json:"transaction_id"`  // 第三方交易号
    TradeStatus   string `json:"trade_status"`    // 交易状态
    Amount        string `json:"amount"`          // 支付金额
    PayTime       string `json:"pay_time"`        // 支付时间
    PayerInfo     string `json:"payer_info"`      // 付款方信息
}
```

## 支付宝使用方法

### 1. 配置
在 `plus/alipay/config.go` 中配置你的支付宝参数：
```go
const (
    AppId       = "your_app_id"
    PrivateKey  = "your_private_key"
    NotifyUrl   = "https://yourdomain.com/alipay/notify"
    ReturnUrl   = "https://yourdomain.com/success"
)
```

### 2. 创建支付
```go
import "assistant/plus/alipay"

// PC网站支付
req := plus.PayRequest{
    OutTradeNo:  "ORDER_123456",
    Description: "测试商品",
    Amount:      0.01,
}
result := alipay.CreatePayment(req)

// 手机网站支付
result := alipay.CreateWapPayment(req)
```

### 3. 处理回调
```go
// 解析支付宝回调
params := map[string]string{} // 从HTTP请求中获取参数
result := alipay.ParseCallback(params)
if result.Success {
    // 支付成功，处理业务逻辑
    fmt.Println("订单号:", result.Data.OutTradeNo)
    fmt.Println("支付金额:", result.Data.Amount)
}
```

## 微信支付使用方法

### 1. 配置
在 `plus/wechatpay/config.go` 中配置你的微信支付参数：
```go
const (
    AppID                        = "wx1234567890abcdef"
    MchID                        = "1234567890"
    MchCertificateSerialNumber   = "FEDCBA9876543210"
    MchAPIv3Key                  = "your_mch_api_v3_key_32_chars"
    NotifyUrl                    = "https://yourdomain.com/wechat/notify"
    OpenID                       = "oQX-1234567890abcdef"
)
```

### 2. 创建支付
```go
import "assistant/plus/wechatpay"

req := plus.PayRequest{
    OutTradeNo:  "ORDER_123456",
    Description: "测试商品",
    Amount:      0.01,
}

// JSAPI支付（微信内H5）
result := wechatpay.CreateJSAPIPayment(req)

// Native支付（扫码支付）
result := wechatpay.CreateNativePayment(req)
```

### 3. 查询支付状态
```go
result := wechatpay.QueryPayment("ORDER_123456")
if result.Success {
    fmt.Println("支付状态:", result.Data.TradeStatus)
}
```

## Demo运行

```bash
# 启动演示程序
go run plus/example/demo.go

# 访问 http://localhost:8080
# 包含支付宝PC支付、手机支付、微信扫码支付、微信JSAPI支付的完整示例
```

## 注意事项

1. **配置修改** - 使用前必须修改各自的配置文件，填入真实的支付参数
2. **回调地址** - 确保回调地址可以被支付平台访问到
3. **HTTPS** - 生产环境建议使用HTTPS
4. **金额单位** - 输入金额单位为元，内部会自动转换为分/厘
5. **微信回调** - 微信支付回调解析功能暂未完善，建议使用QueryPayment查询支付状态

## 业务集成建议

此模块只负责支付接入，业务层应该：

1. **订单管理** - 在业务层维护订单状态
2. **数据库操作** - 在回调成功后更新订单状态到数据库
3. **库存管理** - 支付成功后处理库存扣减
4. **用户通知** - 支付成功后发送通知给用户
5. **日志记录** - 记录支付相关的业务日志

```go
// 业务层示例
func ProcessPaymentCallback(outTradeNo string) {
    // 1. 查询支付状态
    result := alipay.QueryPayment(outTradeNo)

    if result.Success && result.Data.TradeStatus == "TRADE_SUCCESS" {
        // 2. 更新订单状态
        updateOrderStatus(outTradeNo, "paid")

        // 3. 扣减库存
        deductInventory(outTradeNo)

        // 4. 发送通知
        sendNotification(outTradeNo)

        // 5. 记录日志
        logPaymentSuccess(result.Data)
    }
}
```