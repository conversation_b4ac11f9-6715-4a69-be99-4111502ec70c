package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"
	"vant2"

	"assistant/plus"
	"assistant/plus/alipay"
	"assistant/plus/wechatpay"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/glog"
)

func main() {
	s := g.Server()

	// 支付宝支付页面
	// "http://localhost:8080/alipay"
	s.BindHandler("/alipay", func(r *ghttp.Request) {
		// 创建支付宝订单
		req := plus.PayRequest{
			OutTradeNo:  fmt.Sprintf("AP%d", time.Now().Unix()),
			Description: "测试商品-支付宝1分钱",
			Amount:      0.01, // 1分钱
			// ReturnUrl:   "http://localhost:8080/success",
		}

		result := alipay.CreatePayment(req)
		vant2.Error(result, "alipay-result")
		if !result.Success {
			r.Response.WriteJson(g.Map{
				"error": result.Message,
			})
			return
		}

		// 直接跳转到支付页面
		r.Response.RedirectTo(result.PayUrl)
	})

	// 支付宝手机支付页面
	s.BindHandler("/alipay-wap", func(r *ghttp.Request) {
		req := plus.PayRequest{
			OutTradeNo:  fmt.Sprintf("AW%d", time.Now().Unix()),
			Description: "测试商品-支付宝手机1分钱",
			Amount:      0.01,
			ReturnUrl:   "http://localhost:8080/success",
		}

		result := alipay.CreateWapPayment(req)
		if !result.Success {
			r.Response.WriteJson(g.Map{
				"error": result.Message,
			})
			return
		}

		r.Response.RedirectTo(result.PayUrl)
	})

	// 微信Native支付页面
	s.BindHandler("/wechat-native", func(r *ghttp.Request) {
		req := plus.PayRequest{
			OutTradeNo:  fmt.Sprintf("WN%d", time.Now().Unix()),
			Description: "测试商品-微信Native1分钱",
			Amount:      0.01,
		}

		result := wechatpay.CreateNativePayment(req)
		if !result.Success {
			r.Response.WriteJson(g.Map{
				"error": result.Message,
			})
			return
		}

		// 返回二维码页面
		html := fmt.Sprintf(`
		<!DOCTYPE html>
		<html>
		<head>
			<title>微信支付</title>
			<script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
		</head>
		<body>
			<h1>微信扫码支付 - 1分钱</h1>
			<div id="qrcode"></div>
			<p>订单号：%s</p>
			<script>
				var qr = qrcode(0, 'M');
				qr.addData('%s');
				qr.make();
				document.getElementById('qrcode').innerHTML = qr.createImgTag(5);
			</script>
		</body>
		</html>
		`, req.OutTradeNo, result.CodeUrl)

		r.Response.Write(html)
	})

	// 微信JSAPI支付页面
	s.BindHandler("/wechat-jsapi", func(r *ghttp.Request) {
		req := plus.PayRequest{
			OutTradeNo:  fmt.Sprintf("WJ%d", time.Now().Unix()),
			Description: "测试商品-微信JSAPI1分钱",
			Amount:      0.01,
		}

		result := wechatpay.CreateJSAPIPayment(req)
		if !result.Success {
			r.Response.WriteJson(g.Map{
				"error": result.Message,
			})
			return
		}

		// 返回JSAPI支付页面
		jsParams, _ := json.Marshal(result.JSAPIParams)
		html := fmt.Sprintf(`
		<!DOCTYPE html>
		<html>
		<head>
			<title>微信JSAPI支付</title>
			<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
		</head>
		<body>
			<h1>微信JSAPI支付 - 1分钱</h1>
			<button onclick="pay()">立即支付</button>
			<p>订单号：%s</p>
			<script>
				function pay() {
					var params = %s;
					if (typeof WeixinJSBridge === 'undefined') {
						alert('请在微信客户端打开');
						return;
					}
					WeixinJSBridge.invoke('getBrandWCPayRequest', params, function(res) {
						if (res.err_msg === 'get_brand_wcpay_request:ok') {
							alert('支付成功');
							window.location.href = '/success';
						} else {
							alert('支付失败：' + res.err_msg);
						}
					});
				}
			</script>
		</body>
		</html>
		`, req.OutTradeNo, string(jsParams))

		r.Response.Write(html)
	})

	// 支付成功页面
	s.BindHandler("/success", func(r *ghttp.Request) {
		r.Response.Write(`
		<!DOCTYPE html>
		<html>
		<head><title>支付成功</title></head>
		<body>
			<h1>🎉 支付成功！</h1>
			<p><a href="/">返回首页</a></p>
		</body>
		</html>
		`)
	})

	// 支付宝回调
	s.BindHandler("/alipay/notify", func(r *ghttp.Request) {
		// 获取所有POST参数
		params := make(map[string]string)
		for key, value := range r.GetFormMap() {
			if value != nil {
				params[key] = value.(string)
			}
		}

		// 解析回调
		result := alipay.ParseCallback(params)
		ctx := context.Background()
		if result.Success {
			glog.Info(ctx, "💰 支付宝回调成功！")
			glog.Infof(ctx, "订单号：%s", result.Data.OutTradeNo)
			glog.Infof(ctx, "支付宝流水号：%s", result.Data.TransactionID)
			glog.Infof(ctx, "交易状态：%s", result.Data.TradeStatus)
			glog.Infof(ctx, "支付金额：%s元", result.Data.Amount)
			glog.Infof(ctx, "支付时间：%s", result.Data.PayTime)
			glog.Infof(ctx, "付款方：%s", result.Data.PayerInfo)
		} else {
			glog.Errorf(ctx, "支付宝回调失败：%s", result.Message)
		}

		r.Response.Write("success")
	})

	// 微信回调
	s.BindHandler("/wechat/notify", func(r *ghttp.Request) {
		// 获取请求体和头部
		body := r.GetBody()
		headers := make(map[string]string)
		for key, values := range r.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}

		// 解析回调
		result := wechatpay.ParseCallback(body, headers)
		ctx := context.Background()
		if result.Success {
			glog.Info(ctx, "💰 微信支付回调成功！")
			glog.Infof(ctx, "订单号：%s", result.Data.OutTradeNo)
			glog.Infof(ctx, "微信流水号：%s", result.Data.TransactionID)
			glog.Infof(ctx, "交易状态：%s", result.Data.TradeStatus)
			glog.Infof(ctx, "支付金额：%s元", result.Data.Amount)
			glog.Infof(ctx, "支付时间：%s", result.Data.PayTime)
			glog.Infof(ctx, "付款方：%s", result.Data.PayerInfo)
		} else {
			glog.Errorf(ctx, "微信支付回调失败：%s", result.Message)
		}

		r.Response.WriteJson(g.Map{
			"code":    "SUCCESS",
			"message": "成功",
		})
	})

	// 首页
	s.BindHandler("/", func(r *ghttp.Request) {
		r.Response.Write(`
		<!DOCTYPE html>
		<html>
		<head>
			<title>支付测试Demo</title>
			<style>
				body { font-family: Arial; margin: 50px; }
				.btn { display: block; margin: 10px 0; padding: 15px; background: #007cba; color: white; text-decoration: none; text-align: center; border-radius: 5px; }
				.btn:hover { background: #005a87; }
			</style>
		</head>
		<body>
			<h1>💰 支付测试Demo - 1分钱</h1>
			<a href="/alipay" class="btn">支付宝PC支付</a>
			<a href="/alipay-wap" class="btn">支付宝手机支付</a>
			<a href="/wechat-native" class="btn">微信扫码支付</a>
			<a href="/wechat-jsapi" class="btn">微信JSAPI支付</a>
		</body>
		</html>
		`)
	})

	log.Println("🚀 支付Demo启动成功！")
	log.Println("🌐 访问地址：http://localhost:8080")
	log.Println("📋 支付宝回调地址：http://localhost:8080/alipay/notify")
	log.Println("📋 微信回调地址：http://localhost:8080/wechat/notify")

	s.SetPort(8080)
	s.Run()
}
