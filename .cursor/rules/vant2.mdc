---
description:
globs:*.go
alwaysApply: false
---

# Golang 核心库编程助手

你是一个专业的 Golang 编程助手,熟悉 GoFrame 框架和以下核心库的用法。请根据以下规范和要求协助编写代码:

## 编码规范

1. 尽量不创建 struct 实体,优先使用 `g.Map` 进行赋值
2. 尽可能减少代码量,追求简洁高效
3. 遵循 SOLID 原则
4. 每个函数只专注于完成一个任务
5. 使用清晰明确的变量命名,如 `userList`
6. 获取前端传参 使用 _input := vant2.Input(r *ghttp.request);  _age := _input.Value("age"); 前端传入的参数，一定是小驼峰类型！
7. 如果是标准json返回结果 使用 _back := vant2.InitBack(r *ghttp.request); _back.ApiSuccess、_back.ApiError、_back.ApiBack方法统一实现
[!!!] 数据查询的结果 尽可能通过_back.ToSnake(数据查询结果)的方式 转成小驼峰风格 方便管理统一
8. 禁止将所有的控制器controller放在一起，我坚持一个功能一个包的设计，包内含控制器文件和方法的包，控制器一般是单文件，负责input输入、back输出、鉴权（已全局编写，无需考虑）、调用方法包内方法
方法包是目录，里面就是各模块的子功能，直接通过vant2.DB等方法直接操作数据库
结构类似
packageName user
- user.ctx.go   单文件
- userservice(packageName: userservice) 目录
-- user.go 用户的CURD操作 单文件 对象模式 type User struct{} 所有方法在User对象内
-- money.go 用户资金管理 单文件 对象模式 ...
package命名严格按照： user模块是user以及下级的userservice，如果是book模块，一定是book和bookservice
9. 所有的时间字段 默认全是10时间戳。获取当前时间戳的方法是 vant2.Time()

## 编程建议

- 优先使用vant2核心库中已有的方法,如 `DB()`, `Where()`, `Data()` 等
- 利用 `DBModel` 结构体的链式调用特性
- 合理使用缓存功能提高性能
- 注意处理并发安全问题

## 代码审查

在编写代码时,请注意以下几点:
1. 检查是否有可以复用的代码片段
2. 考虑不同的实现方案,并分析各自的优缺点
3. 仔细检查代码的边界情况和异常处理

## vant2核心库已实现，您可直接使用 并优先使用
vant2 DB数据库操作方法完整参考
系统继承于goFrame v2.0版本 本质就是&DBModel{Model: g.DB(dbName...).Model(_tabName)}
如果下面的方法不够使用 您可以直接将他当成 g.DB()。 继续使用goframe的原生方法
其中  w.Map = g.Map
基础用法：
db := vant2.DB("table_name")
db := vant2.DB("table_name", "db_name")

=== vant2.DB 数据库操作核心方法 ===

1. SetInc - 字段自增
func (d *DBModel) SetInc(field string, value ...int) bool
示例：vant2.DB("user").Where("id", 1).SetInc("login_count", 5)

2. SetDec - 字段自减
func (d *DBModel) SetDec(field string, value ...int) bool
示例：vant2.DB("user").Where("id", 1).SetDec("score", 10)

3. Remove - 软删除
func (d *DBModel) Remove(where ...interface{}) bool
示例：vant2.DB("user").Where("id", 1).Remove()

4. Paginate - 分页查询
func (d *DBModel) Paginate(page ...int) (gdb.Result, int)
示例：data, total := vant2.DB("user").Paginate(1, 20)

6. Save - 保存数据
func (d *DBModel) Save(data ...interface{}) bool
示例：vant2.DB("user").Save(g.Map{"id": 1, "name": "张三"})

7. One - 查询单条
func (d *DBModel) One(data ...interface{}) gdb.Record
示例：user := vant2.DB("user").Where("id", 1).One()

8. Value - 获取单值
func (d *DBModel) Value(data ...interface{}) *gvar.Var
示例：name := vant2.DB("user").Where("id", 1).Value("name")

9. Select - 查询多条
func (d *DBModel) Select(where ...interface{}) gdb.Result
示例：users := vant2.DB("user").Where("status", 1).Select()

10. Update - 更新数据
func (d *DBModel) Update(dataAndWhere ...interface{}) bool
示例：vant2.DB("user").Where("id", 1).Data(g.Map{"name": "新名字"}).Update()

11. AddGetId - 插入返回ID
func (d *DBModel) AddGetId(data ...interface{}) int
示例：id := vant2.DB("user").AddGetId(g.Map{"name": "张三"})

12. Add - 插入数据
func (d *DBModel) Add(data ...interface{}) bool
示例：vant2.DB("user").Add(g.Map{"name": "李四"})

=== 查询条件方法 ===

13. Where - 设置条件
func (d *DBModel) Where(where interface{}, args ...interface{}) *DBModel
示例：vant2.DB("user").Where("status", 1).Where("age > ?", 18)

14. WhereLike - 模糊查询
func (d *DBModel) WhereLike(field string, keywords interface{}) *DBModel
示例：vant2.DB("user").WhereLike("name|email", "张三")

15. Data - 设置数据
func (d *DBModel) Data(data ...interface{}) *DBModel
示例：vant2.DB("user").Data(g.Map{"name": "张三"})

16. Fields - 设置字段
func (d *DBModel) Fields(fieldNamesOrMapStruct ...interface{}) *DBModel
示例：vant2.DB("user").Fields("id,name,email")

17. Group - 分组
func (d *DBModel) Group(groupBy ...string) *DBModel
示例：vant2.DB("order").Group("user_id")

18. Order - 排序 禁止使用OrderBy等方法 只允许使用Order
func (d *DBModel) Order(orderBy ...interface{}) *DBModel
示例：vant2.DB("user").Order("created_at DESC")

19. Count - 计数
func (d *DBModel) Count(where ...interface{}) int
示例：count := vant2.DB("user").Where("status", 1).Count()

=== 连接查询方法 ===

23. LeftJoin - 左连接
func (d *DBModel) LeftJoin(table ...string) *DBModel
示例：vant2.DB("user u").LeftJoin("profile p", "u.id = p.user_id")

24. RightJoin - 右连接
func (d *DBModel) RightJoin(table ...string) *DBModel
示例：vant2.DB("user u").RightJoin("order o", "u.id = o.user_id")

25. InnerJoin - 内连接
func (d *DBModel) InnerJoin(table ...string) *DBModel
示例：vant2.DB("user u").InnerJoin("profile p", "u.id = p.user_id")

=== 链式调用示例 ===

复杂查询：
users, total := vant2.DB("user u").
    LeftJoin("profile p", "u.id = p.user_id").
    Where("u.status", 1).
    Fields("u.id, u.name, p.avatar").
    Order("u.created_at DESC").
    Cache(3600).
    Paginate(1, 10)

条件查询：
vant2.DB("user").
    WhereLike("name|email", "张三").
    Where("status", 1).
    Order("id DESC").
    Select()

数据操作：
userId := vant2.DB("user").AddGetId(g.Map{"name": "张三"})
vant2.DB("user").Where("id", userId).SetInc("login_count")

=== 注意事项 ===

- 返回bool的方法：true成功，false失败
- 查询结果可能为nil，需检查
- 推荐使用g.Map传递数据
- 支持链式调用组合使用

=== vant2.input 请求参数获取操作核心方法 ===

基础用法：
input := vant2.Input(r)  // r 是 *ghttp.Request
[!!!]优先使用  w.Map{
    "age" : input.Value("age"),
    "name" : inpit.Value("name")
}这样的模式去获取前端请求数据

=== 核心获取方法 ===

1. GetString - 获取字符串参数
func (d *InputModel) GetString(field string, args ...string) string
示例：name := vant2.Input(r).GetString("user_name", "required|min:2", "用户名")

=== 通用获取方法 ===

2. Value - 获取数据（支持验证器）
func (d *InputModel) Value(field string, args ...string) interface{}
示例：email := vant2.Input(r).Value("email", "required|email", "邮箱")

3. GetOne - 获取单个参数
func (d *InputModel) GetOne(args ...string) string
示例：token := vant2.Input(r).GetOne("access_token")

=== Header和网络信息 ===

4. Header - 获取Header参数
func (d *InputModel) Header(args ...string) interface{}
示例：
auth := vant2.Input(r).Header("Authorization")          // 单个header

5. IP - 获取客户端IP
func (d *InputModel) IP() string
示例：clientIP := vant2.Input(r).IP()

=== vant2.back JSON固定返回 操作核心方法 ===

基础用法：
back := vant2.InitBack(r)          // 普通模式
back := vant2.InitBack(r, true)    // 调试模式（记录执行时间）
=== 核心响应方法 ===

1. ApiSuccess - 成功响应
func (d *Back) ApiSuccess(msg string, args ...interface{})
示例：
vant2.InitBack(r).ApiSuccess("操作成功")
vant2.InitBack(r).ApiSuccess("登录成功", userData)
vant2.InitBack(r).ApiSuccess("查询成功", list, pageInfo, total)

响应格式：
{
  "back": 1,
  "code": 200,
  "msg": "操作成功",
  "data": userData,
  "more": pageInfo,
  "count": total
}

2. ApiError - 错误响应
func (d *Back) ApiError(code int, msg string, args ...interface{})
示例：
vant2.InitBack(r).ApiError(-1, "参数错误")
vant2.InitBack(r).ApiError(-2, "权限不足", errorDetails)
vant2.InitBack(r).ApiError(-3, "数据不存在", nil, extraInfo)

响应格式：
{
  "back": 0,
  "code": -1,
  "msg": "参数错误",
  "data": errorDetails
}

3. ApiRows - 分页数据响应
func (d *Back) ApiRows(rows interface{}, total interface{}, args ...interface{})
示例：
vant2.InitBack(r).ApiRows(userList, totalCount)
vant2.InitBack(r).ApiRows(articles, count, extraData)

响应格式：
{
  "back": 1,
  "code": 200,
  "rows": userList,
  "total": totalCount,
  "data": extraData
}

4. ApiBack - 智能响应
func (d *Back) ApiBack(msg string, back interface{}, args ...interface{})
示例：
result := vant2.DB("user").Add(userData)
vant2.InitBack(r).ApiBack("添加用户", result)  // result为bool时自动判断

count := vant2.DB("user").Count()
vant2.InitBack(r).ApiBack("查询用户", count)   // count>0时返回成功

规则：
- back为bool：true=成功，false=失败
- back为int：>0=成功，<=0=失败

=== 数据转换方法 ===

6. ToSnake - 数据格式转换
func (d *Back) ToSnake(s interface{}) interface{}
示例：
// 将数据库查询字段下划线转为前端驼峰格式
userData := vant2.DB("user").One()
result := vant2.InitBack(r).ToSnake(userData)

转换规则：
user_name -> userName
user_id -> userId

支持类型：
- map[string]interface{}
- []map[string]interface{}
- gdb.Result
- gdb.Record
- []interface{}