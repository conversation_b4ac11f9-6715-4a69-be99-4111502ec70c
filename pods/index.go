package pods

import (
	"assistant/pods/accounting"
	"assistant/pods/chat"
	"assistant/pods/login"
	"assistant/pods/memory"
	"assistant/pods/podsdemo"
	"assistant/pods/task"
	"assistant/pods/user"
	ragchat "assistant/rag/chat"
	"assistant/rag/embedding"
	"assistant/rag/openai/openaidemo"
	"assistant/rag/openai/openaiplus"
	"assistant/rag/pgvector/pgvectordemo"

	"github.com/gogf/gf/v2/net/ghttp"
)

// 注册路由
func RegisterRoutes(s *ghttp.Server) {

	// 设置静态资源目录
	s.SetIndexFolder(true)
	s.AddSearchPath("./static")
	s.AddStaticPath("/static", "./static")

	// 允许跨域访问
	s.BindMiddlewareDefault(ghttp.MiddlewareCORS)

	s.SetNameToUriType(3) // 设置路由名称

	// AI对话&&api
	s.BindObject("/v1/chat", &chat.ChatAPICtx{})
	// AI对话&&user
	s.BindObject("/chat", chat.ChatCtx)
	// 登录 && 注册
	s.BindObject("/login", login.LoginCtx)
	s.BindObject("/reg", login.RegCtx)

	// 用户中心
	s.BindObject("/user", user.UserCtx)

	// 记账模块
	accounting.InitRouter(s)

	// 记忆碎片模块
	s.Group("/memory", func(group *ghttp.RouterGroup) {
		// 分类管理
		group.POST("/category/save", memory.CategorySave)
		group.POST("/category/delete", memory.CategoryDelete)
		group.GET("/category/list", memory.CategoryList)
		group.GET("/category/get", memory.CategoryGet)

		// 标签管理
		group.POST("/tag/save", memory.TagSave)
		group.POST("/tag/delete", memory.TagDelete)
		group.GET("/tag/list", memory.TagList)
		group.GET("/tag/search", memory.TagSearch)

		// 数据管理
		group.POST("/data/save", memory.DataSave)
		group.POST("/data/delete", memory.DataDelete)
		group.GET("/data/get", memory.DataGet)
		group.GET("/data/list", memory.DataList)

		// 统计信息
		group.GET("/statistics", memory.Statistics)
	})

	// 待办记事模块
	task.InitRouter(s)

	// 简单的识别
	s.BindObject("/asr", podsdemo.AsrCtx)

	// // 文档解析服务接口
	// docparseCtrl := docparse.NewController()
	// s.BindHandler("/docparse", docparseCtrl.ParseIndex)           // 文档解析首页
	// s.BindHandler("/docparse/upload", docparseCtrl.ParseUpload)   // 文件上传解析接口
	// s.BindHandler("/docparse/formats", docparseCtrl.ParseFormats) // 获取支持格式接口
	// s.BindHandler("/docparse/check", docparseCtrl.ParseCheck)     // 格式检查接口
	s.BindHandler("/assistant/chat", ragchat.Chat)
	// 注册所有测试演示路由
	// registerDemoRoutes(s)
	// registerLLMTestRoutes(s)
	// registerEmbeddingDemoRoutes(s)
	// registerPGVectorRoutes(s)

}

// registerDemoRoutes 注册Demo演示相关路由
func registerDemoRoutes(s *ghttp.Server) {
	// Demo终极对话演示接口
	s.BindHandler("/assistant/chat", ragchat.Chat)

	// Web搜索演示接口
	searchCtrl := &openaiplus.Controller{}
	s.BindHandler("/search", searchCtrl.SearchIndex)     // 搜索首页
	s.BindHandler("/search/demo", searchCtrl.SearchDemo) // 搜索API演示
}

// registerLLMTestRoutes 注册LLM大模型测试相关路由
func registerLLMTestRoutes(s *ghttp.Server) {
	// LLM大模型对话测试接口
	s.BindHandler("/test", openaidemo.TestIndex)                                        // 测试首页
	s.BindHandler("/test/basic-chat", openaidemo.TestBasicChat)                         // 基础对话测试
	s.BindHandler("/test/system-message", openaidemo.TestSystemMessage)                 // 系统消息测试
	s.BindHandler("/test/context-chat", openaidemo.TestContextChat)                     // 上下文对话测试
	s.BindHandler("/test/chained-call", openaidemo.TestChainedCall)                     // 链式调用测试
	s.BindHandler("/test/stream-chat", openaidemo.TestStreamChat)                       // 流式对话测试
	s.BindHandler("/test/tools-call", openaidemo.TestToolsCall)                         // 工具调用测试
	s.BindHandler("/test/sse-tools-detection", openaidemo.TestSSEToolsDetection)        // SSE工具检测测试
	s.BindHandler("/test/real-tools-workflow", openaidemo.TestRealToolsWorkflow)        // 真实工具调用流程(SSE)
	s.BindHandler("/test/real-tools-workflow-api", openaidemo.TestRealToolsWorkflowAPI) // 真实工具调用流程(API)
	s.BindHandler("/test/advanced-config", openaidemo.TestAdvancedConfig)               // 高级配置测试
	s.BindHandler("/test/clone-reset", openaidemo.TestCloneAndReset)                    // 克隆重置测试
	s.BindHandler("/test/custom-config", openaidemo.TestCustomConfig)                   // 自定义配置测试
	s.BindHandler("/test/format-json", openaidemo.TestFormatJson)                       // FormatJson测试
	s.BindHandler("/test/performance-analysis", openaidemo.TestPerformanceAnalysis)     // 性能分析测试
	s.BindHandler("/test/smart-chat-context", openaidemo.TestSmartChatContext)          // 智能对话上下文演示(SSE)
	s.BindHandler("/test/smart-chat-context-api", openaidemo.TestSmartChatContextAPI)   // 智能对话上下文演示(API)
	s.BindHandler("/test/multimodal-api", openaidemo.TestMultimodalAPI)                 // 多模态对话API测试
	s.BindHandler("/test/multimodal-sse", openaidemo.TestMultimodalSSE)                 // 多模态对话SSE测试
}

// registerEmbeddingDemoRoutes 注册Embedding演示相关路由
func registerEmbeddingDemoRoutes(s *ghttp.Server) {
	// Embedding演示接口
	demoCtrl := &embedding.DemoController{}
	s.BindHandler("/demo", demoCtrl.DemoIndex)
	s.BindHandler("/demo/embedding", demoCtrl.EmbeddingDemo)
	s.BindHandler("/demo/rerank", demoCtrl.RerankDemo)
	s.BindHandler("/demo/advanced-rerank", demoCtrl.AdvancedRerankDemo)
	s.BindHandler("/demo/combined", demoCtrl.CombinedDemo)
}

// registerPGVectorRoutes 注册PGVector相关路由
func registerPGVectorRoutes(s *ghttp.Server) {
	// 文档向量库控制器
	pgvectorCtrl := &pgvectordemo.Controller{}

	// PGVector测试主页
	s.BindHandler("/pgvector", pgvectorCtrl.TestIndex)

	// 文档向量库CRUD接口
	s.BindHandler("/pgvector/crud/init", pgvectorCtrl.CrudInit)
	s.BindHandler("/pgvector/crud/clean", pgvectorCtrl.CrudClean)
	s.BindHandler("/pgvector/crud/create", pgvectorCtrl.CrudCreate)
	s.BindHandler("/pgvector/crud/create-split", pgvectorCtrl.CrudCreateSplit)
	s.BindHandler("/pgvector/crud/query", pgvectorCtrl.CrudQuery)
	s.BindHandler("/pgvector/crud/search", pgvectorCtrl.CrudSearch)
	s.BindHandler("/pgvector/crud/search-manual-embedding", pgvectorCtrl.CrudSearchManualEmbedding)
	s.BindHandler("/pgvector/crud/rerank", pgvectorCtrl.CrudRerank)
	s.BindHandler("/pgvector/crud/query-content", pgvectorCtrl.CrudQueryContent)
	s.BindHandler("/pgvector/crud/search-split-docs", pgvectorCtrl.CrudSearchSplitDocs)
	s.BindHandler("/pgvector/crud/search-split-segments", pgvectorCtrl.CrudSearchSplitSegments)
	s.BindHandler("/pgvector/crud/update", pgvectorCtrl.CrudUpdate)
	s.BindHandler("/pgvector/crud/update-split", pgvectorCtrl.CrudUpdateSplit)
	s.BindHandler("/pgvector/crud/delete", pgvectorCtrl.CrudDelete)
	s.BindHandler("/pgvector/crud/debug-tables", pgvectorCtrl.CrudDebugTables)

	// QA问答库控制器
	qaCtrl := &pgvectordemo.QAController{}

	// QA问答库接口
	s.BindHandler("/pgvector/qa/clean", qaCtrl.QACrudClean)
	s.BindHandler("/pgvector/qa/create", qaCtrl.QACrudCreate)
	s.BindHandler("/pgvector/qa/create-batch", qaCtrl.QACrudCreateBatch)
	s.BindHandler("/pgvector/qa/query", qaCtrl.QACrudQuery)
	s.BindHandler("/pgvector/qa/search", qaCtrl.QACrudSearch)
	s.BindHandler("/pgvector/qa/rerank", qaCtrl.QACrudRerank)
	s.BindHandler("/pgvector/qa/update", qaCtrl.QACrudUpdate)
	s.BindHandler("/pgvector/qa/delete", qaCtrl.QACrudDelete)

	// 聊天历史控制器
	chatCtrl := &pgvectordemo.ChatController{}

	// 聊天历史接口
	s.BindHandler("/pgvector/chat", chatCtrl.TestIndex)
	s.BindHandler("/pgvector/chat/clean", chatCtrl.ChatClean)
	s.BindHandler("/pgvector/chat/init", chatCtrl.ChatInit)
	s.BindHandler("/pgvector/chat/debug", chatCtrl.ChatDebug)
	s.BindHandler("/pgvector/chat/list-basic", chatCtrl.ChatListBasic)
	s.BindHandler("/pgvector/chat/list-where", chatCtrl.ChatListWhere)
	s.BindHandler("/pgvector/chat/conversations", chatCtrl.ChatConversations)
	s.BindHandler("/pgvector/chat/query-simple", chatCtrl.ChatQuerySimple)
	s.BindHandler("/pgvector/chat/query-embedding", chatCtrl.ChatQueryEmbedding)
	s.BindHandler("/pgvector/chat/query-distance", chatCtrl.ChatQueryDistance)
	s.BindHandler("/pgvector/chat/rerank", chatCtrl.ChatRerank)
	s.BindHandler("/pgvector/chat/fullchat", chatCtrl.ChatFullChat)
	s.BindHandler("/pgvector/chat/message-list", chatCtrl.ChatMessageList)
	s.BindHandler("/pgvector/chat/complex-demo", chatCtrl.ChatComplexDemo)
	s.BindHandler("/pgvector/chat/save-user", chatCtrl.ChatSaveUser)
	s.BindHandler("/pgvector/chat/save-assistant", chatCtrl.ChatSaveAssistant)
	s.BindHandler("/pgvector/chat/update-message", chatCtrl.ChatUpdateMessage)
	s.BindHandler("/pgvector/chat/delete-message", chatCtrl.ChatDeleteMessage)
}
