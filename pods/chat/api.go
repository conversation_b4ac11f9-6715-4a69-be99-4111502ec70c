package chat

import (
	chats "assistant/pods/chat/service"
	"fmt"
	"strings"
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

type ChatAPICtx struct {
}

func (c *ChatAPICtx) Completions(r *ghttp.Request) {
	chatInit := _initAPIChat(r)
	if chatInit.ErrorMsg != "" {
		SseError(r, 500, chatInit.ErrorMsg)
		return
	}

	// API版本直接使用传入的消息，无需从数据库获取历史消息
	vant2.Primary("🚀 -> API -> message:", chatInit.Messages, chatInit.LastContent, chatInit.LastRule)

	_content, _tokenTotal := "", &TokenTotal{}
	_dataMap := w.Map{
		"model":       chatInit.ChatModelConfig.Model,
		"stream":      chatInit.Stream,
		"messages":    chatInit.Messages,
		"temperature": chatInit.Temperature,
	}

	if chatInit.MaxToken > 0 {
		_dataMap["max_tokens"] = chatInit.MaxToken
	}

	// 火山云的应用 需要返回usage
	if strings.HasPrefix(chatInit.ChatModelConfig.Model, "bot-") && chatInit.Stream {
		_dataMap["stream_options"] = w.Map{"include_usage": true}
	}

	// 调用AI大模型API接口
	vant2.Warning("🚀 发起大模型调用 -> API -> _dataMap:", _dataMap, chatInit.ChatModelConfig.ApiUrl, chatInit.ChatModelConfig.ApiKey)

	var _b interface{}
	var _fc func(d w.Map)
	if chatInit.Stream {
		_fc = HandleSSEResponse(r, chatInit.ChannelId, &_content, _tokenTotal)
	}
	_b = vant2.Ajax(&w.AjaxArgs{
		Url:           chatInit.ChatModelConfig.ApiUrl,
		Authorization: chatInit.ChatModelConfig.ApiKey,
		Method:        "post",
		Data:          _dataMap,
	}, _fc)
	vant2.Error("error", _b, chatInit, _content, _tokenTotal, chatInit.ChatModelConfig)
	// API版本不保存消息到数据库
	if !chatInit.Stream {
		_content = vant2.LodashGetString(_b, "choices.0.message.content")
		vant2.Primary("🚀 -> _content:", _content)
		// 非流式返回时，设置适当的响应头
		r.Response.Header().Set("Content-Type", "application/json")
		// 直接输出_b
		r.Response.Write(vant2.JsonEncoder(_b))
		r.Response.Flush()
		return
	}

	return
}

// 原有的示例方法保留
func (c *ChatAPICtx) CompletionsExample(r *ghttp.Request) {
	// 设置SSE相关的响应头
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")
	r.Response.Header().Set("X-Accel-Buffering", "no")

	// 立即发送初始信息确保连接已建立
	r.Response.Write("retry: 1000\n\n")
	r.Response.Flush()

	// 循环30次，每次休眠1秒
	for i := 1; i <= 5; i++ {
		// 发送数据
		r.Response.Write(fmt.Sprintf("data: %d\n\n", i))
		// 立即刷新缓冲区确保数据被发送
		r.Response.Flush()
		// 输出日志
		fmt.Printf("已发送事件: %d\n", i)
		// 休眠1秒
		time.Sleep(time.Second)
	}

	r.Response.Write("[DONE]")
	r.Response.Flush()
}

// ErrorExample 示例方法，展示如何使用SseError函数
func (c *ChatAPICtx) ErrorExample(r *ghttp.Request) {
	// 设置SSE相关的响应头
	SetSSEHeaders(r)

	// 假设检查用户权限
	hasPermission := false
	if !hasPermission {
		// 无权限访问，返回错误
		SseError(r, 403, "您无权访问此资源")
		return
	}

	// 假设处理API限流
	isRateLimited := true
	if isRateLimited {
		// 返回限流错误，并附加额外信息
		limitInfo := g.Map{
			"retry_after":   60,
			"limit_type":    "hourly_rate",
			"current_usage": 100,
			"max_usage":     100,
		}
		SseErrorWithData(r, 429, "请求频率超限，请稍后再试", limitInfo)
		return
	}

	// 正常的SSE处理逻辑...
}

// API版的初始化聊天，使用API密钥鉴权
func _initAPIChat(r *ghttp.Request) *ChatInit {
	d := &ChatInit{}

	apiKey := r.Header.Get("Authorization")
	d.UserId, d.ErrorMsg = CheckApiKey(apiKey)
	if d.UserId == 0 {
		return d
	}

	messages := gconv.SliceMap(r.Get("messages"))
	d.Temperature = gconv.Float64(r.Get("temperature"))
	d.MaxToken = gconv.Int(r.Get("maxToken"))
	d.OpenContext = gconv.Int(r.Get("openContext"))
	d.Stream = gconv.Bool(r.Get("stream"))

	// 获取请求参数
	model := gconv.String(r.Get("model"))
	if model == "" {
		model = "qwen2.5-7b"
	}

	d.ChatModelConfig = chats.GetModelConfig(model, 0) // 使用默认用户
	if d.ChatModelConfig == nil {
		d.ErrorMsg = "模型不存在"
		return d
	}
	if d.ChatModelConfig.Model == "" {
		d.ErrorMsg = "模型不存在"
		return d
	}

	// 验证 message 是否为数组形式
	if len(messages) == 0 {
		d.ErrorMsg = "message 不能为空"
		return d
	}

	// 取出最后一条消息并验证内容
	lastMsg := gconv.Map(messages[len(messages)-1])
	if len(lastMsg) == 0 {
		d.ErrorMsg = "message 格式不正确"
		return d
	}

	_lastContent := vant2.LodashGetString(lastMsg, "content")
	_lastRule := vant2.LodashGetString(lastMsg, "role")
	if _lastContent == "" {
		d.ErrorMsg = "消息内容不能为空"
		return d
	}
	if _lastRule != "user" {
		d.ErrorMsg = "消息类型错误"
		return d
	}

	d.Model = model
	d.Messages = messages
	d.LastContent = _lastContent
	d.LastRule = _lastRule

	// API版无需查询渠道ID，直接生成一个临时ID
	d.ChannelId = vant2.Time() // 使用当前时间戳作为临时渠道ID

	// 只有在流式响应时才设置SSE响应头
	if d.Stream {
		SetSSEHeaders(r)
	}

	return d
}
