package chats

import (
	"strings"
	"vant2"
	"vant2/tool/w"
)

type ModelConfig struct {
	Name    string      `json:"name"`
	Models  string      `json:"models"`
	Model   string      `json:"model"`
	ApiUrl  string      `json:"api_url"`
	ApiKey  string      `json:"api_key"`
	Mapping w.MapStrStr `json:"mapping"`
}

var Models = []ModelConfig{
	// 火宝
	{
		Name:   "chatfire",
		Models: "gpt-4o-mini,gpt-4o,gpt-4o-mini,gpt-4.1,claude-3-7-sonnet,claude-3-5-sonnet",
		ApiUrl: "https://api.chatfire.cn/v1/chat/completions",
		ApiKey: "sk-7P96Z4V6BadXGOWT7bC42a7d4aD84c9d8cEc9bF917B0Da5a",
		Mapping: w.MapStrStr{
			"gpt-4o-mini":       "gpt-4o-mini",
			"gpt-4o":            "gpt-4o",
			"gpt-4.1":           "gpt-4.1-nano", // gpt-4.1
			"claude-3-7-sonnet": "claude-3-7-sonnet-latest",
			"claude-3-5-sonnet": "claude-3-5-sonnet-latest",
		},
	},
	// 火山云
	{
		Name:   "volcengine",
		Models: "deepseek-r1,deepseek-v3,deepseek",
		ApiUrl: "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
		ApiKey: "fbdb1302-95f7-4be3-9124-249961f6de6a",
		Mapping: w.MapStrStr{
			"deepseek":    "ep-20250205163442-c9gv8",
			"deepseek-v3": "ep-20250205163442-c9gv8",
			"deepseek-r1": "ep-20250205164826-d9tmm",
		},
	},
	// 火山云-16k
	{
		Name:   "volcengine-bot",
		Models: "deepseek-search",
		ApiUrl: "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions",
		ApiKey: "fbdb1302-95f7-4be3-9124-249961f6de6a",
		Mapping: w.MapStrStr{
			"deepseek-search": "bot-20250217112501-pffww",
		},
	},
	// 超高速
	{
		Name:   "deepseek-16k",
		Models: "deepseek-r1-16k,deepseek-v3-16k,qwen-2.5-72b-16k",
		ApiUrl: "https://api.sambanova.ai/v1/chat/completions",
		ApiKey: "2ad01400-e7cf-4d5d-b942-c8b7d8ab8a08",
		Mapping: w.MapStrStr{
			"deepseek-r1-16k":  "DeepSeek-R1",
			"qwen-2.5-72b-16k": "Qwen2.5-72B-Instruct",
			"deepseek-v3-16k":  "DeepSeek-V3-0324",
		},
	},
	// 硅动
	{
		Name:   "siliconflow",
		Models: "qwen2.5-32b,qwen2.5-7b",
		ApiUrl: "https://api.siliconflow.cn/v1/chat/completions",
		ApiKey: "sk-oyiulmbliprkttsprrbycqcphtuboanwenftjohtfzissrrr",
		Mapping: w.MapStrStr{
			"qwen2.5-32b": "Qwen/Qwen2.5-32B-Instruct",
			"qwen2.5-7b":  "Qwen/Qwen2.5-7B-Instruct",
		},
	},
}

// 传入比如 qwen2.5-32b 返回对应的模型配置 和Model名称
func GetModelConfig(model string, userId int64) *ModelConfig {
	vant2.Primary("🚀 -> func -> model2:", model)
	for _, v := range Models {
		_list := strings.Split(v.Models, ",")
		for _, _model := range _list {
			if _model == model {
				_model := &ModelConfig{}
				_model.Model = v.Mapping[model]
				_model.Name = v.Name
				_model.ApiUrl = v.ApiUrl
				_model.ApiKey = v.ApiKey
				_model.Mapping = v.Mapping
				vant2.Primary("🚀 -> func -> GetModelConfig:", v, _model)

				// 判断用户
				return _model
			}
		}
	}
	return nil
}
