package chats

import (
	"slices"
	"time"
	"vant2"
	vrag "vant2/tool/chat/rag"
	"vant2/tool/w"
)

// 实现无限上下文
func GetMessageList(messages w.SliceMap, where w.Map, openContext int) (w.SliceMap, interface{}) {
	_msg, _ids := messages, []int{}
	//
	// 在最前插入 当前系统时间
	_msg = slices.Insert(
		_msg,
		0,
		w.Map{
			"role":    "system",
			"content": "当前系统时间： " + time.Now().Format("2006-01-02 15:04:05"),
		},
	)
	// 插入系统system
	_msg = slices.Insert(
		_msg,
		0,
		w.Map{
			"role":    "system",
			"content": vant2.GetLocalFile("manifest/prompt/system/role.md"),
		},
	)

	if openContext != 1 {
		return _msg, nil
	}

	for _, v := range messages {
		_id := vant2.LodashGetInt(v, "id")
		if _id > 0 {
			_ids = append(_ids, _id)
		}
	}

	_content := vant2.LodashGetString(where, "query")
	if _content == "" {
		return messages, nil
	}

	_, _vetctor := vrag.ApiEmbeddingString(_content)
	// 获取所有id
	_db := vant2.DB("chat_history", "vsearch")
	_fields := "id,content,role,"
	_fields += _db.VectorFields(&w.VectorAttr{
		FieldName: "vector",
		Embedding: _vetctor,
	})
	_res, _ := _db.Where("id not in (?)", _ids).
		Where(w.Map{
			"channel_id": where["channel_id"],
			"user_id":    where["user_id"],
		}).
		Fields(_fields).
		Order("distance asc,id desc").
		Limit(4).
		All()
	// 检索相关的数据
	if _res == nil {
		return _msg, nil
	}

	// 在_msg列表中 先找到 role == "system"的最大序号 然后在这个序号后面插入查询的内容
	_maxId := 0
	for _index, v := range _msg {
		if vant2.LodashGetString(v, "role") == "system" {
			_maxId = _index
		}
	}
	// 拼接长期记忆role
	_roleContent := "## 记忆增强(从聊天历史中召回相似内容) \n ### 召回记忆数据：\n\n"
	for _, v := range _res {
		_roleContent += vant2.LodashGetString(v, "role") + ":" + vant2.LodashGetString(v, "content") + "\n"
	}
	_msgRole := w.Map{"role": "system", "content": _roleContent}
	_msg = slices.Insert(_msg, _maxId+1, _msgRole)

	return _msg, _vetctor
}
