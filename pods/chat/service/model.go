package chats

type ChatSaveModel struct {
	Model           string      `json:"model"`            // 模型
	Role            string      `json:"role"`             // 角色
	Content         string      `json:"last_content"`     // 最后一条消息
	UserId          int64       `json:"user_id"`          // 用户id
	GroupId         int64       `json:"group_id"`         // 群组id
	TokenPrompt     int64       `json:"token_prompt"`     // 传入的token
	TokenCompletion int64       `json:"token_completion"` // 消耗的token
	TokenTotal      int64       `json:"token_total"`      // 合计Token
	TimeCreate      int64       `json:"time_create"`      // 创建时间
	ContextMsg      string      `json:"context_msg"`      // 上下文
	ChannelId       int64       `json:"channel_id"`       // 渠道ID
	Vector          interface{} `json:"vector"`           // 向量

}
