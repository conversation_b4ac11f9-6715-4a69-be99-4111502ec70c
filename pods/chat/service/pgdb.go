package chats

import (
	"vant2"
	vrag "vant2/tool/chat/rag"
	"vant2/tool/w"
)

// 保存对话历史到数据库

// 执行保存Rag的操作
func Save(args *ChatSaveModel) bool {
	_res := false
	// 先保存用户对话
	_pgTable := &vrag.PGTable{
		TableName: "pgvector_chat_history",
		DBName:    "vsearch",
		Embedding: args.Vector,
		// Where:     model.Where,
		DataMap: w.Map{
			"user_id":         args.UserId,
			"channel_id":      args.ChannelId,
			"conversation_id": args.TimeCreate, // 使用时间戳作为对话组ID
			"role":            args.Role,
			"content":         args.Content,
			"time_create":     args.TimeCreate,
			"tokens":          args.TokenTotal,
			"extra": w.Map{
				"model":            args.Model,
				"token_prompt":     args.TokenPrompt,
				"token_completion": args.TokenCompletion,
				"context_msg":      args.ContextMsg,
			},
		},
	}
	// 插入数据
	_res = _pgTable.Insert()

	if args.Role == "assistant" {
		// 更新channel_id
		vant2.DB("chat_channel").Where(w.Map{
			"id": args.ChannelId,
		}).Data(w.Map{
			"time_last": args.TimeCreate,
		}).Update()
	}

	return _res
}

// 查看对话历史

// 召回10条最新的对话记录

// 召回10条相似性最高的对话记录
