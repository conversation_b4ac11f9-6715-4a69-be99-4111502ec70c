package chat

import (
	"assistant/pods/base"
	chats "assistant/pods/chat/service"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/net/ghttp"
)

var ChatCtx = func() *_chat {
	return &_chat{}
}()

type _chat struct {
	*base.BaseCtx
}

// 实现SSE流式大模型对话
func (d *_chat) Completions(r *ghttp.Request) {
	chatInit := _initSSEChat(r, true)
	if chatInit.ErrorMsg != "" {
		SseError(r, 500, chatInit.ErrorMsg)
		return
	}

	// 无限上下文（向量库实现模式）
	var _emb2 interface{}
	chatInit.Messages, _emb2 = chats.GetMessageList(chatInit.Messages, w.Map{
		"channel_id": chatInit.ChannelId,
		"user_id":    chatInit.UserId,
		"query":      chatInit.LastContent,
	}, chatInit.OpenContext)
	vant2.Primary("🚀 -> func -> message:", chatInit.Messages, chatInit.LastContent, chatInit.LastRule)

	// 保存用户消息
	go d.saveChatMessage(chatInit, "user", chatInit.LastContent, 0, 0, 0, _emb2, "")

	_content, _tokenTotal := "", &TokenTotal{}

	// 使用独立方法处理SSE响应
	_fc := HandleSSEResponse(r, chatInit.ChannelId, &_content, _tokenTotal)

	_dataMap := w.Map{
		"model":       chatInit.ChatModelConfig.Model,
		"stream":      true,
		"messages":    chatInit.Messages,
		"temperature": chatInit.Temperature,
	}
	if chatInit.MaxToken > 0 {
		_dataMap["max_tokens"] = chatInit.MaxToken
	}

	// 火山云的应用 需要返回usage
	if strings.HasPrefix(chatInit.ChatModelConfig.Model, "bot-") {
		_dataMap["stream_options"] = w.Map{"include_usage": true}
	}
	// 调用AI大模型API接口
	vant2.Warning("🚀 发起大模型调用 -> func -> _dataMap:", _dataMap, chatInit.ChatModelConfig.ApiUrl, chatInit.ChatModelConfig.ApiKey)
	_b := vant2.Ajax(&w.AjaxArgs{
		Url:           chatInit.ChatModelConfig.ApiUrl,
		Authorization: chatInit.ChatModelConfig.ApiKey,
		Method:        "post",
		Data:          _dataMap,
	}, _fc)

	vant2.Error("error", _b, chatInit, _content, _tokenTotal, chatInit.ChatModelConfig)

	// 保存AI回复
	go d.saveChatMessage(chatInit, "assistant", _content, _tokenTotal.Prompt, _tokenTotal.Completion, _tokenTotal.Total, nil, vant2.JsonEncoder(chatInit.Messages))

	return
}

// saveChatMessage 保存聊天消息，整合了用户和AI消息的保存逻辑
func (d *_chat) saveChatMessage(chatInit *ChatInit, role, content string, tokenPrompt, tokenCompletion, tokenTotal int64, vector interface{}, contextMsg string) {
	model := &chats.ChatSaveModel{
		Model:           chatInit.Model,
		Content:         content,
		Role:            role,
		UserId:          chatInit.UserId,
		GroupId:         chatInit.GroupId,
		ChannelId:       chatInit.ChannelId,
		TimeCreate:      vant2.Time(),
		TokenPrompt:     tokenPrompt,
		TokenCompletion: tokenCompletion,
		TokenTotal:      tokenTotal,
		Vector:          vector,
	}

	if contextMsg != "" {
		model.ContextMsg = contextMsg
	}

	chats.Save(model)
}
