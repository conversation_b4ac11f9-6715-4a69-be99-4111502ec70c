package chat

import (
	"assistant/plus/qcloud"
	"assistant/pods/base"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// 支持的音频格式
var supportedAudioFormats = map[string]bool{
	".mp3":  true,
	".wav":  true,
	".m4a":  true,
	".aac":  true,
	".amr":  true,
	".pcm":  true,
	".webm": true, // 支持H5录音的webm格式
	".mp4":  true, // 支持H5录音的mp4格式
}

// 最大文件大小 (3MB)
const maxAudioFileSize = 3 * 1024 * 1024

// 对前端传入的音频文件进行语音识别
func (d *_chat) Stt(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取上传的音频文件
	uploadFile := r.GetUploadFile("audioFile")
	if uploadFile == nil {
		_base.Back.ApiError(-1, "未检测到上传的音频文件，请选择音频文件后重试")
		return
	}

	vant2.Warning(g.Map{
		"filename": uploadFile.Filename,
		"size":     uploadFile.Size,
		"header":   uploadFile.Header,
	}, "=== 音频文件上传信息 ===")

	// 检查文件大小
	if uploadFile.Size > maxAudioFileSize {
		_base.Back.ApiError(-1, fmt.Sprintf("音频文件大小超过限制，最大支持%.1fMB，当前文件大小: %.2fMB",
			float64(maxAudioFileSize)/(1024*1024), float64(uploadFile.Size)/(1024*1024)))
		return
	}

	// 检查文件名
	if uploadFile.Filename == "" {
		_base.Back.ApiError(-1, "音频文件名为空")
		return
	}

	// 检查音频格式
	ext := strings.ToLower(filepath.Ext(uploadFile.Filename))
	if !supportedAudioFormats[ext] {
		_base.Back.ApiError(-1, fmt.Sprintf("不支持的音频格式: %s，支持格式: mp3, wav, m4a, aac, amr, pcm", ext))
		return
	}

	// 创建临时文件保存上传的音频
	tempDir := "./temp/audio"
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		vant2.Error("创建临时目录失败:", err)
		_base.Back.ApiError(-1, "服务器内部错误，请稍后重试")
		return
	}

	// 生成唯一的临时文件名
	timestamp := time.Now().UnixNano()
	tempFileName := fmt.Sprintf("audio_%d_%d%s", _base.UserId, timestamp, ext)
	tempFilePath := filepath.Join(tempDir, tempFileName)

	// 读取上传文件内容
	fileContent, err := uploadFile.Open()
	if err != nil {
		vant2.Error("打开上传文件失败:", err)
		_base.Back.ApiError(-1, "读取音频文件失败，请重新上传")
		return
	}
	defer fileContent.Close()

	// 创建临时文件
	tempFile, err := os.Create(tempFilePath)
	if err != nil {
		vant2.Error("创建临时文件失败:", err)
		_base.Back.ApiError(-1, "服务器内部错误，请稍后重试")
		return
	}
	defer tempFile.Close()

	// 将上传文件内容复制到临时文件
	_, err = io.Copy(tempFile, fileContent)
	if err != nil {
		vant2.Error("保存临时文件失败:", err)
		os.Remove(tempFilePath) // 清理临时文件
		_base.Back.ApiError(-1, "保存音频文件失败，请重新上传")
		return
	}

	// 确保文件写入完成
	tempFile.Sync()

	// 延迟清理临时文件
	defer func() {
		if err := os.Remove(tempFilePath); err != nil {
			vant2.Warning("清理临时文件失败:", tempFilePath, err)
		}
	}()

	// 使用ASR服务进行语音识别
	asr := qcloud.NewASR()
	recognitionResult, err := asr.RecognizeFromFile(tempFilePath)
	if err != nil {
		vant2.Error("语音识别失败:", err)
		_base.Back.ApiError(-1, fmt.Sprintf("语音识别失败: %v", err))
		return
	}

	// 检查识别结果
	if recognitionResult == "" {
		_base.Back.ApiError(-1, "语音识别结果为空，请确保音频内容清晰且包含语音")
		return
	}

	// 返回识别结果
	responseData := _base.Back.ToSnake(g.Map{
		"text":          recognitionResult,
		"fileName":      uploadFile.Filename,
		"fileSize":      uploadFile.Size,
		"audioFormat":   strings.TrimPrefix(ext, "."),
		"recognizeTime": vant2.Time(),
	})

	_base.Back.ApiSuccess("语音识别成功", responseData)
}
