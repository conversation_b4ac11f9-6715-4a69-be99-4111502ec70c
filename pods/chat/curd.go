package chat

import (
	"assistant/pods/base"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/net/ghttp"
)

// 获取对话列表
func (d *_chat) ChannelList(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	//
	_db := vant2.DB("chat_channel").Where(w.Map{
		"user_id":   _c.UserId,
		"is_delete": 0,
	}).Order("is_commond desc, time_last desc, time_create desc")

	_rows, _count := _db.Paginate(1, 20)

	// 如果查询结果为0，自动创建默认对话分组
	if _count == 0 {
		_time := vant2.Time()
		_defaultChannel := vant2.DB("chat_channel")
		_defaultChannel.Data(w.Map{
			"name":        "默认对话",
			"user_id":     _c.UserId,
			"is_commond":  1, // 设为常用分组
			"time_create": _time,
			"time_last":   _time,
		})
		_res := _defaultChannel.Add()

		// 如果创建成功，重新查询返回
		if _res {
			_rows, _count = _db.Paginate(1, 20)
		}
	}

	_c.Back.ApiRows(_rows, _count)
}

// 获取历史对话
func (d *_chat) History(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	//
	_db := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"user_id":    _c.UserId,
		"channel_id": _c.Input.Value("channel_id", "required", "分类ID"),
		"is_delete":  0,
	}).Order("time_create desc,id desc").
		Fields("id,content,role,tokens,time_create,extra")
	_rows, _count := _db.Paginate(_c.PageNum, 20)
	_c.Back.ApiRows(_rows, _count)
}

// 删除分组
func (d *_chat) ChannelDelete(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	if _c.Id == 0 {
		_c.Back.ApiError(0, "参数有误")

	}
	//
	_db := vant2.DB("chat_channel").Where(w.Map{
		"user_id":   _c.UserId,
		"id":        _c.Id,
		"is_delete": 0,
	})
	_res, _ := _db.Delete()
	_rows, _ := _res.RowsAffected()
	if _rows > 0 {
		_where2 := w.Map{
			"channel_id": _c.Id,
		}
		vant2.DB("pgvector_chat_history", "vsearch").Where(_where2).Delete() // 全部删除！
		_c.Back.ApiSuccess("删除成功")
	} else {
		_c.Back.ApiError(0, "删除失败")
	}
}

// 修改分组名称
func (d *_chat) ChangeGroupName(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	//
	_db := vant2.DB("chat_group").Where(w.Map{
		"user_id":   _c.UserId,
		"id":        d.Id,
		"is_delete": 0,
	})
	_db.Update(w.Map{
		"name":        _c.Input.Value("name", "required", "分组名称"),
		"time_update": vant2.Time(),
	})
	_c.Back.ApiSuccess("修改成功")
}

// 将ai的设置 保存到数据表
func (d *_chat) ChannelConfig(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	if _c.Id == 0 {
		_c.Back.ApiError(0, "参数有误")
	}
	//
	_db := vant2.DB("chat_channel").Where(w.Map{
		"user_id":   _c.UserId,
		"id":        _c.Id,
		"is_delete": 0,
	})

	_db = _db.Data(w.Map{
		"config": _c.Input.Value("config", "required", "分组名称"),
	})
	_c.Back.ApiBack("保存", _db.Update())
}

// 创建新组
func (d *_chat) ChannelSave(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	_time := vant2.Time()
	//
	if _c.Id == 0 {
		_db := vant2.DB("chat_channel")

		_db.Data(w.Map{
			"name":        _c.Input.Value("name", "required", "分组名称"),
			"user_id":     _c.UserId,
			"is_commond":  _c.Input.GetInt64("is_commond"),
			"time_create": _time,
			"time_last":   _time,
		})
		_res := _db.Add()
		_c.Back.ApiBack("保存", _res)
	} else {
		// 更新
		_db := vant2.DB("chat_channel").Where(w.Map{
			"user_id":   _c.UserId,
			"id":        _c.Id,
			"is_delete": 0,
		})
		_db.Data(w.Map{
			"name":       _c.Input.Value("name", "required", "分组名称"),
			"is_commond": _c.Input.GetInt64("is_commond"),
			"time_last":  _time,
		})
		_c.Back.ApiBack("保存", _db.Update())
	}
}

// 清空某个分组下的对话
func (d *_chat) ChannelClear(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	//
	if _c.Id == 0 {
		_c.Back.ApiError(0, "参数有误")
	}
	_db := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"user_id":    _c.UserId,
		"channel_id": _c.Id,
	})
	_db.Delete()
	_c.Back.ApiSuccess("清空成功")
}

// 删除对话
func (d *_chat) Delete(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	//
	_db := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"user_id":   _c.UserId,
		"id":        _c.Input.Value("id", "required", "分类ID"),
		"is_delete": 0,
	})
	_res, _ := _db.Delete()
	_rows, _ := _res.RowsAffected()
	if _rows > 0 {
		_c.Back.ApiSuccess("删除成功")
	} else {
		_c.Back.ApiError(0, "删除失败")
	}
}

// 重新请求某个对话 就是删除id
func (d *_chat) Refresh(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)
	if _c.Id == 0 {
		_c.Back.ApiError(0, "参数有误")
	}
	// 就是将改组下的对话 大于Id的全部删除！
	_db := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"user_id":    _c.UserId,
		"channel_id": _c.Input.GetInt64("channel_id", "required", "分类ID"),
	}).Where("id >= ?", _c.Id)
	_db.Delete()
	_c.Back.ApiSuccess("重置成功")
}

// 修改聊天记录内容
func (d *_chat) Update(r *ghttp.Request) {
	_c := base.InitBaseCtx(r)

	// 获取要修改的聊天记录ID
	chatId := _c.Input.GetInt64("id")
	if chatId == 0 {
		_c.Back.ApiError(0, "聊天记录ID不能为空")
		return
	}

	// 获取新的内容
	newContent := _c.Input.GetString("content")
	if newContent == "" {
		_c.Back.ApiError(0, "内容不能为空")
		return
	}

	// 验证聊天记录是否存在且属于当前用户
	_checkDb := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"id":        chatId,
		"user_id":   _c.UserId,
		"is_delete": 0,
	})

	_existRecord := _checkDb.One()
	if _existRecord == nil {
		_c.Back.ApiError(0, "聊天记录不存在或无权限修改")
		return
	}

	// 更新聊天记录内容
	_updateDb := vant2.DB("pgvector_chat_history", "vsearch").Where(w.Map{
		"id":        chatId,
		"user_id":   _c.UserId,
		"is_delete": 0,
	})

	_updateData := w.Map{
		"content": newContent,
	}

	_result := _updateDb.Data(_updateData).Update()

	if _result {
		_c.Back.ApiSuccess("修改成功")
	} else {
		_c.Back.ApiError(0, "修改失败")
	}
}
