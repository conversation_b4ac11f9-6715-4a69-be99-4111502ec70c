package chat

import (
	authM "assistant/pods/auth/model"
	"assistant/pods/base"
	chats "assistant/pods/chat/service"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

type ChatInit struct {
	Model           string                   `json:"model"`
	Messages        []map[string]interface{} `json:"messages"`
	LastContent     string                   `json:"lastContent"`
	LastRule        string                   `json:"lastRule"`
	UserId          int64                    `json:"userId"`
	GroupId         int64                    `json:"groupId"`
	ErrorMsg        string                   `json:"errorMsg"`
	ChannelId       int64                    `json:"channelId"`
	ChatModelConfig *chats.ModelConfig       `json:"chatModelConfig"`
	Temperature     float64                  `json:"temperature"` // 温度
	MaxToken        int                      `json:"maxToken"`    // 最大token
	OpenContext     int                      `json:"openContext"` // 是否开启上下文
	Stream          bool                     `json:"stream"`      // 是否流式

	// 会话模式
	Mode      string `json:"mode"`      // 默认的chat
	Thinking  bool   `json:"thinking"`  // 是否开启思考
	WebSearch bool   `json:"webSearch"` // 是否开启Web搜索
}

// 获取模型列表
func (d *_chat) Models(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	_list := w.SliceMap{
		w.Map{
			"id":   "qwen2.5-7b",
			"name": "千问Qwen2.5-7B-Chat",
		},
		w.Map{
			"id":   "deepseek-r1",
			"name": "官方DeepSeek R1",
		},
		w.Map{
			"id":   "deepseek-v3",
			"name": "官方DeepSeek V3",
		},
		w.Map{
			"id":   "deepseek-search",
			"name": "官方联网DeepSeek Search",
		},
		w.Map{
			"id":   "gpt-4.1",
			"name": "ChatGPT-4.1",
		},
	}
	if d.UserId == 1 {
		_more := w.SliceMap{
			w.Map{
				"id":   "deepseek-v3-16k",
				"name": "云控急速DeepSeek V3",
			},
			w.Map{
				"id":   "deepseek-r1-16k",
				"name": "云控急速DeepSeek R1",
			},
			w.Map{
				"id":   "claude-3.7-sonnet",
				"name": "Claude-3.7-Sonnet",
			},
		}
		_list = append(_list, _more...)
	}
	// 获取请求参数
	d.Back.ApiSuccess("ok", _list)
}

// 初始化聊天
func _initSSEChat(r *ghttp.Request, stream ...bool) *ChatInit {
	d := &ChatInit{}
	_token := r.Header.Get("Authorization")
	messages := gconv.SliceMap(r.Get("messages"))
	d.Temperature = gconv.Float64(r.Get("temperature"))
	d.MaxToken = gconv.Int(r.Get("maxToken"))
	d.OpenContext = gconv.Int(r.Get("openContext")) //
	d.Stream = gconv.Bool(r.Get("stream"))          //

	d.Mode = gconv.String(r.Get("mode")) // 对话模式
	if d.Mode == "" || !vant2.InArray2(d.Mode, w.SliceStr{"chat", "rag"}) {
		d.Mode = "chat"
	}
	d.Thinking = gconv.Bool(r.Get("thinking"))   // 是否开启思考
	d.WebSearch = gconv.Bool(r.Get("webSearch")) // 是否开启Web搜索

	if len(stream) > 0 && stream[0] {
		d.Stream = stream[0]
	}
	_res, _userId := _authUser(_token, r.Header.Get("user-origin"))
	if _res != "" {
		d.ErrorMsg = _res
		return d
	}

	// 获取请求参数
	model := gconv.String(r.Get("model"))
	if model == "" {
		model = "qwen2.5-7b"
	}
	d.ChatModelConfig = chats.GetModelConfig(model, d.UserId)
	if d.ChatModelConfig == nil {
		d.ErrorMsg = "模型不存在"
		return d
	}
	if d.ChatModelConfig.Model == "" {
		d.ErrorMsg = "模型不存在"
		return d
	}

	// 验证 message 是否为数组形式
	if len(messages) == 0 {
		d.ErrorMsg = "message 不能为空"
		return d
	}

	// 取出最后一条消息并验证内容
	lastMsg := gconv.Map(messages[len(messages)-1])
	if len(lastMsg) == 0 {
		d.ErrorMsg = "message 格式不正确"
		return d
	}
	_lastContent := vant2.LodashGetString(lastMsg, "content")
	_lastRule := vant2.LodashGetString(lastMsg, "role")
	if _lastContent == "" {
		d.ErrorMsg = "消息内容不能为空"
		return d
	}
	if _lastRule != "user" {
		d.ErrorMsg = "消息类型错误"
		return d
	}

	d.Model = model
	d.Messages = messages
	d.LastContent = _lastContent
	d.LastRule = _lastRule
	d.UserId = _userId

	d.ChannelId = _getChannelId(gconv.Int64(r.Get("channel_id")), d.UserId, d.GroupId)
	// // 设置SSE相关的响应头

	if d.Stream {
		r.Response.Header().Set("Content-Type", "text/event-stream")
		r.Response.Header().Set("Cache-Control", "no-cache")
		r.Response.Header().Set("Connection", "keep-alive")
		r.Response.Header().Set("Access-Control-Allow-Origin", "*")
		r.Response.Header().Set("X-Accel-Buffering", "no")

		// 立即发送初始信息确保连接已建立
		r.Response.Write("retry: 1000\n\n")
		r.Response.Flush()
	}

	return d

}

// 鉴权 获取用户基本信息
func _authUser(token, authKey string) (string, int64) {
	_, _error := vant2.CheckHeadAuth(authKey) // 简单鉴权
	if _error != "" {
		return _error, 0
	}

	if token == "" {
		return "您尚未登录，请登录后再试", 0
	}
	// 检查Token
	_tokenService := &authM.TokenModel{}
	_userId := _tokenService.CheckToken(token)
	if _userId == 0 {
		return "登录已过期，请重新登录", 0
	}

	return "", _userId

}

func _getChannelId(channelId, userId, groupId int64) int64 {
	// 获取ChannelId
	if channelId != 0 {
		_count := vant2.DB("chat_channel").Where(w.Map{
			"id":      channelId,
			"user_id": userId,
		}).Count()
		if _count > 0 {
			return channelId
		}
	}

	// PostgreSQL需要特殊处理获取最后插入ID
	data := w.Map{
		"name":        "新建AI对话",
		"user_id":     userId,
		"group_id":    groupId,
		"time_create": vant2.Time(),
	}

	// 方法1：使用InsertAndGetId并指定主键字段名
	_res, _ := vant2.DB("chat_channel").Data(data).InsertAndGetId()

	vant2.Warning("创建ChannelId", _res)
	if _res > 0 {
		return _res
	}
	return 0
}
