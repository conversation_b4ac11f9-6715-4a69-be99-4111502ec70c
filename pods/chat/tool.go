package chat

import (
	"encoding/json"
	"fmt"
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

type TokenTotal struct {
	Total      int64 `json:"total"`
	Prompt     int64 `json:"prompt"`
	Completion int64 `json:"completion"`
}

// SseError SSE错误处理方法，向客户端发送错误信息并终止连接
// @param r *ghttp.Request 请求对象
// @param code int 错误状态码
// @param message string 错误信息
func SseError(r *ghttp.Request, code int, message string) {
	SseErrorWithData(r, code, message, nil)
}

// SseErrorWithData 增强版SSE错误处理方法，支持传递额外数据
// @param r *ghttp.Request 请求对象
// @param code int 错误状态码
// @param message string 错误信息
// @param data interface{} 附加数据对象，可以是任意类型
func SseErrorWithData(r *ghttp.Request, code int, message string, data interface{}) {
	// 确保响应头设置正确
	r.Response.Header().Set("Content-Type", "text/event-stream")

	// 设置HTTP状态码
	// r.Response.WriteStatus(code)

	// 构造错误信息JSON
	response := g.Map{
		"code":    code,
		"message": message,
		"success": false,
	}

	// 如果有额外数据，添加到响应中
	if data != nil {
		response["data"] = data
	}

	// 序列化为JSON
	errorJson, _ := json.Marshal(response)

	// 发送错误信息
	_msg := fmt.Sprintf("data: %s\n\n", string(errorJson))
	vant2.Error("sseError", _msg)
	r.Response.Write(_msg)
	r.Response.Flush()

	// 发送[DONE]终止连接
	r.Response.Write("data: [DONE]\n\n")
	r.Response.Flush()
}

// SetSSEHeaders 设置SSE响应的HTTP头信息
// @param r *ghttp.Request 请求对象
func SetSSEHeaders(r *ghttp.Request) {
	r.Response.Header().Set("Content-Type", "text/event-stream")
	r.Response.Header().Set("Cache-Control", "no-cache")
	r.Response.Header().Set("Connection", "keep-alive")
	r.Response.Header().Set("Access-Control-Allow-Origin", "*")
	r.Response.Header().Set("X-Accel-Buffering", "no")

	// 立即发送初始信息确保连接已建立
	r.Response.Write("retry: 1000\n\n")
	r.Response.Flush()
}

// CheckApiKey 检查API密钥是否有效
// @param apiKey string 待检查的API密钥
// @return int64 用户ID，如果无效则返回0
// @return string 错误信息
func CheckApiKey(apiKey string) (int64, string) {
	// 此处实现API密钥验证逻辑
	// 简单示例，实际应用中应该查询数据库或配置文件验证

	_apiKey := strings.ReplaceAll(apiKey, "Bearer ", "")
	_apiKey = strings.ReplaceAll(_apiKey, "bearer ", "")

	if apiKey == "" {
		return 0, "API密钥不能为空"
	}
	_one := vant2.DB("apikey").Where(w.Map{
		"key":         _apiKey,
		"time_delete": 0,
		// "time_end >=": vant2.Time(),
	}).Fields("id,user_id,price").
		Where("time_end = 0 OR time_end >= ?", vant2.Time()).
		One()
	if _one == nil {
		return 0, "API密钥无效"
	}
	// 返回用户ID
	return gconv.Int64(_one["user_id"]), ""
}

// HandleSSEResponse 处理SSE响应数据
// @param r *ghttp.Request 请求对象
// @param channelId int64 渠道ID
// @param content *string 内容指针
// @param tokenTotal *TokenTotal token统计指针
// @return func(d w.Map) 回调函数
func HandleSSEResponse(r *ghttp.Request, channelId int64, content *string, tokenTotal *TokenTotal) func(d w.Map) {
	return func(d w.Map) {
		d["channel_id"] = channelId // 反向注入
		vant2.Console(vant2.LodashGetString(d, "choices.0.delta.content"))
		_msg := vant2.JsonEncoder(d)
		if strings.Contains(_msg, "[DONE]") {
			r.Response.Write("data: [DONE]\n\n")
			r.Response.Flush()
			return
		} else {
			// 发送数据
			r.Response.Write(fmt.Sprintf("data: %s\n\n", _msg))
			r.Response.Flush()
			// 拼接返回文本
			*content = *content + vant2.LodashGetString(d, "choices.0.delta.content")
			// 计算token
			tokenTotal.Total = vant2.LodashGetInt64(d, "usage.total_tokens")
			tokenTotal.Prompt = vant2.LodashGetInt64(d, "usage.prompt_tokens")
			tokenTotal.Completion = vant2.LodashGetInt64(d, "usage.completion_tokens")
		}
	}
}
