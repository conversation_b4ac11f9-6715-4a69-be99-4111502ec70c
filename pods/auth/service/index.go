package auths

import (
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

var (
	_tokenExpire int64  = 3600 * 24 * 30 // 7天
	_tokenModel  string = "assistant"
)

// 从token加载用户Id
func GetUserIdFromToken(token string) int64 {
	if token == "" {
		return 0 //  lock
	}
	_token := strings.ToLower(token)
	if strings.HasPrefix(_token, "bearer ") {
		_token = strings.ReplaceAll(_token, "bearer ", "")
	}

	_where := w.Map{
		"token": _token,
		"model": _tokenModel,
	}
	_info := vant2.DB("token").Where("time_expire > ?", vant2.Time()).Fields("user_id").Where(_where).One()
	if _info == nil {
		return 0
	}
	return gconv.Int64(_info["user_id"]) //
}

// 根据用户Id生成Token
func TokenFromUserId(userId int64, deviceType string) string {
	_time := vant2.Time()

	// 先查询用户是否已有相同设备类型的token
	_whereDevice := w.Map{
		"user_id":     userId,
		"model":       _tokenModel,
		"device_type": deviceType,
	}
	_existingToken := vant2.DB("token").Fields("token,time_expire,device_type").Where(_whereDevice).Where("time_expire > ?", _time).One()

	// 如果存在相同设备类型的有效token，直接更新过期时间并返回
	if _existingToken != nil {
		_updateInfo := w.Map{
			"time_expire": _time + _tokenExpire,
		}
		_b := vant2.DB("token").Where(_whereDevice).Data(_updateInfo).Update()
		if _b {
			return gconv.String(_existingToken["token"])
		}
	}

	// 查询用户是否已有其他设备类型的token
	_whereUser := w.Map{
		"user_id": userId,
		"model":   _tokenModel,
	}
	_otherToken := vant2.DB("token").Fields("token,time_expire,device_type").Where(_whereUser).Where("time_expire > ?", _time).One()

	// 如果存在其他设备类型的有效token且设备类型不同，更新该token的设备类型和过期时间
	if _otherToken != nil && gconv.String(_otherToken["device_type"]) != deviceType {
		_updateInfo := w.Map{
			"time_expire": _time + _tokenExpire,
			"device_type": deviceType,
		}
		_b := vant2.DB("token").Where(_whereUser).Data(_updateInfo).Update()
		if _b {
			return gconv.String(_otherToken["token"])
		}
	}

	// 生成新token
	_tokenKey := _generate(userId, _time)
	_info := w.Map{
		"token":       _tokenKey,
		"time_create": _time,
		"time_expire": _time + _tokenExpire,
		"device_type": deviceType,
		"user_id":     userId,
		"model":       _tokenModel,
	}

	// 检查是否已存在该用户的token记录
	_checkToken := vant2.DB("token").Fields("token,time_expire").Where(_whereUser).One()
	var _b bool
	if _checkToken != nil {
		_b = vant2.DB("token").Where(_whereUser).Data(_info).Update()
	} else {
		_b = vant2.DB("token").Data(_info).Add()
	}

	if _b {
		return _tokenKey // 返回Token
	} else {
		return ""
	}
}

// 生成随机token 简单实现
func _generate(userId, dateTime int64) string {
	_info := "user_id=" + gconv.String(userId) + "&time=" + gconv.String(dateTime)
	return "ut_" + vant2.Password(_info)
}

// 清除Token
func ClearToken(token string) bool {
	if token == "" {
		return false
	}
	_where := w.Map{
		"token": token,
		"model": _tokenModel,
	}
	_res, _ := vant2.DB("token").Where(_where).Delete()
	_b, _ := _res.RowsAffected()
	return _b > 0
}
