package task

import (
	"assistant/pods/base"
	"assistant/pods/task/taskservice"
	"encoding/json"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// TaskList 获取待办事项列表
func TaskList(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取查询参数
	categoryId := gconv.Int(_base.Input.Value("categoryId"))
	tagsStr := gconv.String(_base.Input.Value("tags"))
	statusStr := gconv.String(_base.Input.Value("status"))  // 注意：前端传的是status不是statuses
	priority := gconv.String(_base.Input.Value("priority")) // 前端传的是单个priority字符串
	keyword := gconv.String(_base.Input.Value("keyword"))
	datesStr := gconv.String(_base.Input.Value("dates"))
	startTime := gconv.Int64(_base.Input.Value("startTime"))
	endTime := gconv.Int64(_base.Input.Value("endTime"))

	// 处理标签字符串数组
	var tags []string
	if tagsStr != "" {
		err := json.Unmarshal([]byte(tagsStr), &tags)
		if err != nil {
			_base.Back.ApiError(-1, "参数错误：标签数据格式不正确")
			return
		}
	}

	// 处理状态数组
	var statuses []string
	if statusStr != "" {
		err := json.Unmarshal([]byte(statusStr), &statuses)
		if err != nil {
			_base.Back.ApiError(-2, "参数错误：状态数据格式不正确")
			return
		}
	}

	// 处理优先级 - 前端传的是单个字符串，转换为数组以保持服务层接口一致
	var priorities []string
	if priority != "" {
		priorities = append(priorities, priority)
	}

	// 处理日期数组
	var dates []string
	if datesStr != "" {
		err := json.Unmarshal([]byte(datesStr), &dates)
		if err != nil {
			_base.Back.ApiError(-4, "参数错误：日期数据格式不正确")
			return
		}
	}

	listService := &taskservice.List{}
	list, total := listService.GetTaskList(_base.UserId, _base.PageNum, _base.PageSize, categoryId, tags, statuses, priorities, keyword, dates, startTime, endTime)

	_base.Back.ApiRows(_base.Back.ToSnake(list), total)
}

// TaskStatistics 获取待办事项统计信息
func TaskStatistics(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	listService := &taskservice.List{}
	stats := listService.GetTaskStatistics(_base.UserId)

	_base.Back.ApiSuccess("获取统计信息成功", _base.Back.ToSnake(stats))
}

// TaskMonthDates 获取指定月份有待办事项的日期列表
func TaskMonthDates(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取月份参数
	month := gconv.String(_base.Input.Value("month")) // 月份格式：2025-07

	if month == "" {
		_base.Back.ApiError(-1, "月份参数不能为空")
		return
	}

	// 调用服务层方法
	listService := &taskservice.List{}
	dates, err := listService.GetMonthDates(_base.UserId, month)
	if err != nil {
		_base.Back.ApiError(-1, "查询失败", err.Error())
		return
	}

	// 返回结果
	_base.Back.ApiSuccess("查询成功", dates)
}
