package task

import (
	"assistant/pods/base"
	"assistant/pods/task/taskservice"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// CategorySave 保存分类（新增或编辑）
func CategorySave(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	name := _base.Input.Value("name")
	icon := gconv.String(_base.Input.Value("icon"))
	color := gconv.String(_base.Input.Value("color"))
	weight := gconv.Int(_base.Input.Value("weight"))

	if name == "" {
		_base.Back.ApiError(-1, "参数错误：分类名称不能为空")
		return
	}

	categoryService := &taskservice.Category{}
	result, msg := categoryService.Save(_base.UserId, _base.Id, gconv.String(name), icon, color, weight)
	if result == false {
		_base.Back.ApiError(-2, msg)
		return
	}
	_base.Back.ApiBack("保存分类", result)
}

// CategoryDelete 删除分类
func CategoryDelete(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：分类ID不能为空")
		return
	}

	categoryService := &taskservice.Category{}
	result := categoryService.Delete(_base.UserId, _base.Id)
	if !result {
		_base.Back.ApiError(-2, "删除失败：该分类下还有待办事项，请先处理完相关事项")
		return
	}
	_base.Back.ApiBack("删除分类", result)
}

// CategoryList 获取分类列表
func CategoryList(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 检查是否需要统计数量
	withCount := gconv.Bool(_base.Input.Value("withCount"))

	categoryService := &taskservice.Category{}
	list := categoryService.List(_base.UserId, withCount)
	_base.Back.ApiSuccess("获取分类列表成功", _base.Back.ToSnake(list))
}

// CategoryGet 获取分类详情
func CategoryGet(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：分类ID不能为空")
		return
	}

	categoryService := &taskservice.Category{}
	data := categoryService.Get(_base.UserId, _base.Id)
	if data == nil {
		_base.Back.ApiError(-2, "分类不存在")
		return
	}

	_base.Back.ApiSuccess("获取分类详情成功", _base.Back.ToSnake(data))
}
