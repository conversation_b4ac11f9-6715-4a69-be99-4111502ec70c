package taskservice

import (
	"fmt"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// StatusLog 状态日志服务
type StatusLog struct{}

// 变动类型常量定义
const (
	ModeCreateTask     = "创建任务"
	ModeUpdateStatus   = "修改状态"
	ModeUpdateCategory = "修改分类"
	ModeUpdateTags     = "修改标签"
	ModeUpdatePriority = "修改优先级"
	ModeUpdateDeadline = "修改截止时间"
	ModeUpdateContent  = "修改内容"
	ModeUpdateCommond  = "修改置顶"
	ModeUserReply      = "用户回复"
)

// AddSystemLog 添加系统日志记录
func (s *StatusLog) AddSystemLog(taskId interface{}, userId interface{}, mode string, remark string) bool {
	return vant2.DB("task_status_log").Data(g.Map{
		"task_id":     taskId,
		"user_id":     userId,
		"is_system":   1,
		"mode":        mode,
		"remark":      remark,
		"top_id":      0,
		"time_create": vant2.Time(),
	}).Add()
}

// AddUserReply 添加用户回复
func (s *StatusLog) AddUserReply(taskId interface{}, userId interface{}, remark string, topId interface{}) bool {
	if topId == nil {
		topId = 0
	}

	return vant2.DB("task_status_log").Data(g.Map{
		"task_id":     taskId,
		"user_id":     userId,
		"is_system":   0,
		"mode":        ModeUserReply,
		"remark":      remark,
		"top_id":      topId,
		"time_create": vant2.Time(),
	}).Add()
}

// ProcessTaskChanges 处理任务变动并生成相应的日志记录
func (s *StatusLog) ProcessTaskChanges(taskId interface{}, userId interface{}, oldData g.Map, newData g.Map) {
	// 如果是新建任务（oldData为空）
	if len(oldData) == 0 {
		s.AddSystemLog(taskId, userId, ModeCreateTask, "创建任务")
		return
	}

	// 获取用户名称（这里简化处理，实际项目中可能需要查询用户表）
	userName := s.getUserName(userId)

	// 检查各个字段的变动
	s.checkStatusChange(taskId, userId, userName, oldData, newData)
	s.checkCategoryChange(taskId, userId, userName, oldData, newData)
	s.checkPriorityChange(taskId, userId, userName, oldData, newData)
	s.checkDeadlineChange(taskId, userId, userName, oldData, newData)
	s.checkContentChange(taskId, userId, userName, oldData, newData)
	s.checkCommondChange(taskId, userId, userName, oldData, newData)
}

// getUserName 获取用户名称（查询真实用户昵称）
func (s *StatusLog) getUserName(userId interface{}) string {
	user := vant2.DB("user").
		Where("id", userId).
		Fields("user_nick").
		Cache(300).
		One()

	if user != nil && gconv.String(user["user_nick"]) != "" {
		return gconv.String(user["user_nick"])
	}

	// 如果没有昵称或查询失败，返回默认格式
	return fmt.Sprintf("用户%v", userId)
}

// getUserInfo 获取用户信息（昵称和头像）
func (s *StatusLog) getUserInfo(userId interface{}) g.Map {
	user := vant2.DB("user").
		Where("id", userId).
		Fields("user_nick, avatar").
		Cache(300).
		One()

	result := g.Map{
		"user_nick": fmt.Sprintf("用户%v", userId),
		"avatar":    "",
	}

	if user != nil {
		if nickname := gconv.String(user["user_nick"]); nickname != "" {
			result["user_nick"] = nickname
		}
		if avatar := gconv.String(user["avatar"]); avatar != "" {
			result["avatar"] = avatar
		}
	}

	return result
}

// checkStatusChange 检查状态变动
func (s *StatusLog) checkStatusChange(taskId interface{}, userId interface{}, userName string, oldData g.Map, newData g.Map) {
	oldStatus := gconv.String(oldData["status"])
	newStatus := gconv.String(newData["status"])

	if oldStatus != newStatus && newStatus != "" {
		remark := fmt.Sprintf("%s 将状态从 「%s」 设置为 「%s」", userName, oldStatus, newStatus)
		s.AddSystemLog(taskId, userId, ModeUpdateStatus, remark)
	}
}

// checkCategoryChange 检查分类变动
func (s *StatusLog) checkCategoryChange(taskId interface{}, userId interface{}, userName string, oldData g.Map, newData g.Map) {
	oldCategoryId := gconv.Int(oldData["category_id"])
	newCategoryId := gconv.Int(newData["category_id"])

	if oldCategoryId != newCategoryId {
		oldCategoryName := s.getCategoryNameWithUser(oldCategoryId, userId)
		newCategoryName := s.getCategoryNameWithUser(newCategoryId, userId)

		remark := fmt.Sprintf("%s 将分类从 「%s」 设置为 「%s」", userName, oldCategoryName, newCategoryName)
		s.AddSystemLog(taskId, userId, ModeUpdateCategory, remark)
	}
}

// checkPriorityChange 检查优先级变动
func (s *StatusLog) checkPriorityChange(taskId interface{}, userId interface{}, userName string, oldData g.Map, newData g.Map) {
	oldPriority := gconv.String(oldData["priority"])
	newPriority := gconv.String(newData["priority"])

	if oldPriority != newPriority {
		if oldPriority == "" {
			oldPriority = "无"
		}
		if newPriority == "" {
			newPriority = "无"
		}

		remark := fmt.Sprintf("%s 将优先级从 「%s」 设置为 「%s」", userName, oldPriority, newPriority)
		s.AddSystemLog(taskId, userId, ModeUpdatePriority, remark)
	}
}

// checkDeadlineChange 检查截止时间变动
func (s *StatusLog) checkDeadlineChange(taskId interface{}, userId interface{}, userName string, oldData g.Map, newData g.Map) {
	oldDeadline := gconv.Int64(oldData["deadline"])
	newDeadline := gconv.Int64(newData["deadline"])

	if oldDeadline != newDeadline {
		oldDeadlineStr := "无截止时间"
		newDeadlineStr := "无截止时间"

		if oldDeadline > 0 {
			oldDeadlineStr = time.Unix(oldDeadline, 0).Format("2006-01-02 15:04:05")
		}
		if newDeadline > 0 {
			newDeadlineStr = time.Unix(newDeadline, 0).Format("2006-01-02 15:04:05")
		}

		remark := fmt.Sprintf("%s 将截止时间从 「%s」 设置为 「%s」", userName, oldDeadlineStr, newDeadlineStr)
		s.AddSystemLog(taskId, userId, ModeUpdateDeadline, remark)
	}
}

// checkContentChange 检查内容变动
func (s *StatusLog) checkContentChange(taskId interface{}, userId interface{}, userName string, oldData g.Map, newData g.Map) {
	oldContent := strings.TrimSpace(gconv.String(oldData["content"]))
	newContent := strings.TrimSpace(gconv.String(newData["content"]))

	if oldContent != newContent {
		remark := fmt.Sprintf("%s 修改了任务内容", userName)
		s.AddSystemLog(taskId, userId, ModeUpdateContent, remark)
	}
}

// ProcessTagChanges 处理标签变动
func (s *StatusLog) ProcessTagChanges(taskId interface{}, userId interface{}, oldTags []string, newTags []string) {
	userName := s.getUserName(userId)

	// 转换为map便于比较
	oldTagMap := make(map[string]bool)
	newTagMap := make(map[string]bool)

	for _, tag := range oldTags {
		oldTagMap[tag] = true
	}
	for _, tag := range newTags {
		newTagMap[tag] = true
	}

	// 检查是否有变动
	hasChanges := false
	if len(oldTags) != len(newTags) {
		hasChanges = true
	} else {
		for _, tag := range oldTags {
			if !newTagMap[tag] {
				hasChanges = true
				break
			}
		}
	}

	if hasChanges {
		oldTagsStr := "无标签"
		newTagsStr := "无标签"

		if len(oldTags) > 0 {
			oldTagsStr = strings.Join(oldTags, "、")
		}
		if len(newTags) > 0 {
			newTagsStr = strings.Join(newTags, "、")
		}

		remark := fmt.Sprintf("%s 将标签从 「%s」 设置为 「%s」", userName, oldTagsStr, newTagsStr)
		s.AddSystemLog(taskId, userId, ModeUpdateTags, remark)
	}
}

// getCategoryNameWithUser 获取分类名称（验证用户权限）
func (s *StatusLog) getCategoryNameWithUser(categoryId int, userId interface{}) string {
	if categoryId <= 0 {
		return "无分类"
	}

	category := vant2.DB("task_category").
		Where("id", categoryId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("name").
		One()

	if category != nil {
		return gconv.String(category["name"])
	}

	return "未知分类"
}

// GetTaskLogs 获取任务的所有日志记录（验证用户权限）
func (s *StatusLog) GetTaskLogs(taskId interface{}, userId interface{}) []g.Map {
	// 先验证任务是否属于当前用户
	taskExists := vant2.DB("task").
		Where("id", taskId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if taskExists == 0 {
		g.Log().Warningf(nil, "GetTaskLogs: 任务不存在或不属于当前用户, taskId=%v, userId=%v", taskId, userId)
		return []g.Map{}
	}

	_list := vant2.DB("task_status_log").
		Where("task_id", taskId).
		Order("time_create DESC, id DESC").
		Select()
	return gconv.SliceMap(_list)
}

// GetTaskLogsWithUserInfo 获取任务的所有日志记录（包含用户信息，验证用户权限）
func (s *StatusLog) GetTaskLogsWithUserInfo(taskId interface{}, userId interface{}) []g.Map {
	logs := s.GetTaskLogs(taskId, userId)

	// 为每条日志添加用户信息
	for i := range logs {
		userInfo := s.getUserInfo(logs[i]["user_id"])
		logs[i]["user_name"] = userInfo["user_nick"]
		logs[i]["user_avatar"] = userInfo["avatar"]
		logs[i]["time_create_formatted"] = time.Unix(gconv.Int64(logs[i]["time_create"]), 0).Format("2006-01-02 15:04:05")
	}

	return logs
}

// AddReply 添加用户回复（公开方法供外部调用）
func (s *StatusLog) AddReply(taskId interface{}, userId interface{}, content string, topId interface{}) (bool, string) {
	content = strings.TrimSpace(content)
	if content == "" {
		return false, "回复内容不能为空"
	}

	// 验证任务是否存在
	taskExists := vant2.DB("task").
		Where("id", taskId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if taskExists == 0 {
		return false, "任务不存在"
	}

	// 如果指定了上级回复ID，验证其是否存在
	if topId != nil && gconv.Int64(topId) > 0 {
		replyExists := vant2.DB("task_status_log").
			Where("id", topId).
			Where("task_id", taskId).
			Count()

		if replyExists == 0 {
			return false, "指定的上级回复不存在"
		}
	}

	success := s.AddUserReply(taskId, userId, content, topId)
	if success {
		return true, "回复添加成功"
	}

	return false, "回复添加失败"
}

// DeleteReply 删除用户回复（只能删除自己的回复）
func (s *StatusLog) DeleteReply(replyId interface{}, userId interface{}) (bool, string) {
	// 验证回复是否存在且属于当前用户
	reply := vant2.DB("task_status_log").
		Where("id", replyId).
		Where("user_id", userId).
		// Where("is_system", 0). // 只能删除用户回复，不能删除系统日志
		Count()

	if reply == 0 {
		return false, "回复不存在或无权限删除"
	}

	// 检查该回复是否有子回复
	hasChildReplies := vant2.DB("task_status_log").
		Where("top_id", replyId).
		Count()

	if hasChildReplies > 0 {
		return false, "该回复下有子回复，无法删除"
	}

	// 删除回复
	_, err := vant2.DB("task_status_log").
		Where("id", replyId).
		Where("user_id", userId).
		// Where("is_system", 0).
		Delete()

	if err == nil {
		return true, "回复删除成功"
	}

	return false, "回复删除失败"
}

// checkCommondChange 检查置顶状态变动
func (s *StatusLog) checkCommondChange(taskId interface{}, userId interface{}, userName string, oldData g.Map, newData g.Map) {
	oldCommond := gconv.Int(oldData["is_commond"])
	newCommond := gconv.Int(newData["is_commond"])

	// 只有在置顶状态真正发生变化时才记录日志
	if oldCommond != newCommond {
		var remark string
		if newCommond == 1 {
			remark = fmt.Sprintf("%s 将任务设置为置顶", userName)
		} else {
			remark = fmt.Sprintf("%s 取消了任务置顶", userName)
		}
		s.AddSystemLog(taskId, userId, ModeUpdateCommond, remark)
	}
}
