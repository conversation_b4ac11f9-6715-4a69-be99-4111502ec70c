package taskservice

import (
	"encoding/json"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// List 列表查询服务
type List struct{}

// GetTaskList 获取待办事项列表
func (l *List) GetTaskList(userId interface{}, pageNum int, pageSize int, categoryId int, tags []string, statuses []string, priorities []string, keyword string, dates []string, startTime int64, endTime int64) (interface{}, int) {
	// 构建基础查询
	db := vant2.DB("task t").
		LeftJoin("task_category c", "t.category_id = c.id").
		Where("t.user_id", userId).
		Where("t.is_delete", 0)

	// 分类筛选
	if categoryId > 0 {
		db = db.Where("t.category_id", categoryId)
	}

	// 状态筛选（数组）- 直接按中文状态名称匹配
	if len(statuses) > 0 {
		db = db.Where("t.status", statuses)
	}

	// 优先级筛选（数组）- 直接按中文优先级名称匹配
	if len(priorities) > 0 {
		db = db.Where("t.priority", priorities)
	}

	// 关键词搜索（搜索内容）
	if keyword != "" {
		keyword = strings.TrimSpace(keyword)
		db = db.Where("t.content LIKE ?", "%"+keyword+"%")
	}

	// 处理dates数组（特定日期筛选）
	if len(dates) > 0 {
		var dateConditions []string
		for _, dateStr := range dates {
			dateStr = strings.TrimSpace(dateStr)
			if dateStr == "" {
				continue
			}
			// 使用Go的time包进行正确的日期解析
			if parsedTime, err := time.Parse("2006-1-2", dateStr); err == nil {
				// 计算当天的开始和结束时间戳
				startOfDay := parsedTime.Unix()
				endOfDay := parsedTime.Add(24*time.Hour - time.Second).Unix()
				dateConditions = append(dateConditions, "(t.deadline BETWEEN "+gconv.String(startOfDay)+" AND "+gconv.String(endOfDay)+")")
			}
		}
		if len(dateConditions) > 0 {
			db = db.Where("(" + strings.Join(dateConditions, " OR ") + ")")
		}
	}

	// 处理startTime和endTime（时间戳范围）
	if startTime > 0 && endTime > 0 && startTime <= endTime {
		db = db.Where("t.deadline BETWEEN ? AND ?", startTime, endTime)
	} else if startTime > 0 {
		db = db.Where("t.deadline >= ?", startTime)
	} else if endTime > 0 {
		db = db.Where("t.deadline <= ?", endTime)
	}

	// 标签筛选（如果有标签筛选条件）- 按中文标签名称匹配，包含任意一个即可
	if len(tags) > 0 {
		// 直接使用EXISTS子查询，性能更好
		db = db.Where("EXISTS (SELECT 1 FROM dede_task_tag_relation r "+
			"LEFT JOIN dede_task_tag tag ON r.tag_id = tag.id "+
			"WHERE r.task_id = t.id "+
			"AND tag.user_id = ? "+
			"AND tag.is_delete = 0 "+
			"AND tag.name IN (?))", userId, tags)
	}

	// 获取总数
	total := db.Count()

	// 分页处理
	if pageNum < 1 {
		pageNum = 1
	}
	if pageSize < 1 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	offset := (pageNum - 1) * pageSize

	// 查询列表数据
	list, _ := db.Fields("t.id,t.category_id,t.content,t.images,t.status,t.priority,t.deadline,t.is_commond,t.time_create,t.time_update,c.name as category_name,c.icon as category_icon,c.color as category_color").
		Order("t.is_commond DESC, t.time_update DESC, t.id DESC").
		Limit(offset, pageSize).
		All()

	if len(list) == 0 {
		return []g.Map{}, total
	}

	// 批量获取所有待办事项的标签
	var taskIds []interface{}
	for _, item := range list {
		taskIds = append(taskIds, item["id"])
	}

	// 查询所有相关标签
	tagRelations := vant2.DB("task_tag_relation r").
		LeftJoin("task_tag tag", "r.tag_id = tag.id").
		Where("r.task_id", taskIds).
		Where("tag.user_id", userId).
		Where("tag.is_delete", 0).
		Fields("r.task_id,tag.id as tag_id,tag.name as tag_name,tag.color as tag_color").
		Order("tag.name ASC").
		Select()

	// 构建标签映射
	tagMap := make(map[string][]g.Map)
	for _, relation := range tagRelations {
		taskId := gconv.String(relation["task_id"])
		tagInfo := g.Map{
			"id":    relation["tag_id"],
			"name":  relation["tag_name"],
			"color": relation["tag_color"],
		}
		tagMap[taskId] = append(tagMap[taskId], tagInfo)
	}

	// 组装最终结果
	var result []g.Map
	for _, item := range list {
		// 处理图片数组
		var images []string
		if item["images"] != nil && gconv.String(item["images"]) != "" {
			json.Unmarshal([]byte(gconv.String(item["images"])), &images)
		}

		taskData := g.Map{
			"id":             item["id"],
			"category_id":    item["category_id"],
			"content":        item["content"],
			"images":         images,
			"status":         item["status"],
			"priority":       item["priority"],
			"deadline":       item["deadline"],
			"is_commond":     item["is_commond"],
			"time_create":    item["time_create"],
			"time_update":    item["time_update"],
			"category_name":  item["category_name"],
			"category_icon":  item["category_icon"],
			"category_color": item["category_color"],
		}

		// 添加标签信息
		taskId := gconv.String(item["id"])
		if tags, exists := tagMap[taskId]; exists {
			taskData["tags"] = tags
		} else {
			taskData["tags"] = []g.Map{}
		}

		result = append(result, taskData)
	}

	return result, total
}

// GetMonthDates 获取指定月份有待办事项（按截止日期）的日期列表
// 参数：userId 用户ID，month 月份格式如 2025-07
// 返回：该月份有待办事项的日期数组，格式如 ["2025-07-01", "2025-07-03"]
func (l *List) GetMonthDates(userId interface{}, month string) ([]string, error) {
	// 解析月份参数
	monthTime, err := time.Parse("2006-01", month)
	if err != nil {
		return nil, err
	}

	// 计算月份开始和结束时间戳
	startTime := monthTime
	endTime := monthTime.AddDate(0, 1, 0).Add(-time.Second)
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	// 查询该月份有截止日期的待办事项，按日期分组
	// 使用 FROM_UNIXTIME 将时间戳转换为日期格式进行分组
	records := vant2.DB("task").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("deadline > 0").                  // 只查询有截止日期的待办事项
		Where("deadline >= ?", startTimestamp). // 截止日期在月份开始时间之后
		Where("deadline <= ?", endTimestamp).   // 截止日期在月份结束时间之前
		Fields("DATE(FROM_UNIXTIME(deadline)) as deadline_date").
		Group("DATE(FROM_UNIXTIME(deadline))").
		Order("deadline_date ASC").
		Select()

	var dates []string
	for _, record := range records {
		if dateStr := gconv.String(record["deadline_date"]); dateStr != "" {
			dates = append(dates, dateStr)
		}
	}

	return dates, nil
}

// GetTaskStatistics 获取待办事项统计信息
func (l *List) GetTaskStatistics(userId interface{}) interface{} {
	// 总数统计
	totalCount := vant2.DB("task").
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	// 按状态统计
	statusStats := vant2.DB("task").
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("status, COUNT(*) as count").
		Group("status").
		Select()

	// 按优先级统计
	priorityStats := vant2.DB("task").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("priority != ''").
		Fields("priority, COUNT(*) as count").
		Group("priority").
		Select()

	// 按分类统计
	categoryStats := vant2.DB("task t").
		LeftJoin("task_category c", "t.category_id = c.id").
		Where("t.user_id", userId).
		Where("t.is_delete", 0).
		Fields("c.id as category_id, c.name as category_name, c.icon as category_icon, c.color as category_color, COUNT(*) as count").
		Group("c.id").
		Select()

	// 今日到期数量
	currentTime := vant2.Time()
	startOfDay := currentTime - (currentTime % 86400)
	endOfDay := startOfDay + 86399
	todayDeadlineCount := vant2.DB("task").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("deadline BETWEEN ? AND ?", startOfDay, endOfDay).
		Count()

	// 已过期数量
	overdueCount := vant2.DB("task").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("deadline > 0 AND deadline < ?", currentTime).
		Count()

	return g.Map{
		"total_count":          totalCount,
		"status_stats":         statusStats,
		"priority_stats":       priorityStats,
		"category_stats":       categoryStats,
		"today_deadline_count": todayDeadlineCount,
		"overdue_count":        overdueCount,
	}
}
