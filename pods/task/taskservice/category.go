package taskservice

import (
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Category 分类服务
type Category struct{}

// Save 保存分类（新增或编辑）
func (c *Category) Save(userId interface{}, categoryId interface{}, name string, icon string, color string, weight int) (interface{}, string) {
	name = strings.TrimSpace(name)
	icon = strings.TrimSpace(icon)
	color = strings.TrimSpace(color)

	if name == "" {
		return false, "分类名称不能为空"
	}

	// 设置默认值
	if weight < 0 {
		weight = 0
	}
	if color == "" {
		color = "primary"
	}
	if icon == "" {
		icon = "📝"
	}

	id := gconv.Int(categoryId)

	// 新增时检查分类数量限制
	if id == 0 {
		currentCount := vant2.DB("task_category").
			Where("user_id", userId).
			Where("is_delete", 0).
			Count()

		if currentCount >= 50 {
			return false, "分类数量已达上限（最多50个分类）"
		}
	}

	// 检查分类名称是否重复
	db := vant2.DB("task_category").
		Where("user_id", userId).
		Where("name", name).
		Where("is_delete", 0)

	// 如果是编辑，排除自己
	if id > 0 {
		db = db.Where("id !=", id)
	}

	exists := db.Count()
	if exists > 0 {
		return false, "分类名称已存在"
	}

	// 新增
	if id == 0 {
		categoryId := vant2.DB("task_category").AddGetId(g.Map{
			"user_id":     userId,
			"name":        name,
			"icon":        icon,
			"color":       color,
			"weight":      weight,
			"time_create": vant2.Time(),
			"time_update": vant2.Time(),
			"is_delete":   0,
		})
		if categoryId > 0 {
			return categoryId, "分类创建成功"
		}
		return false, "分类创建失败"
	}

	// 编辑
	result := vant2.DB("task_category").
		Where("id", id).
		Where("user_id", userId).
		Data(g.Map{
			"name":        name,
			"icon":        icon,
			"color":       color,
			"weight":      weight,
			"time_update": vant2.Time(),
		}).Update()

	if result {
		return true, "分类更新成功"
	}
	return false, "分类更新失败"
}

// Delete 删除分类（软删除）
func (c *Category) Delete(userId interface{}, categoryId interface{}) bool {
	// 检查分类下是否有待办事项
	taskCount := vant2.DB("task").
		Where("category_id", categoryId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if taskCount > 0 {
		return false // 有待办事项时不允许删除
	}

	return vant2.DB("task_category").
		Where("id", categoryId).
		Where("user_id", userId).
		Data(g.Map{
			"is_delete":   1,
			"time_update": vant2.Time(),
		}).Update()
}

// List 获取分类列表
func (c *Category) List(userId interface{}, withCount ...bool) interface{} {
	result, _ := vant2.DB("task_category").
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("id,name,icon,color,weight,time_create").
		Order("weight DESC, id DESC").
		All()

	// 如果用户没有任何分类，自动创建默认分类
	if len(result) == 0 {
		c.GetDefaultId(userId)
		// 重新查询
		result, _ = vant2.DB("task_category").
			Where("user_id", userId).
			Where("is_delete", 0).
			Fields("id,name,icon,color,weight,time_create").
			Order("weight DESC, id DESC").
			All()
	}

	// 如果需要统计数量
	needCount := false
	if len(withCount) > 0 && withCount[0] {
		needCount = true
	}

	if needCount && len(result) > 0 {
		// 批量获取每个分类的待办事项数量
		var categoryIds []interface{}
		for _, item := range result {
			categoryIds = append(categoryIds, item["id"])
		}

		// 查询每个分类的待办事项数量
		countQuery := vant2.DB("task").
			Where("user_id", userId).
			Where("is_delete", 0).
			Where("category_id", categoryIds).
			Fields("category_id, COUNT(*) as task_count").
			Group("category_id").
			Select()

		// 创建数量映射
		countMap := make(map[string]int)
		for _, countItem := range countQuery {
			categoryId := gconv.String(countItem["category_id"])
			taskCount := gconv.Int(countItem["task_count"])
			countMap[categoryId] = taskCount
		}

		// 重新构建结果，添加数量信息
		var newResult []g.Map
		for _, item := range result {
			categoryData := g.Map{
				"id":          item["id"],
				"name":        item["name"],
				"icon":        item["icon"],
				"color":       item["color"],
				"weight":      item["weight"],
				"time_create": item["time_create"],
			}

			categoryId := gconv.String(item["id"])
			if count, exists := countMap[categoryId]; exists {
				categoryData["task_count"] = count
			} else {
				categoryData["task_count"] = 0
			}

			newResult = append(newResult, categoryData)
		}

		return newResult
	}

	return result
}

// Get 获取单个分类信息
func (c *Category) Get(userId interface{}, categoryId interface{}) interface{} {
	return vant2.DB("task_category").
		Where("id", categoryId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("id,name,icon,color,weight,time_create").
		One()
}

// GetDefaultId 获取或创建默认分类ID
func (c *Category) GetDefaultId(userId interface{}) int {
	// 先检查是否已存在默认分类
	defaultCategory := vant2.DB("task_category").
		Where("user_id", userId).
		Where("name", "默认分类").
		Where("is_delete", 0).
		One()

	if len(defaultCategory) > 0 {
		return gconv.Int(defaultCategory["id"])
	}

	// 创建默认分类
	categoryId := vant2.DB("task_category").AddGetId(g.Map{
		"user_id":     userId,
		"name":        "默认分类",
		"icon":        "📝",
		"color":       "primary",
		"weight":      0,
		"time_create": vant2.Time(),
		"time_update": vant2.Time(),
		"is_delete":   0,
	})

	return categoryId
}

// Count 获取分类数量
func (c *Category) Count(userId interface{}) int {
	return vant2.DB("task_category").
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()
}
