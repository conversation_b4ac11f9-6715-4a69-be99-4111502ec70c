package taskservice

import (
	"fmt"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// AI 任务AI助手服务
type AI struct{}

// GetAllConfig 获取当前用户的所有任务配置选项
func (a *AI) GetAllConfig(userId interface{}) g.Map {
	// 获取用户的分类列表
	categoryService := &Category{}
	categories := categoryService.List(userId)

	// 获取用户的标签列表
	tagService := &Tag{}
	tags := tagService.List(userId)

	// 获取有效状态列表
	taskService := &Task{}
	statuses := taskService.GetValidStatuses()

	// 获取有效优先级列表
	priorities := taskService.GetValidPriorities()

	// 构建配置返回数据
	config := g.Map{
		"categories": categories, // 用户的分类列表
		"tags":       tags,       // 用户的标签列表
		"statuses":   statuses,   // 有效状态列表
		"priorities": priorities, // 有效优先级列表
	}

	return config
}

// GenerateAddTaskPrompt 生成专门的新增任务AI工具定义
func (a *AI) GenerateAddTaskPrompt(userId interface{}) []g.Map {
	// 获取当前配置
	config := a.GetAllConfig(userId)

	// 获取当前时间
	now := time.Now()
	dateNow := now.Format("2006-01-02 15:04:05")

	// 构建分类选项描述
	var categoryDesc strings.Builder
	categoryDesc.WriteString("可选分类：")

	// 调试输出
	g.Log().Debugf(nil, "AI.GenerateAddTaskPrompt - categories type: %T, value: %+v", config["categories"], config["categories"])

	// 修复数据类型转换
	if categoriesData := config["categories"]; categoriesData != nil {
		switch categories := categoriesData.(type) {
		case []interface{}:
			for i, cat := range categories {
				if catMap, ok := cat.(map[string]interface{}); ok {
					if i > 0 {
						categoryDesc.WriteString("、")
					}
					categoryDesc.WriteString(fmt.Sprintf("%s(ID:%v)", catMap["name"], catMap["id"]))
				}
			}
		case []map[string]interface{}:
			for i, catMap := range categories {
				if i > 0 {
					categoryDesc.WriteString("、")
				}
				categoryDesc.WriteString(fmt.Sprintf("%s(ID:%v)", catMap["name"], catMap["id"]))
			}
		default:
			// 尝试转换为切片
			slice := gconv.SliceAny(categories)
			for i, item := range slice {
				if catMap := gconv.Map(item); len(catMap) > 0 {
					if i > 0 {
						categoryDesc.WriteString("、")
					}
					categoryDesc.WriteString(fmt.Sprintf("%s(ID:%v)", catMap["name"], catMap["id"]))
				}
			}
		}
	}

	// 构建标签选项描述
	var tagDesc strings.Builder
	tagDesc.WriteString("可选标签：")

	// 调试输出
	g.Log().Debugf(nil, "AI.GenerateAddTaskPrompt - tags type: %T, value: %+v", config["tags"], config["tags"])

	// 修复数据类型转换
	if tagsData := config["tags"]; tagsData != nil {
		switch tags := tagsData.(type) {
		case []interface{}:
			for i, tag := range tags {
				if tagMap, ok := tag.(map[string]interface{}); ok {
					if i > 0 {
						tagDesc.WriteString("、")
					}
					tagDesc.WriteString(fmt.Sprintf("%s", tagMap["name"]))
				}
			}
		case []map[string]interface{}:
			for i, tagMap := range tags {
				if i > 0 {
					tagDesc.WriteString("、")
				}
				tagDesc.WriteString(fmt.Sprintf("%s", tagMap["name"]))
			}
		default:
			// 尝试转换为切片
			slice := gconv.SliceAny(tags)
			for i, item := range slice {
				if tagMap := gconv.Map(item); len(tagMap) > 0 {
					if i > 0 {
						tagDesc.WriteString("、")
					}
					tagDesc.WriteString(fmt.Sprintf("%s", tagMap["name"]))
				}
			}
		}
	}

	// 构建状态选项描述
	var statusDesc strings.Builder
	statusDesc.WriteString("可选状态：")
	if statuses, ok := config["statuses"].([]string); ok {
		for i, status := range statuses {
			if i > 0 {
				statusDesc.WriteString("、")
			}
			statusDesc.WriteString(status)
		}
	}

	// 构建优先级选项描述
	var priorityDesc strings.Builder
	priorityDesc.WriteString("可选优先级：")
	if priorities, ok := config["priorities"].([]string); ok {
		for i, priority := range priorities {
			if i > 0 {
				priorityDesc.WriteString("、")
			}
			priorityDesc.WriteString(priority)
		}
	}

	// 生成专门的新增任务工具定义
	tools := []g.Map{
		{
			"type": "function",
			"function": g.Map{
				"name":        "addTask",
				"description": fmt.Sprintf("解析用户输入，提取待办任务信息，返回结构化数据供用户确认。当前系统时间：%s。%s。%s。%s。%s", dateNow, categoryDesc.String(), tagDesc.String(), statusDesc.String(), priorityDesc.String()),
				"parameters": g.Map{
					"type": "object",
					"properties": g.Map{
						"content": g.Map{
							"type":        "string",
							"description": "待办事项的详细内容描述",
						},
						"category_id": g.Map{
							"type":        "integer",
							"description": "任务所属分类ID，从可选分类中选择对应的ID，必须指定一个有效的分类ID",
						},
						"deadline": g.Map{
							"type":        "string",
							"description": "任务完成截止日期时间，格式：yyyy-MM-dd HH:mm:ss，例如：2024-01-15 14:30:00。如果用户只说了日期没说时间，默认设置为当天18:00，注意，如果是早上，一般是早上8:00",
						},
						"status": g.Map{
							"type":        "string",
							"description": fmt.Sprintf("任务状态，可选值：%s。默认为：未开始", strings.Join(config["statuses"].([]string), "、")),
						},
						"priority": g.Map{
							"type":        "string",
							"description": fmt.Sprintf("任务优先级，可选值：%s。默认为：普通", strings.Join(config["priorities"].([]string), "、")),
						},
						"tags": g.Map{
							"type": "array",
							"items": g.Map{
								"type": "string",
							},
							"description": "任务标签数组，用于分类和标记任务。可以选择已有标签，也可以创建新标签",
						},
					},
					"required": []string{
						"content",
						"category_id",
						"status",
						"priority",
					},
				},
			},
		},
	}

	return tools
}

// GenerateSearchTasksPrompt 生成专门的搜索任务AI工具定义
func (a *AI) GenerateSearchTasksPrompt(userId interface{}) []g.Map {
	// 获取当前配置
	config := a.GetAllConfig(userId)

	// 生成专门的搜索任务工具定义
	tools := []g.Map{
		{
			"type": "function",
			"function": g.Map{
				"name":        "searchTasks",
				"description": "搜索和查询待办任务，用于查找特定的任务或获取任务列表",
				"parameters": g.Map{
					"type": "object",
					"properties": g.Map{
						"keyword": g.Map{
							"type":        "string",
							"description": "搜索关键词，用于匹配任务内容",
						},
						"status": g.Map{
							"type":        "string",
							"description": fmt.Sprintf("按状态筛选，可选值：%s", strings.Join(config["statuses"].([]string), "、")),
						},
						"priority": g.Map{
							"type":        "string",
							"description": fmt.Sprintf("按优先级筛选，可选值：%s", strings.Join(config["priorities"].([]string), "、")),
						},
						"category_id": g.Map{
							"type":        "integer",
							"description": "按分类ID筛选",
						},
						"tag_names": g.Map{
							"type": "array",
							"items": g.Map{
								"type": "string",
							},
							"description": "按标签名称筛选",
						},
						"limit": g.Map{
							"type":        "integer",
							"description": "返回结果数量限制，默认为10",
						},
					},
					"required": []string{},
				},
			},
		},
	}

	return tools
}

// GeneratePrompt 生成大模型对话的prompt（复用新增和搜索方法）
func (a *AI) GeneratePrompt(userId interface{}) []g.Map {
	// 复用调用新增和搜索的专门方法
	addTaskTools := a.GenerateAddTaskPrompt(userId)
	searchTasksTools := a.GenerateSearchTasksPrompt(userId)

	// 合并工具定义
	tools := make([]g.Map, 0)
	tools = append(tools, addTaskTools...)
	tools = append(tools, searchTasksTools...)

	return tools
}

// ProcessTaskByAI 处理AI生成的任务操作（只支持addTask和searchTasks）
func (a *AI) ProcessTaskByAI(userId interface{}, functionName string, params g.Map) (interface{}, string) {
	switch functionName {
	case "addTask":
		return a._processAddTask(userId, params)
	case "searchTasks":
		return a._processSearchTasks(userId, params)
	default:
		return false, "不支持的函数调用，当前只支持addTask和searchTasks"
	}
}

// _processAddTask 解析任务信息，返回结构化数据（不保存到数据库）
func (a *AI) _processAddTask(userId interface{}, params g.Map) (interface{}, string) {
	content := gconv.String(params["content"])
	if content == "" {
		return false, "任务内容不能为空"
	}

	// 处理分类ID
	categoryId := gconv.Int(params["category_id"])
	if categoryId <= 0 {
		// 使用默认分类
		categoryService := &Category{}
		categoryId = categoryService.GetDefaultId(userId)
	} else {
		// 验证分类是否存在且属于当前用户
		categoryExists := vant2.DB("task_category").
			Where("id", categoryId).
			Where("user_id", userId).
			Where("is_delete", 0).
			Count()
		if categoryExists == 0 {
			return false, "指定的分类不存在"
		}
	}

	// 处理截止时间
	var deadline int64
	deadlineStr := gconv.String(params["deadline"])
	if deadlineStr != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", deadlineStr); err == nil {
			deadline = t.Unix()
		}
	}

	// 处理状态和优先级
	status := gconv.String(params["status"])
	if status == "" {
		status = "未开始"
	}
	priority := gconv.String(params["priority"])
	if priority == "" {
		priority = "普通"
	}

	// 处理标签
	var tags []string
	if tagArray, ok := params["tags"].([]interface{}); ok {
		for _, tag := range tagArray {
			if tagStr := gconv.String(tag); tagStr != "" {
				tags = append(tags, tagStr)
			}
		}
	}

	// 处理置顶状态
	isCommond := gconv.Int(params["is_commond"])
	if isCommond != 0 && isCommond != 1 {
		isCommond = 0 // 默认不置顶
	}

	// 返回解析后的结构化数据，供前端确认和编辑
	result := g.Map{
		"content":     content,
		"category_id": categoryId,
		"deadline":    deadline,
		"status":      status,
		"priority":    priority,
		"tags":        tags,
		"is_commond":  isCommond,
	}

	return result, "任务信息解析成功"
}

// _processSearchTasks 处理搜索任务
func (a *AI) _processSearchTasks(userId interface{}, params g.Map) (interface{}, string) {
	keyword := gconv.String(params["keyword"])
	status := gconv.String(params["status"])
	priority := gconv.String(params["priority"])
	categoryId := gconv.Int(params["category_id"])
	limit := gconv.Int(params["limit"])
	if limit == 0 {
		limit = 10
	}

	// 构建查询
	db := vant2.DB("task t").
		LeftJoin("task_category c", "t.category_id = c.id").
		Where("t.user_id", userId).
		Where("t.is_delete", 0)

	// 关键词搜索
	if keyword != "" {
		db = db.Where("t.content LIKE ?", "%"+keyword+"%")
	}

	// 状态筛选
	if status != "" {
		db = db.Where("t.status", status)
	}

	// 优先级筛选
	if priority != "" {
		db = db.Where("t.priority", priority)
	}

	// 分类筛选
	if categoryId > 0 {
		db = db.Where("t.category_id", categoryId)
	}

	// 标签筛选
	if tagArray, ok := params["tag_names"].([]interface{}); ok && len(tagArray) > 0 {
		var tagNames []string
		for _, tag := range tagArray {
			if tagStr := gconv.String(tag); tagStr != "" {
				tagNames = append(tagNames, tagStr)
			}
		}
		if len(tagNames) > 0 {
			db = db.LeftJoin("task_tag_relation r", "t.id = r.task_id").
				LeftJoin("task_tag tag", "r.tag_id = tag.id").
				Where("tag.name", tagNames).
				Group("t.id")
		}
	}

	// 查询结果
	tasks, _ := db.Fields("t.id,t.content,t.status,t.priority,t.deadline,t.is_commond,t.time_create,c.name as category_name").
		Order("t.is_commond DESC, t.time_create DESC").
		Limit(limit).
		All()

	return tasks, "搜索完成"
}
