package taskservice

import (
	"encoding/json"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Task 待办事项服务
type Task struct{}

// 状态常量定义
const (
	StatusNotStarted = "未开始"
	StatusProcessing = "处理中"
	StatusInProgress = "进行中"
	StatusCompleted  = "已完成"
	StatusCancelled  = "已取消"
	StatusClosed     = "已关闭"
	StatusOnHold     = "搁置中"
)

// 优先级常量定义
const (
	PriorityUrgent = "紧急"
	PriorityHigh   = "高"
	PriorityMedium = "中等"
	PriorityNormal = "普通"
	PriorityLow    = "低"
)

// Save 保存待办事项（新增或编辑）
func (t *Task) Save(userId interface{}, taskId interface{}, categoryId int, content string, images []string, status string, priority string, deadline int64, tags []string, isCommond int) (interface{}, string) {
	content = strings.TrimSpace(content)

	if content == "" {
		return false, "待办事项内容不能为空"
	}

	// 验证分类是否存在
	if categoryId <= 0 {
		categoryService := &Category{}
		categoryId = categoryService.GetDefaultId(userId)
	} else {
		categoryExists := vant2.DB("task_category").
			Where("id", categoryId).
			Where("user_id", userId).
			Where("is_delete", 0).
			Count()
		if categoryExists == 0 {
			return false, "指定的分类不存在"
		}
	}

	// 验证状态
	validStatuses := []string{StatusNotStarted, StatusProcessing, StatusInProgress, StatusCompleted, StatusCancelled, StatusClosed, StatusOnHold}
	if status == "" {
		status = StatusNotStarted
	} else {
		isValidStatus := false
		for _, validStatus := range validStatuses {
			if status == validStatus {
				isValidStatus = true
				break
			}
		}
		if !isValidStatus {
			return false, "无效的状态值"
		}
	}

	// 验证优先级
	if priority != "" {
		validPriorities := []string{PriorityUrgent, PriorityHigh, PriorityMedium, PriorityNormal, PriorityLow}
		isValidPriority := false
		for _, validPriority := range validPriorities {
			if priority == validPriority {
				isValidPriority = true
				break
			}
		}
		if !isValidPriority {
			return false, "无效的优先级值"
		}
	}

	// 验证置顶参数
	if isCommond != 0 && isCommond != 1 {
		isCommond = 0 // 默认不置顶
	}

	// 处理图片数组
	var imagesJson interface{}
	if len(images) > 0 {
		imagesBytes, _ := json.Marshal(images)
		imagesJson = string(imagesBytes)
	}

	id := gconv.Int64(taskId)
	currentTime := vant2.Time()
	statusLogService := &StatusLog{}

	// 新增
	if id == 0 {
		newTaskId := vant2.DB("task").AddGetId(g.Map{
			"user_id":     userId,
			"category_id": categoryId,
			"content":     content,
			"images":      imagesJson,
			"status":      status,
			"priority":    priority,
			"deadline":    deadline,
			"is_commond":  isCommond,
			"time_create": currentTime,
			"time_update": currentTime,
			"is_delete":   0,
		})

		if newTaskId > 0 {
			// 使用新的状态日志服务记录创建任务
			statusLogService.ProcessTaskChanges(newTaskId, userId, g.Map{}, g.Map{
				"category_id": categoryId,
				"content":     content,
				"status":      status,
				"priority":    priority,
				"deadline":    deadline,
				"is_commond":  isCommond,
			})

			// 处理标签关联
			if len(tags) > 0 {
				t._updateTaskTags(newTaskId, userId, tags)
				// 记录标签变动（新建时oldTags为空）
				statusLogService.ProcessTagChanges(newTaskId, userId, []string{}, tags)
			}

			return newTaskId, "待办事项创建成功"
		}
		return false, "待办事项创建失败"
	}

	// 编辑 - 先获取原有数据
	oldTask := vant2.DB("task").
		Where("id", id).
		Where("user_id", userId).
		Where("is_delete", 0).
		One()

	if oldTask == nil {
		return false, "待办事项不存在"
	}

	// 获取原有标签
	oldTags := t._getTaskTagNames(id, userId)

	// 更新待办事项
	result := vant2.DB("task").
		Where("id", id).
		Where("user_id", userId).
		Data(g.Map{
			"category_id": categoryId,
			"content":     content,
			"images":      imagesJson,
			"status":      status,
			"priority":    priority,
			"deadline":    deadline,
			"is_commond":  isCommond,
			"time_update": currentTime,
		}).Update()

	if result {
		// 使用新的状态日志服务处理变动记录
		newData := g.Map{
			"category_id": categoryId,
			"content":     content,
			"status":      status,
			"priority":    priority,
			"deadline":    deadline,
			"is_commond":  isCommond,
		}
		// 转换oldTask为g.Map类型
		oldData := g.Map{}
		for k, v := range oldTask {
			oldData[k] = v
		}
		statusLogService.ProcessTaskChanges(id, userId, oldData, newData)

		// 处理标签关联
		t._updateTaskTags(id, userId, tags)

		// 记录标签变动
		statusLogService.ProcessTagChanges(id, userId, oldTags, tags)

		return true, "待办事项更新成功"
	}
	return false, "待办事项更新失败"
}

// Delete 删除待办事项（软删除）
func (t *Task) Delete(userId interface{}, taskId interface{}) bool {
	// 先验证任务是否属于当前用户
	taskExists := vant2.DB("task").
		Where("id", taskId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if taskExists == 0 {
		return false
	}

	// 验证通过后，直接删除标签关联
	vant2.DB("task_tag_relation").
		Where("task_id", taskId).
		Delete()

	// 软删除待办事项
	return vant2.DB("task").
		Where("id", taskId).
		Where("user_id", userId).
		Data(g.Map{
			"is_delete":   1,
			"time_update": vant2.Time(),
		}).Update()
}

// _updateTaskTags 更新待办事项的标签关联（性能优化版本）
func (t *Task) _updateTaskTags(taskId interface{}, userId interface{}, tags []string) {
	// 先验证任务是否属于当前用户
	taskExists := vant2.DB("task").
		Where("id", taskId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if taskExists == 0 {
		g.Log().Warningf(nil, "_updateTaskTags: 任务不存在或不属于当前用户, taskId=%v, userId=%v", taskId, userId)
		return
	}

	// 获取当前任务的标签名称列表
	currentTags := t._getTaskTagNames(taskId, userId)

	// 标准化新标签列表（去除空字符串并去重）
	newTags := make([]string, 0)
	tagMap := make(map[string]bool)
	for _, tag := range tags {
		tag = strings.TrimSpace(tag)
		if tag != "" && !tagMap[tag] {
			newTags = append(newTags, tag)
			tagMap[tag] = true
		}
	}

	// 检查标签是否有变化
	if t._compareTagLists(currentTags, newTags) {
		return
	}

	// 标签有变化，执行更新
	// 先删除原有关联
	vant2.DB("task_tag_relation").
		Where("task_id", taskId).
		Delete()

	// 添加新的关联
	if len(newTags) > 0 {
		// 批量获取或创建标签ID，减少数据库查询次数
		tagIds := t._getOrCreateTagIds(userId, newTags)

		// 批量插入关联关系
		currentTime := vant2.Time()
		for _, tagId := range tagIds {
			if tagId > 0 {
				vant2.DB("task_tag_relation").Data(g.Map{
					"task_id":     taskId,
					"tag_id":      tagId,
					"time_create": currentTime,
				}).Add()
			}
		}
	}
}

// _compareTagLists 比较两个标签列表是否相同
func (t *Task) _compareTagLists(oldTags, newTags []string) bool {
	if len(oldTags) != len(newTags) {
		return false
	}

	// 转换为map进行比较
	oldMap := make(map[string]bool)
	for _, tag := range oldTags {
		oldMap[tag] = true
	}

	for _, tag := range newTags {
		if !oldMap[tag] {
			return false
		}
	}

	return true
}

// _getOrCreateTagIds 批量获取或创建标签ID
func (t *Task) _getOrCreateTagIds(userId interface{}, tagNames []string) []int {
	if len(tagNames) == 0 {
		return []int{}
	}

	// 批量查询已存在的标签
	existingTags := vant2.DB("task_tag").
		Where("user_id", userId).
		Where("name", tagNames).
		Where("is_delete", 0).
		Fields("id, name").
		Select()

	// 建立名称到ID的映射
	existingMap := make(map[string]int)
	for _, tag := range existingTags {
		existingMap[gconv.String(tag["name"])] = gconv.Int(tag["id"])
	}

	// 收集结果
	tagIds := make([]int, 0, len(tagNames))
	currentTime := vant2.Time()

	for _, tagName := range tagNames {
		if tagId, exists := existingMap[tagName]; exists {
			// 标签已存在
			tagIds = append(tagIds, tagId)
		} else {
			// 创建新标签
			newTagId := vant2.DB("task_tag").AddGetId(g.Map{
				"user_id":     userId,
				"name":        tagName,
				"color":       "default",
				"time_create": currentTime,
				"time_update": currentTime,
				"is_delete":   0,
			})
			if newTagId > 0 {

				tagIds = append(tagIds, newTagId)
			}
		}
	}

	return tagIds
}

// _getTaskTagNames 获取任务的标签名称列表
func (t *Task) _getTaskTagNames(taskId interface{}, userId interface{}) []string {
	tags := vant2.DB("task_tag_relation r").
		LeftJoin("task_tag tag", "r.tag_id = tag.id").
		Where("r.task_id", taskId).
		Where("tag.user_id", userId).
		Where("tag.is_delete", 0).
		Fields("tag.name").
		Select()

	var tagNames []string
	for _, tag := range tags {
		if name := gconv.String(tag["name"]); name != "" {
			tagNames = append(tagNames, name)
		}
	}

	return tagNames
}

// GetValidStatuses 获取有效状态列表
func (t *Task) GetValidStatuses() []string {
	return []string{StatusNotStarted, StatusProcessing, StatusInProgress, StatusCompleted, StatusCancelled, StatusClosed, StatusOnHold}
}

// GetValidPriorities 获取有效优先级列表
func (t *Task) GetValidPriorities() []string {
	return []string{PriorityUrgent, PriorityHigh, PriorityMedium, PriorityNormal, PriorityLow}
}

// Get 获取待办事项详情
func (t *Task) Get(userId interface{}, taskId interface{}, isEdit int) interface{} {
	// 获取基础数据
	data := vant2.DB("task t").
		LeftJoin("task_category c", "t.category_id = c.id").
		Where("t.id", taskId).
		Where("t.user_id", userId).
		Where("t.is_delete", 0).
		Fields("t.id,t.category_id,t.content,t.images,t.status,t.priority,t.deadline,t.is_commond,t.time_create,t.time_update,c.name as category_name,c.icon as category_icon,c.color as category_color").
		One()

	if len(data) == 0 {
		return g.Map{}
	}

	// 获取关联的标签
	tags := vant2.DB("task_tag_relation r").
		LeftJoin("task_tag tag", "r.tag_id = tag.id").
		Where("r.task_id", taskId).
		Where("tag.user_id", userId).
		Where("tag.is_delete", 0).
		Fields("tag.id,tag.name,tag.color").
		Select()

	// 处理图片数组
	var images []string
	if data["images"] != nil && gconv.String(data["images"]) != "" {
		json.Unmarshal([]byte(gconv.String(data["images"])), &images)
	}

	// 获取任务历史记录
	var statusLogs []g.Map
	if isEdit == 0 {
		statusLogService := &StatusLog{}
		statusLogs = statusLogService.GetTaskLogsWithUserInfo(taskId, userId)
	}

	// 组装返回数据
	result := g.Map{
		"id":             data["id"],
		"category_id":    data["category_id"],
		"content":        data["content"],
		"images":         images,
		"status":         data["status"],
		"priority":       data["priority"],
		"deadline":       data["deadline"],
		"is_commond":     data["is_commond"],
		"time_create":    data["time_create"],
		"time_update":    data["time_update"],
		"category_name":  data["category_name"],
		"category_icon":  data["category_icon"],
		"category_color": data["category_color"],
		"tags":           tags,
		"status_logs":    statusLogs, // 添加任务历史记录

	}

	return result
}
