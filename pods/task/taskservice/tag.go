package taskservice

import (
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Tag 标签服务
type Tag struct{}

// Save 保存标签（新增或编辑）
func (t *Tag) Save(userId interface{}, tagId interface{}, name string, color string) (interface{}, string) {
	name = strings.TrimSpace(name)
	color = strings.TrimSpace(color)

	if name == "" {
		return false, "标签名称不能为空"
	}

	// 设置默认值
	if color == "" {
		color = "default"
	}

	id := gconv.Int(tagId)

	// 新增时检查标签数量限制
	if id == 0 {
		currentCount := vant2.DB("task_tag").
			Where("user_id", userId).
			Where("is_delete", 0).
			Count()

		if currentCount >= 100 {
			return false, "标签数量已达上限（最多100个标签）"
		}
	}

	// 检查标签名称是否重复
	db := vant2.DB("task_tag").
		Where("user_id", userId).
		Where("name", name).
		Where("is_delete", 0)

	// 如果是编辑，排除自己
	if id > 0 {
		db = db.Where("id !=", id)
	}

	exists := db.Count()
	if exists > 0 {
		return false, "标签名称已存在"
	}

	// 新增
	if id == 0 {
		tagId := vant2.DB("task_tag").AddGetId(g.Map{
			"user_id":     userId,
			"name":        name,
			"color":       color,
			"time_create": vant2.Time(),
			"time_update": vant2.Time(),
			"is_delete":   0,
		})
		if tagId > 0 {
			return tagId, "标签创建成功"
		}
		return false, "标签创建失败"
	}

	// 编辑
	result := vant2.DB("task_tag").
		Where("id", id).
		Where("user_id", userId).
		Data(g.Map{
			"name":        name,
			"color":       color,
			"time_update": vant2.Time(),
		}).Update()

	if result {
		return true, "标签更新成功"
	}
	return false, "标签更新失败"
}

// Delete 删除标签（软删除）
func (t *Tag) Delete(userId interface{}, tagId interface{}) bool {
	// 先删除标签关联关系
	vant2.DB("task_tag_relation").
		LeftJoin("task", "task_tag_relation.task_id = task.id").
		Where("task_tag_relation.tag_id", tagId).
		Where("task.user_id", userId).
		Delete()

	// 软删除标签
	return vant2.DB("task_tag").
		Where("id", tagId).
		Where("user_id", userId).
		Data(g.Map{
			"is_delete":   1,
			"time_update": vant2.Time(),
		}).Update()
}

// List 获取标签列表
func (t *Tag) List(userId interface{}, withCount ...bool) interface{} {
	result, _ := vant2.DB("task_tag").
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("id,name,color,time_create").
		Order("id DESC").
		All()

	// 如果需要统计数量
	needCount := false
	if len(withCount) > 0 && withCount[0] {
		needCount = true
	}

	if needCount && len(result) > 0 {
		// 批量获取每个标签的使用数量
		var tagIds []interface{}
		for _, item := range result {
			tagIds = append(tagIds, item["id"])
		}

		// 查询每个标签的使用数量
		countQuery := vant2.DB("task_tag_relation r").
			LeftJoin("task t", "r.task_id = t.id").
			Where("r.tag_id", tagIds).
			Where("t.user_id", userId).
			Where("t.is_delete", 0).
			Fields("r.tag_id, COUNT(*) as use_count").
			Group("r.tag_id").
			Select()

		// 创建数量映射
		countMap := make(map[string]int)
		for _, countItem := range countQuery {
			tagId := gconv.String(countItem["tag_id"])
			useCount := gconv.Int(countItem["use_count"])
			countMap[tagId] = useCount
		}

		// 重新构建结果，添加数量信息
		var newResult []g.Map
		for _, item := range result {
			tagData := g.Map{
				"id":          item["id"],
				"name":        item["name"],
				"color":       item["color"],
				"time_create": item["time_create"],
			}

			tagId := gconv.String(item["id"])
			if count, exists := countMap[tagId]; exists {
				tagData["use_count"] = count
			} else {
				tagData["use_count"] = 0
			}

			newResult = append(newResult, tagData)
		}

		return newResult
	}

	return result
}

// Get 获取单个标签信息
func (t *Tag) Get(userId interface{}, tagId interface{}) interface{} {
	return vant2.DB("task_tag").
		Where("id", tagId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("id,name,color,time_create").
		One()
}

// Search 搜索标签（用于输入时的自动补全）
func (t *Tag) Search(userId interface{}, keyword string, limit ...int) interface{} {
	keyword = strings.TrimSpace(keyword)
	if keyword == "" {
		return []g.Map{}
	}

	limitCount := 10
	if len(limit) > 0 && limit[0] > 0 {
		limitCount = limit[0]
	}

	_res, _ := vant2.DB("task_tag").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("name LIKE ?", "%"+keyword+"%").
		Fields("id,name,color").
		Order("name ASC").
		Limit(limitCount).
		All()
	return _res
}

// Count 获取标签数量
func (t *Tag) Count(userId interface{}) int {
	return vant2.DB("task_tag").
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()
}

// GetByIds 根据ID数组获取标签信息
func (t *Tag) GetByIds(userId interface{}, tagIds []interface{}) interface{} {
	if len(tagIds) == 0 {
		return []g.Map{}
	}

	return vant2.DB("task_tag").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("id", tagIds).
		Fields("id,name,color").
		Order("name ASC").
		Select()
}
