package task

import (
	"github.com/gogf/gf/v2/net/ghttp"
)

// InitRouter 初始化待办记事模块路由
func InitRouter(s *ghttp.Server) {
	// 待办记事模块
	s.Group("/task", func(group *ghttp.RouterGroup) {
		// 分类管理
		group.POST("/category/save", CategorySave)
		group.POST("/category/delete", CategoryDelete)
		group.GET("/category/list", CategoryList)
		group.GET("/category/get", CategoryGet)

		// 标签管理
		group.POST("/tag/save", TagSave)
		group.POST("/tag/delete", TagDelete)
		group.GET("/tag/list", TagList)
		group.GET("/tag/get", TagGet)
		group.GET("/tag/search", TagSearch)

		// 待办事项管理
		group.POST("/save", TaskSave)
		group.POST("/delete", TaskDelete)
		group.GET("/get", TaskGet)
		group.GET("/list", TaskList)
		group.GET("/statistics", TaskStatistics)
		group.GET("/statuses", TaskGetStatuses)
		group.GET("/priorities", TaskGetPriorities)

		// 任务回复
		group.POST("/reply", TaskAddReply)
		group.POST("/reply/delete", TaskDeleteReply)

		// 日期相关
		group.GET("/month/dates", TaskMonthDates)

		// AI助手相关
		group.GET("/ai/config", AIGetConfig)
		group.POST("/ai/extract", AIExtract)
		group.GET("/ai/prompt", AIGeneratePrompt)
		group.POST("/ai/process", AIProcessTask)
	})
}
