# 待办记事模块 API 文档

## 概述

待办记事模块提供完整的任务管理功能，包括分类管理、标签管理和待办事项管理。所有接口都需要用户认证，通过 `Authorization` 头部传递 token。

## 基础信息

- **模块路径**: `/task`
- **认证方式**: <PERSON><PERSON> (Header: `Authorization`)
- **响应格式**: JSON
- **字符编码**: UTF-8

## 分类管理 API

### 1. 保存分类

**接口地址**: `POST /task/category/save`

**功能说明**: 新增或编辑分类（通过id判断）

**请求参数**:
```json
{
  "id": 0,                    // 分类ID，0为新增，>0为编辑
  "name": "工作任务",          // 分类名称，必填
  "icon": "💼",               // emoji图标，可选，默认📝
  "color": "blue",            // 背景颜色，可选，默认primary
  "weight": 100               // 排序权重，可选，默认0，数值越大越靠前
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存分类",
  "data": 123                 // 新增时返回分类ID，编辑时返回true
}
```

### 2. 删除分类

**接口地址**: `POST /task/category/delete`

**功能说明**: 软删除分类（分类下有待办事项时不允许删除）

**请求参数**:
```json
{
  "id": 123                   // 分类ID，必填
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除分类",
  "data": true
}
```

### 3. 获取分类列表

**接口地址**: `GET /task/category/list`

**功能说明**: 获取用户的分类列表

**请求参数**:
```
withCount: true             // 是否统计每个分类下的待办事项数量，可选
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取分类列表成功",
  "data": [
    {
      "id": 1,
      "name": "工作任务",
      "icon": "💼",
      "color": "blue",
      "weight": 100,
      "timeCreate": 1640995200,
      "taskCount": 5            // 当withCount=true时返回
    }
  ]
}
```

### 4. 获取分类详情

**接口地址**: `GET /task/category/get`

**功能说明**: 获取单个分类的详细信息

**请求参数**:
```
id: 123                     // 分类ID，必填
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取分类详情成功",
  "data": {
    "id": 1,
    "name": "工作任务",
    "icon": "💼",
    "color": "blue",
    "weight": 100,
    "timeCreate": 1640995200
  }
}
```

## 标签管理 API

### 1. 保存标签

**接口地址**: `POST /task/tag/save`

**功能说明**: 新增或编辑标签（通过id判断）

**请求参数**:
```json
{
  "id": 0,                    // 标签ID，0为新增，>0为编辑
  "name": "重要",             // 标签名称，必填
  "color": "red"              // 背景颜色，可选，默认default
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存标签",
  "data": 456                 // 新增时返回标签ID，编辑时返回true
}
```

### 2. 删除标签

**接口地址**: `POST /task/tag/delete`

**功能说明**: 软删除标签（同时删除相关联的关系）

**请求参数**:
```json
{
  "id": 456                   // 标签ID，必填
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除标签",
  "data": true
}
```

### 3. 获取标签列表

**接口地址**: `GET /task/tag/list`

**功能说明**: 获取用户的标签列表

**请求参数**:
```
withCount: true             // 是否统计每个标签的使用次数，可选
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取标签列表成功",
  "data": [
    {
      "id": 1,
      "name": "重要",
      "color": "red",
      "timeCreate": 1640995200,
      "useCount": 3             // 当withCount=true时返回
    }
  ]
}
```

### 4. 获取标签详情

**接口地址**: `GET /task/tag/get`

**功能说明**: 获取单个标签的详细信息

**请求参数**:
```
id: 456                     // 标签ID，必填
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取标签详情成功",
  "data": {
    "id": 1,
    "name": "重要",
    "color": "red",
    "timeCreate": 1640995200
  }
}
```

### 5. 搜索标签

**接口地址**: `GET /task/tag/search`

**功能说明**: 根据关键词搜索标签（用于输入时的自动补全）

**请求参数**:
```
keyword: 重要               // 搜索关键词，必填
limit: 10                   // 返回数量限制，可选，默认10
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "搜索标签成功",
  "data": [
    {
      "id": 1,
      "name": "重要",
      "color": "red"
    }
  ]
}
```

## 待办事项管理 API

### 1. 保存待办事项

**接口地址**: `POST /task/save`

**功能说明**: 新增或编辑待办事项（通过id判断）

**请求参数**:
```json
{
  "id": 0,                    // 待办事项ID，0为新增，>0为编辑
  "categoryId": "1",          // 分类ID，可选，0或不传则使用默认分类
  "content": "完成项目文档",   // 待办事项内容，必填
  "images": "[]",             // 图片链接数组JSON字符串，可选
  "status": "已完成",         // 状态，直接传中文状态，可选，默认"未开始"
  "priority": "高",           // 优先级，直接传中文优先级，可选，允许为空
  "deadline": 1641081600,     // 截止时间戳，可选，0表示无截止时间
  "tags": ["重要", "紧急"]    // 标签名称数组，可选
}
```

**支持的状态值**:
- `未开始` - 默认状态
- `处理中` - 正在处理
- `进行中` - 正在进行
- `已完成` - 已完成
- `已取消` - 已取消
- `已关闭` - 已关闭
- `搁置中` - 暂时搁置

**支持的优先级值**:
- `紧急` - 最高优先级
- `高` - 高优先级
- `中等` - 中等优先级
- `普通` - 普通优先级
- `低` - 最低优先级

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "保存待办事项",
  "data": 789                 // 新增时返回待办事项ID，编辑时返回true
}
```

### 2. 删除待办事项

**接口地址**: `POST /task/delete`

**功能说明**: 软删除待办事项（同时删除标签关联关系）

**请求参数**:
```json
{
  "id": 789                   // 待办事项ID，必填
}
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "删除待办事项",
  "data": true
}
```

### 3. 获取有效状态列表

**接口地址**: `GET /task/statuses`

**功能说明**: 获取系统支持的所有状态

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取状态列表成功",
  "data": ["未开始", "处理中", "进行中", "已完成", "已取消", "已关闭", "搁置中"]
}
```

### 4. 获取有效优先级列表

**接口地址**: `GET /task/priorities`

**功能说明**: 获取系统支持的所有优先级

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取优先级列表成功",
  "data": ["紧急", "高", "中等", "普通", "低"]
}
```

### 5. 获取待办事项详情

**接口地址**: `GET /task/get`

**功能说明**: 获取单个待办事项的详细信息

**请求参数**:
```
id: 789                     // 待办事项ID，必填
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取待办事项详情成功",
  "data": {
    "id": 789,
    "categoryId": 1,
    "content": "完成项目文档",
    "images": ["http://example.com/1.jpg"],
    "status": "进行中",
    "priority": "高",
    "deadline": 1641081600,
    "timeCreate": 1640995200,
    "timeUpdate": 1641000000,
    "categoryName": "工作任务",
    "categoryIcon": "💼",
    "categoryColor": "blue",
    "tags": [
      {
        "id": 1,
        "name": "重要",
        "color": "red"
      },
      {
        "id": 2,
        "name": "紧急",
        "color": "orange"
      }
    ]
  }
}
```

### 6. 获取待办事项列表

**接口地址**: `GET /task/list`

**功能说明**: 获取待办事项列表，支持多种筛选条件和分页

**请求参数**:
```
page: 1                     // 页码，可选，默认1
pageSize: 20                // 每页数量，可选，默认20，最大100
categoryId: 1               // 分类ID筛选，可选，0或不传表示全部
tags: "[\"重要\",\"紧急\"]"  // 标签名称数组JSON字符串，可选，必须包含所有指定标签
statuses: "[\"进行中\",\"处理中\"]"  // 状态数组JSON字符串，可选，多选关系
priorities: "[\"高\",\"紧急\"]"     // 优先级数组JSON字符串，可选，多选关系
keyword: "项目"             // 关键词搜索，可选，搜索内容字段
dates: "[\"2025-7-9\",\"2025-7-10\"]"  // 特定日期数组JSON字符串，可选
startTime: 1641081600       // 开始时间戳，可选，10位时间戳
endTime: 1641168000         // 结束时间戳，可选，10位时间戳
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "success",
  "data": [
    {
      "id": 789,
      "categoryId": 1,
      "content": "完成项目文档",
      "images": ["http://example.com/1.jpg"],
      "status": "进行中",
      "priority": "高",
      "deadline": 1641081600,
      "timeCreate": 1640995200,
      "timeUpdate": 1641000000,
      "categoryName": "工作任务",
      "categoryIcon": "💼",
      "categoryColor": "blue",
      "tags": [
        {
          "id": 1,
          "name": "重要",
          "color": "red"
        }
      ]
    }
  ],
  "count": 25
}
```

### 7. 获取统计信息

**接口地址**: `GET /task/statistics`

**功能说明**: 获取待办事项的各种统计信息

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "获取统计信息成功",
  "data": {
    "totalCount": 50,
    "statusStats": [
      {
        "status": "未开始",
        "count": 10
      },
      {
        "status": "进行中",
        "count": 15
      },
      {
        "status": "已完成",
        "count": 20
      }
    ],
    "priorityStats": [
      {
        "priority": "紧急",
        "count": 5
      },
      {
        "priority": "高",
        "count": 10
      }
    ],
    "categoryStats": [
      {
        "categoryId": 1,
        "categoryName": "工作任务",
        "categoryIcon": "💼",
        "categoryColor": "blue",
        "count": 25
      }
    ],
    "todayDeadlineCount": 3,
    "overdueCount": 2
  }
}
```

### 8. 获取月份日期统计

**接口地址**: `GET /task/month/dates`

**功能说明**: 获取指定月份中有待办事项（按截止日期）的日期列表

**请求参数**:
- `month` (string, 必填): 月份，格式为 `YYYY-MM`，如 `2025-07`

**请求示例**:
```
GET /task/month/dates?month=2025-07
```

**响应示例**:
```json
{
  "back": 1,
  "code": 200,
  "msg": "查询成功",
  "data": [
    "2025-07-01",
    "2025-07-03",
    "2025-07-15",
    "2025-07-28"
  ]
}
```

**说明**:
- 只返回有设置截止日期的待办事项的日期
- 日期按升序排列
- 如果指定月份没有任何待办事项，返回空数组

## 🔗 完整路由列表

**分类管理**:
- `POST /task/category/save` - 保存分类
- `POST /task/category/delete` - 删除分类
- `GET /task/category/list` - 获取分类列表
- `GET /task/category/get` - 获取分类详情

**标签管理**:
- `POST /task/tag/save` - 保存标签
- `POST /task/tag/delete` - 删除标签
- `GET /task/tag/list` - 获取标签列表
- `GET /task/tag/get` - 获取标签详情
- `GET /task/tag/search` - 搜索标签

**待办事项管理**:
- `POST /task/save` - 保存待办事项
- `POST /task/delete` - 删除待办事项
- `GET /task/get` - 获取待办事项详情
- `GET /task/list` - 获取待办事项列表
- `GET /task/statistics` - 获取统计信息
- `GET /task/statuses` - 获取有效状态列表
- `GET /task/priorities` - 获取有效优先级列表
- `GET /task/month/dates` - 获取月份日期统计

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 401 | 未登录或登录已过期 |
| -1 | 参数错误 |
| -2 | 业务逻辑错误（如名称重复、数量超限等） |
| -3 | 数据格式错误 |
| -4 | 保存失败 |

## 注意事项

1. 所有接口都需要在请求头中携带 `Authorization: Bearer {token}`
2. 分类和标签名称在同一用户下不能重复
3. 分类最多50个，标签最多100个
4. 删除分类时，如果分类下有待办事项则不允许删除
5. **重要**：标签参数使用标签名称数组，支持直接传递数组格式
6. **重要**：状态和优先级直接传递中文字符串，不需要转换
7. 状态和优先级筛选支持多选，使用数组格式传递
8. 支持特定日期数组筛选和时间戳范围筛选
9. 图片参数需要以JSON数组字符串格式传递
10. 时间戳使用10位Unix时间戳格式
11. 状态变更会自动记录到状态跟踪表中
12. 系统支持的状态：未开始、处理中、进行中、已完成、已取消、已关闭、搁置中
13. 系统支持的优先级：紧急、高、中等、普通、低
