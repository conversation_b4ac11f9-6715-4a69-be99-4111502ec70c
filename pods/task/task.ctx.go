package task

import (
	"assistant/pods/base"
	"assistant/pods/task/taskservice"
	"encoding/json"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// TaskSave 保存待办事项（新增或编辑）
func TaskSave(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	categoryId := gconv.Int(_base.Input.Value("categoryId"))
	content := gconv.String(_base.Input.Value("content"))
	imagesStr := gconv.String(_base.Input.Value("images"))
	status := gconv.String(_base.Input.Value("status"))
	priority := gconv.String(_base.Input.Value("priority"))
	deadline := gconv.Int64(_base.Input.Value("deadline"))
	isCommond := gconv.Int(_base.Input.Value("isCommond"))
	if content == "" {
		_base.Back.ApiError(-1, "参数错误：待办事项内容不能为空")
		return
	}

	// 处理图片数组
	var images []string
	if imagesStr != "" {
		err := json.Unmarshal([]byte(imagesStr), &images)
		if err != nil {
			_base.Back.ApiError(-2, "参数错误：图片数据格式不正确")
			return
		}
	}

	// 处理标签数组（直接从请求中获取数组）
	var tags []string
	if tagsValue := _base.Input.Value("tags"); tagsValue != nil {
		// 如果是数组类型，直接转换
		if tagsArray, ok := tagsValue.([]interface{}); ok {
			for _, tag := range tagsArray {
				if tagStr := gconv.String(tag); tagStr != "" {
					tags = append(tags, tagStr)
				}
			}
		} else {
			// 如果是字符串，尝试JSON解析
			tagsStr := gconv.String(tagsValue)
			if tagsStr != "" {
				err := json.Unmarshal([]byte(tagsStr), &tags)
				if err != nil {
					_base.Back.ApiError(-3, "参数错误：标签数据格式不正确")
					return
				}
			}
		}
	}

	taskService := &taskservice.Task{}
	result, msg := taskService.Save(_base.UserId, _base.Id, categoryId, content, images, status, priority, deadline, tags, isCommond)

	if result == false {
		_base.Back.ApiError(-4, msg)
		return
	}
	_base.Back.ApiSuccess("保存待办事项成功")
}

// TaskDelete 删除待办事项
func TaskDelete(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：待办事项ID不能为空")
		return
	}

	taskService := &taskservice.Task{}
	result := taskService.Delete(_base.UserId, _base.Id)
	_base.Back.ApiBack("删除待办事项", result)
}

// TaskGetStatuses 获取有效状态列表
func TaskGetStatuses(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	taskService := &taskservice.Task{}
	statuses := taskService.GetValidStatuses()
	_base.Back.ApiSuccess("获取状态列表成功", statuses)
}

// TaskGetPriorities 获取有效优先级列表
func TaskGetPriorities(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	taskService := &taskservice.Task{}
	priorities := taskService.GetValidPriorities()
	_base.Back.ApiSuccess("获取优先级列表成功", priorities)
}

// TaskGet 获取待办事项详情
func TaskGet(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：待办事项ID不能为空")
		return
	}

	isEdit := gconv.Int(_base.Input.Value("isEdit"))

	taskService := &taskservice.Task{}
	data := taskService.Get(_base.UserId, _base.Id, isEdit)
	if len(data.(g.Map)) == 0 {
		_base.Back.ApiError(-2, "待办事项不存在")
		return
	}

	_base.Back.ApiSuccess("获取待办事项详情成功", _base.Back.ToSnake(data))
}

// TaskAddReply 添加任务回复
func TaskAddReply(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：任务ID不能为空")
		return
	}

	content := gconv.String(_base.Input.Value("content"))
	topId := gconv.Int64(_base.Input.Value("topId"))

	if content == "" {
		_base.Back.ApiError(-2, "参数错误：回复内容不能为空")
		return
	}

	statusLogService := &taskservice.StatusLog{}
	success, msg := statusLogService.AddReply(_base.Id, _base.UserId, content, topId)

	if !success {
		_base.Back.ApiError(-3, msg)
		return
	}

	_base.Back.ApiSuccess(msg)
}

// TaskDeleteReply 删除任务回复
func TaskDeleteReply(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	replyId := gconv.Int64(_base.Id)
	if replyId == 0 {
		_base.Back.ApiError(-1, "参数错误：回复ID不能为空")
		return
	}

	statusLogService := &taskservice.StatusLog{}
	success, msg := statusLogService.DeleteReply(replyId, _base.UserId)

	if !success {
		_base.Back.ApiError(-2, msg)
		return
	}

	_base.Back.ApiSuccess(msg)
}
