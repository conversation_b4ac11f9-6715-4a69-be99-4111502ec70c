package task

import (
	"assistant/pods/base"
	"assistant/pods/task/taskservice"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// TagSave 保存标签（新增或编辑）
func TagSave(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	name := _base.Input.Value("name")
	color := gconv.String(_base.Input.Value("color"))

	if name == "" {
		_base.Back.ApiError(-1, "参数错误：标签名称不能为空")
		return
	}

	tagService := &taskservice.Tag{}
	result, msg := tagService.Save(_base.UserId, _base.Id, gconv.String(name), color)
	if result == false {
		_base.Back.ApiError(-2, msg)
		return
	}
	_base.Back.ApiBack("保存标签", result)
}

// TagDelete 删除标签
func TagDelete(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：标签ID不能为空")
		return
	}

	tagService := &taskservice.Tag{}
	result := tagService.Delete(_base.UserId, _base.Id)
	_base.Back.ApiBack("删除标签", result)
}

// TagList 获取标签列表
func TagList(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 检查是否需要统计数量
	withCount := gconv.Bool(_base.Input.Value("withCount"))

	tagService := &taskservice.Tag{}
	list := tagService.List(_base.UserId, withCount)
	_base.Back.ApiSuccess("获取标签列表成功", _base.Back.ToSnake(list))
}

// TagGet 获取标签详情
func TagGet(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误：标签ID不能为空")
		return
	}

	tagService := &taskservice.Tag{}
	data := tagService.Get(_base.UserId, _base.Id)
	if data == nil {
		_base.Back.ApiError(-2, "标签不存在")
		return
	}

	_base.Back.ApiSuccess("获取标签详情成功", _base.Back.ToSnake(data))
}

// TagSearch 搜索标签（用于输入时的自动补全）
func TagSearch(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	keyword := gconv.String(_base.Input.Value("keyword"))
	limit := gconv.Int(_base.Input.Value("limit"))

	if keyword == "" {
		_base.Back.ApiError(-1, "参数错误：搜索关键词不能为空")
		return
	}

	tagService := &taskservice.Tag{}
	list := tagService.Search(_base.UserId, keyword, limit)
	_base.Back.ApiSuccess("搜索标签成功", _base.Back.ToSnake(list))
}
