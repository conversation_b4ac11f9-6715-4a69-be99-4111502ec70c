package accountingservice

import (
	"encoding/json"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Record 记账记录服务
type Record struct{}

// GetDetail 获取单笔记录详情
func (r *Record) GetDetail(id int64, userId int64) interface{} {
	if id <= 0 {
		return nil
	}

	// 查询记录详情，包含分类信息
	record := vant2.DB("accounting_records r").
		LeftJoin("accounting_categories c", "r.category_id = c.id").
		Where("r.id", id).
		Where("r.user_id", userId).
		Where("r.is_delete", 0).
		Fields("r.*, c.name as category_name, c.emoji as category_emoji, c.color as category_color").
		One()

	if record == nil {
		return nil
	}

	// 转换为map格式
	recordMap := g.Map{}
	for key, value := range record {
		recordMap[key] = value
	}

	// 添加标签信息
	tags := r._getRecordTags(id, userId)
	recordMap["tags"] = tags

	// 处理图片字段
	if record["images"] != nil && gconv.String(record["images"]) != "" {
		var images []string
		json.Unmarshal([]byte(gconv.String(record["images"])), &images)
		recordMap["images"] = images
	} else {
		recordMap["images"] = []string{}
	}

	// 添加分类详细信息
	recordMap["category"] = g.Map{
		"id":    record["category_id"],
		"name":  record["category_name"],
		"emoji": record["category_emoji"],
		"color": record["category_color"],
	}

	return recordMap
}

// GetList 获取记账记录列表
func (r *Record) GetList(params g.Map, userId int64) (interface{}, int) {
	db := vant2.DB("accounting_records r").
		LeftJoin("accounting_categories c", "r.category_id = c.id").
		Where("r.user_id", userId).
		Where("r.is_delete", 0)

	// 按类型筛选
	if params["type"] != nil && gconv.String(params["type"]) != "" {
		typeStr := gconv.String(params["type"])
		if typeStr == "expense" {
			db = db.Where("r.type", 1) // 支出
		} else if typeStr == "income" {
			db = db.Where("r.type", 2) // 收入
		}
		// 如果是其他值或"all"，不添加类型条件
	}

	// 按分类筛选
	if params["categoryId"] != nil && gconv.Int(params["categoryId"]) > 0 {
		db = db.Where("r.category_id", params["categoryId"])
	}

	// 按时间范围筛选
	if params["startTime"] != nil && gconv.Int(params["startTime"]) > 0 {
		db = db.Where("r.record_time >=", params["startTime"])
	}
	if params["endTime"] != nil && gconv.Int(params["endTime"]) > 0 {
		db = db.Where("r.record_time <=", params["endTime"])
	}

	// 按标签筛选 - 重要功能！只要包含任何一个标签都显示
	if params["tags"] != nil {
		// 直接使用数组，不需要转换
		if tagsArray, ok := params["tags"].([]interface{}); ok && len(tagsArray) > 0 {
			// 转换为字符串数组
			var tagNames []string
			for _, tag := range tagsArray {
				tagNames = append(tagNames, gconv.String(tag))
			}

			// 查找包含指定标签的记录ID
			tagRecordIds := vant2.DB("accounting_record_tags rt").
				LeftJoin("accounting_tags t", "rt.tag_id = t.id").
				Where("t.name", tagNames).
				Where("t.user_id", userId).
				Where("t.is_delete", 0).
				Fields("DISTINCT rt.record_id").
				Select()

			// 提取记录ID
			var recordIds []int64
			for _, record := range tagRecordIds {
				recordIds = append(recordIds, gconv.Int64(record["record_id"]))
			}

			if len(recordIds) > 0 {
				db = db.Where("r.id", recordIds)
			} else {
				// 如果没有找到匹配的记录，返回空结果
				db = db.Where("r.id", -1)
			}
		}
	}

	// 分页 - 修复参数名
	pageNum := gconv.Int(params["page"]) // 修复：使用 "page" 而不是 "pageNum"
	pageSize := gconv.Int(params["pageSize"])
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 获取总数 - 修复：为COUNT查询单独设置字段
	total, _ := db.Clone().Count()

	// 获取数据 - 修复：为数据查询设置字段和排序
	result, _ := db.Fields("r.*, c.name as category_name, c.emoji as category_emoji, c.color as category_color").
		Order("r.record_time DESC, r.id DESC").
		Paginate(pageNum, pageSize)

	// 批量获取所有记录的标签信息 - 修复N+1查询问题
	var recordIds []int64
	for _, record := range result {
		recordIds = append(recordIds, gconv.Int64(record["id"]))
	}

	// 一次性查询所有标签
	tagsMap := r._getRecordTagsBatch(recordIds, userId)

	// 处理每条记录的额外信息
	var processedResult []g.Map
	for _, record := range result {
		recordMap := g.Map{}

		// 复制所有字段
		for key, value := range record {
			recordMap[key] = value
		}

		// 添加标签信息
		recordId := gconv.Int64(record["id"])
		if tags, exists := tagsMap[recordId]; exists {
			recordMap["tags"] = tags
		} else {
			recordMap["tags"] = []g.Map{}
		}

		// 处理图片字段
		if record["images"] != nil && gconv.String(record["images"]) != "" {
			var images []string
			json.Unmarshal([]byte(gconv.String(record["images"])), &images)
			recordMap["images"] = images
		} else {
			recordMap["images"] = []string{}
		}

		processedResult = append(processedResult, recordMap)
	}

	return processedResult, total
}

// Save 保存记账记录（新增/编辑）
func (r *Record) Save(data g.Map, userId int64) bool {
	// 验证必填参数
	if gconv.Int(data["type"]) <= 0 {
		return false
	}
	if gconv.Int(data["categoryId"]) <= 0 {
		return false
	}
	if gconv.Float64(data["amount"]) <= 0 {
		return false
	}
	if gconv.Int(data["recordTime"]) <= 0 {
		return false
	}

	recordType := gconv.Int(data["type"])
	categoryId := gconv.Int(data["categoryId"])
	amount := gconv.Float64(data["amount"])
	description := gconv.String(data["description"])
	recordTime := gconv.Int(data["recordTime"])
	id := gconv.Int64(data["id"])

	// 处理图片数组
	var imagesJson string
	if data["images"] != nil {
		if images, ok := data["images"].([]interface{}); ok {
			var imageStrings []string
			for _, img := range images {
				if imgStr := gconv.String(img); imgStr != "" {
					imageStrings = append(imageStrings, imgStr)
				}
			}
			if len(imageStrings) > 0 {
				imageBytes, _ := json.Marshal(imageStrings)
				imagesJson = string(imageBytes)
			}
		}
	}

	// 验证分类是否存在且类型匹配
	categoryService := &Category{}
	category := categoryService.GetById(int64(categoryId), userId)
	if category == nil {
		return false
	}
	// 将gdb.Record转换为map[string]interface{}
	categoryData := category.(gdb.Record).Map()
	if gconv.Int(categoryData["type"]) != recordType {
		return false // 分类类型不匹配
	}

	saveData := g.Map{
		"type":        recordType,
		"category_id": categoryId,
		"amount":      amount,
		"description": description,
		"images":      imagesJson,
		"record_time": recordTime,
	}

	var recordId int64
	if id > 0 {
		// 编辑 - 验证记录是否属于当前用户
		existRecord := vant2.DB("accounting_records").
			Where("id", id).
			Where("user_id", userId).
			Where("is_delete", 0).
			One()
		if existRecord == nil {
			return false
		}

		saveData["update_time"] = vant2.Time()
		success := vant2.DB("accounting_records").Where("id", id).Data(saveData).Update()
		if !success {
			return false
		}
		recordId = id
	} else {
		// 新增
		saveData["user_id"] = userId
		saveData["create_time"] = vant2.Time()
		saveData["update_time"] = vant2.Time()
		newId := vant2.DB("accounting_records").AddGetId(saveData)
		if newId <= 0 {
			return false
		}
		recordId = int64(newId)
	}

	// 处理标签关联
	if data["tags"] != nil {
		r._saveRecordTags(recordId, data["tags"], recordType, userId)
	}

	return true
}

// Delete 删除记账记录
func (r *Record) Delete(id int64, userId int64) bool {
	if id <= 0 {
		return false
	}

	// 验证记录是否属于当前用户
	existRecord := vant2.DB("accounting_records").
		Where("id", id).
		Where("user_id", userId).
		Where("is_delete", 0).
		One()
	if existRecord == nil {
		return false
	}

	// 软删除记录
	success := vant2.DB("accounting_records").
		Where("id", id).
		Data(g.Map{
			"is_delete":   1,
			"update_time": vant2.Time(),
		}).
		Update()

	if success {
		// 删除标签关联
		vant2.DB("accounting_record_tags").Where("record_id", id).Remove()
	}

	return success
}

// GetStatistics 获取统计数据
func (r *Record) GetStatistics(params g.Map, userId int64) g.Map {
	startTime := gconv.Int(params["startTime"])
	endTime := gconv.Int(params["endTime"])

	// 按类型统计
	stats := vant2.DB("accounting_records").
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("type, SUM(amount) as total_amount, COUNT(*) as count")

	if startTime > 0 {
		stats = stats.Where("record_time >=", startTime)
	}
	if endTime > 0 {
		stats = stats.Where("record_time <=", endTime)
	}

	typeStats := stats.Group("type").Select()

	result := g.Map{
		"expense":   0.0, // 支出
		"income":    0.0, // 收入
		"noCount":   0.0, // 不计入收入
		"balance":   0.0, // 结余
		"typeStats": typeStats,
		"startTime": startTime,
		"endTime":   endTime,
	}

	for _, stat := range typeStats {
		amount := gconv.Float64(stat["total_amount"])
		recordType := gconv.Int(stat["type"])

		switch recordType {
		case 1: // 支出
			result["expense"] = amount
		case 2: // 入账
			result["income"] = amount
		case 3: // 不计入收入
			result["noCount"] = amount
		}
	}

	// 计算结余：收入 - 支出
	result["balance"] = gconv.Float64(result["income"]) - gconv.Float64(result["expense"])

	return result
}

// _getRecordTags 获取记录的标签信息
func (r *Record) _getRecordTags(recordId int64, userId int64) interface{} {
	if recordId <= 0 {
		return []g.Map{}
	}

	return vant2.DB("accounting_record_tags rt").
		LeftJoin("accounting_tags t", "rt.tag_id = t.id").
		Where("rt.record_id", recordId).
		Where("t.user_id", userId).
		Where("t.is_delete", 0).
		Fields("t.id, t.name, t.color").
		Select()
}

// _getRecordTagsBatch 批量获取多个记录的标签信息
func (r *Record) _getRecordTagsBatch(recordIds []int64, userId int64) map[int64][]g.Map {
	if len(recordIds) == 0 {
		return make(map[int64][]g.Map)
	}

	// 一次性查询所有标签
	tags := vant2.DB("accounting_record_tags rt").
		LeftJoin("accounting_tags t", "rt.tag_id = t.id").
		Where("rt.record_id", recordIds).
		Where("t.user_id", userId).
		Where("t.is_delete", 0).
		Fields("rt.record_id, t.id, t.name, t.color").
		Select()

	// 按record_id分组
	tagsMap := make(map[int64][]g.Map)
	for _, tag := range tags {
		recordId := gconv.Int64(tag["record_id"])
		if tagsMap[recordId] == nil {
			tagsMap[recordId] = []g.Map{}
		}
		tagsMap[recordId] = append(tagsMap[recordId], g.Map{
			"id":    tag["id"],
			"name":  tag["name"],
			"color": tag["color"],
		})
	}

	return tagsMap
}

// _saveRecordTags 保存记录标签关联
func (r *Record) _saveRecordTags(recordId int64, tags interface{}, recordType int, userId int64) {
	if recordId <= 0 {
		return
	}

	// 先删除原有的标签关联
	vant2.DB("accounting_record_tags").Where("record_id", recordId).Delete()

	// 处理标签数据
	var tagNames []string
	if tagsArray, ok := tags.([]interface{}); ok {
		for _, tag := range tagsArray {
			if tagName := gconv.String(tag); tagName != "" {
				tagNames = append(tagNames, strings.TrimSpace(tagName))
			}
		}
	} else if tagsStr := gconv.String(tags); tagsStr != "" {
		// 支持逗号分隔的字符串
		for _, tagName := range strings.Split(tagsStr, ",") {
			if tagName = strings.TrimSpace(tagName); tagName != "" {
				tagNames = append(tagNames, tagName)
			}
		}
	}

	if len(tagNames) == 0 {
		return
	}

	// 创建或获取标签ID
	tagService := &Tag{}
	tagIds := tagService.CreateByNames(tagNames, recordType, userId)

	// 创建关联记录
	for _, tagId := range tagIds {
		vant2.DB("accounting_record_tags").Add(g.Map{
			"record_id":   recordId,
			"tag_id":      tagId,
			"create_time": vant2.Time(),
		})
	}
}
