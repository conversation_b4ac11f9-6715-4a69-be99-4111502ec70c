package accountingservice

import (
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Tag 标签服务
type Tag struct{}

// GetList 获取标签列表
func (t *Tag) GetList(tagType interface{}, userId int64) interface{} {
	db := vant2.DB("accounting_tags").
		Where("user_id", userId).
		Order("id ASC")

	// 如果传入了类型参数，则按类型筛选
	if tagType != nil && gconv.String(tagType) != "" {
		db = db.Where("type", tagType)
	}

	return db.Select()
}

// Save 保存标签（新增/编辑）
func (t *Tag) Save(data g.Map, userId int64) bool {
	// 验证必填参数
	if gconv.String(data["name"]) == "" {
		return false
	}
	if gconv.Int(data["type"]) <= 0 {
		return false
	}

	name := gconv.String(data["name"])
	tagType := gconv.Int(data["type"])
	id := gconv.Int64(data["id"])

	// 检查同用户同类型下标签名称是否重复
	existQuery := vant2.DB("accounting_tags").
		Where("user_id", userId).
		Where("name", name).
		Where("type", tagType)

	if id > 0 {
		existQuery = existQuery.Where("id !=", id)
	}

	if existQuery.Count() > 0 {
		return false // 标签名称重复
	}

	saveData := g.Map{
		"name":  name,
		"type":  tagType,
		"color": gconv.String(data["color"]),
	}

	if id > 0 {
		// 编辑 - 验证标签是否属于当前用户
		existRecord := vant2.DB("accounting_tags").
			Where("id", id).
			Where("user_id", userId).
			One()
		if existRecord == nil {
			return false
		}

		saveData["update_time"] = vant2.Time()
		return vant2.DB("accounting_tags").Where("id", id).Data(saveData).Update()
	} else {
		// 新增
		saveData["user_id"] = userId
		saveData["create_time"] = vant2.Time()
		saveData["update_time"] = vant2.Time()
		return vant2.DB("accounting_tags").Add(saveData)
	}
}

// Delete 删除标签（硬删除）
func (t *Tag) Delete(id int64, userId int64) bool {
	if id <= 0 {
		return false
	}

	// 验证标签是否属于当前用户
	existRecord := vant2.DB("accounting_tags").
		Where("id", id).
		Where("user_id", userId).
		One()
	if existRecord == nil {
		return false
	}

	// 检查是否有记账记录使用此标签
	recordCount := vant2.DB("accounting_record_tags rt").
		LeftJoin("accounting_records r", "rt.record_id = r.id").
		Where("rt.tag_id", id).
		Where("r.user_id", userId).
		Where("r.is_delete", 0).
		Count()

	if recordCount > 0 {
		return false // 有记账记录使用此标签，不能删除
	}

	// 先删除标签关联记录
	vant2.DB("accounting_record_tags").Where("tag_id", id).Remove()

	// 硬删除标签 - 使用原生SQL删除
	result, _ := vant2.DB("accounting_tags").
		Where("id", id).
		Where("user_id", userId).
		Delete()
	rows, _ := result.RowsAffected()
	return rows > 0
}

// GetById 根据ID获取标签信息
func (t *Tag) GetById(id int64, userId int64) interface{} {
	if id <= 0 {
		return nil
	}

	return vant2.DB("accounting_tags").
		Where("id", id).
		Where("user_id", userId).
		Where("is_delete", 0).
		One()
}

// GetByNames 根据标签名称数组获取标签ID数组
func (t *Tag) GetByNames(names []string, tagType int, userId int64) []int64 {
	if len(names) == 0 {
		return []int64{}
	}

	tags := vant2.DB("accounting_tags").
		Where("user_id", userId).
		Where("name IN(?)", names).
		Where("type", tagType).
		Where("is_delete", 0).
		Fields("id").
		Select()

	var tagIds []int64
	for _, tag := range tags {
		tagIds = append(tagIds, gconv.Int64(tag["id"]))
	}

	return tagIds
}

// CreateByNames 根据标签名称数组创建不存在的标签，返回所有标签ID
func (t *Tag) CreateByNames(names []string, tagType int, userId int64) []int64 {
	if len(names) == 0 {
		return []int64{}
	}

	var allTagIds []int64

	for _, name := range names {
		if name == "" {
			continue
		}

		// 检查标签是否存在
		existTag := vant2.DB("accounting_tags").
			Where("user_id", userId).
			Where("name", name).
			Where("type", tagType).
			Where("is_delete", 0).
			Fields("id").
			One()

		if existTag != nil {
			// 标签已存在
			allTagIds = append(allTagIds, gconv.Int64(existTag["id"]))
		} else {
			// 创建新标签
			tagId := vant2.DB("accounting_tags").AddGetId(g.Map{
				"user_id":     userId,
				"name":        name,
				"type":        tagType,
				"create_time": vant2.Time(),
				"update_time": vant2.Time(),
			})
			if tagId > 0 {
				allTagIds = append(allTagIds, int64(tagId))
			}
		}
	}

	return allTagIds
}
