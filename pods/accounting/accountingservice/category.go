package accountingservice

import (
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Category 分类服务
type Category struct{}

// GetList 获取分类列表
func (c *Category) GetList(categoryType interface{}, userId int64) interface{} {
	// 检查用户是否有分类，如果没有则初始化
	existCount := vant2.DB("accounting_categories").
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if existCount == 0 {
		c._initUserCategories(userId)
	}

	db := vant2.DB("accounting_categories").
		Where("user_id", userId).
		Where("is_delete", 0).
		Order("weight ASC, id ASC")

	// 如果传入了类型参数，则按类型筛选
	if categoryType != nil && gconv.String(categoryType) != "" {
		db = db.Where("type", categoryType)
	}

	return db.Select()
}

// Save 保存分类（新增/编辑）
func (c *Category) Save(data g.Map, userId int64) bool {
	// 验证必填参数
	if gconv.String(data["name"]) == "" {
		return false
	}
	if gconv.Int(data["type"]) <= 0 {
		return false
	}

	name := gconv.String(data["name"])
	categoryType := gconv.Int(data["type"])
	id := gconv.Int64(data["id"])

	// 如果是编辑，检查是否为系统分类
	if id > 0 {
		existRecord := vant2.DB("accounting_categories").
			Where("id", id).
			Where("user_id", userId).
			Where("is_delete", 0).
			One()
		if existRecord == nil {
			return false
		}

		// 系统分类不允许修改
		if gconv.Int(existRecord["is_system"]) == 1 {
			return false
		}
	}

	// 检查同用户同类型下分类名称是否重复
	existQuery := vant2.DB("accounting_categories").
		Where("user_id", userId).
		Where("name", name).
		Where("type", categoryType).
		Where("is_delete", 0)

	if id > 0 {
		existQuery = existQuery.Where("id !=", id)
	}

	if existQuery.Count() > 0 {
		return false // 分类名称重复
	}

	saveData := g.Map{
		"name":   name,
		"type":   categoryType,
		"color":  gconv.String(data["color"]),
		"emoji":  gconv.String(data["emoji"]),
		"weight": gconv.Int(data["weight"]),
	}

	if id > 0 {
		// 编辑
		saveData["update_time"] = vant2.Time()
		return vant2.DB("accounting_categories").Where("id", id).Data(saveData).Update()
	} else {
		// 新增用户自定义分类
		saveData["user_id"] = userId
		saveData["is_system"] = 0
		saveData["create_time"] = vant2.Time()
		saveData["update_time"] = vant2.Time()
		return vant2.DB("accounting_categories").Add(saveData)
	}
}

// Delete 删除分类
func (c *Category) Delete(id int64, userId int64) bool {
	if id <= 0 {
		return false
	}

	// 验证分类是否属于当前用户
	existRecord := vant2.DB("accounting_categories").
		Where("id", id).
		Where("user_id", userId).
		Where("is_delete", 0).
		One()
	if existRecord == nil {
		return false
	}

	// 系统分类不允许删除
	if gconv.Int(existRecord["is_system"]) == 1 {
		return false
	}

	// 检查是否有记账记录使用此分类
	recordCount := vant2.DB("accounting_records").
		Where("category_id", id).
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()

	if recordCount > 0 {
		return false // 有记账记录使用此分类，不能删除
	}

	// 软删除
	return vant2.DB("accounting_categories").
		Where("id", id).
		Where("user_id", userId).
		Data(g.Map{
			"is_delete":   1,
			"update_time": vant2.Time(),
		}).
		Update()
}

// GetById 根据ID获取分类信息
func (c *Category) GetById(id int64, userId int64) interface{} {
	if id <= 0 {
		return nil
	}

	return vant2.DB("accounting_categories").
		Where("id", id).
		Where("user_id", userId).
		Where("is_delete", 0).
		One()
}

// _initUserCategories 初始化用户默认分类
func (c *Category) _initUserCategories(userId int64) {
	currentTime := vant2.Time()

	// 支出分类
	expenseCategories := []g.Map{
		{"name": "餐饮", "emoji": "🍽️", "color": "#ff7875", "weight": 1},
		{"name": "交通", "emoji": "🚗", "color": "#40a9ff", "weight": 2},
		{"name": "服饰", "emoji": "👕", "color": "#b37feb", "weight": 3},
		{"name": "购物", "emoji": "🛍️", "color": "#ff9c6e", "weight": 4},
		{"name": "服务", "emoji": "🔧", "color": "#36cfc9", "weight": 5},
		{"name": "教育", "emoji": "📚", "color": "#73d13d", "weight": 6},
		{"name": "娱乐", "emoji": "🎮", "color": "#ffb347", "weight": 7},
		{"name": "运动", "emoji": "⚽", "color": "#95de64", "weight": 8},
		{"name": "生活缴费", "emoji": "💡", "color": "#ffd666", "weight": 9},
		{"name": "旅行", "emoji": "✈️", "color": "#87e8de", "weight": 10},
		{"name": "宠物", "emoji": "🐕", "color": "#ffadd2", "weight": 11},
		{"name": "医疗", "emoji": "🏥", "color": "#ff85c0", "weight": 12},
		{"name": "其他", "emoji": "🔍", "color": "#8c8c8c", "weight": 13},
	}

	// 入账分类
	incomeCategories := []g.Map{
		{"name": "生意", "emoji": "💼", "color": "#1890ff", "weight": 1},
		{"name": "工资", "emoji": "💰", "color": "#52c41a", "weight": 2},
		{"name": "奖金", "emoji": "🎁", "color": "#faad14", "weight": 3},
		{"name": "其他人情", "emoji": "🧧", "color": "#ff7875", "weight": 4},
		{"name": "收红包", "emoji": "🧧", "color": "#f759ab", "weight": 5},
		{"name": "收转账", "emoji": "📱", "color": "#40a9ff", "weight": 6},
		{"name": "商家转账", "emoji": "🏪", "color": "#36cfc9", "weight": 7},
		{"name": "退款", "emoji": "↩️", "color": "#95de64", "weight": 8},
		{"name": "其他", "emoji": "🔍", "color": "#8c8c8c", "weight": 9},
	}

	// 不计入收入分类
	transferCategories := []g.Map{
		{"name": "理财", "emoji": "📈", "color": "#722ed1", "weight": 1},
		{"name": "借还款", "emoji": "🤝", "color": "#faad14", "weight": 2},
		{"name": "其他", "emoji": "🔍", "color": "#8c8c8c", "weight": 3},
	}

	// 批量插入支出分类
	for _, category := range expenseCategories {
		category["user_id"] = userId
		category["type"] = 1
		category["is_system"] = 1
		category["create_time"] = currentTime
		category["update_time"] = currentTime
		vant2.DB("accounting_categories").Add(category)
	}

	// 批量插入入账分类
	for _, category := range incomeCategories {
		category["user_id"] = userId
		category["type"] = 2
		category["is_system"] = 1
		category["create_time"] = currentTime
		category["update_time"] = currentTime
		vant2.DB("accounting_categories").Add(category)
	}

	// 批量插入不计入收入分类
	for _, category := range transferCategories {
		category["user_id"] = userId
		category["type"] = 3
		category["is_system"] = 1
		category["create_time"] = currentTime
		category["update_time"] = currentTime
		vant2.DB("accounting_categories").Add(category)
	}
}
