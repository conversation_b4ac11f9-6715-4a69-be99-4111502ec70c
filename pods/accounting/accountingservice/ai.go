package accountingservice

import (
	"fmt"
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// AI 记账AI助手服务
type AI struct{}

// GetAllConfig 获取当前用户的所有记账配置选项
func (a *AI) GetAllConfig(userId interface{}) g.Map {
	// 获取用户的分类列表
	categoryService := &Category{}
	categories := categoryService.GetList(nil, gconv.Int64(userId)) // 获取所有类型的分类

	// 获取用户的标签列表
	tagService := &Tag{}
	tags := tagService.GetList(nil, gconv.Int64(userId)) // 获取所有类型的标签

	// 获取有效记账类型列表
	recordTypes := []g.Map{
		{"value": 1, "name": "支出"},
		{"value": 2, "name": "收入"},
		{"value": 3, "name": "不计入收入"},
	}

	// 构建配置返回数据
	config := g.Map{
		"categories":   categories,  // 用户的分类列表
		"tags":         tags,        // 用户的标签列表
		"record_types": recordTypes, // 有效记账类型列表
	}

	return config
}

// GenerateAddRecordsPrompt 生成专门的新增记账记录AI工具定义（支持批量）
func (a *AI) GenerateAddRecordsPrompt(userId interface{}) []g.Map {
	// 获取当前配置
	config := a.GetAllConfig(userId)

	// 获取当前时间
	now := time.Now()
	dateNow := now.Format("2006-01-02 15:04:05")

	// 构建分类选项描述
	var categoryDesc strings.Builder
	categoryDesc.WriteString("可选分类：")

	// 处理分类数据
	if categoriesData := config["categories"]; categoriesData != nil {
		slice := gconv.SliceAny(categoriesData)
		for i, item := range slice {
			if catMap := gconv.Map(item); len(catMap) > 0 {
				if i > 0 {
					categoryDesc.WriteString("、")
				}
				typeName := ""
				switch gconv.Int(catMap["type"]) {
				case 1:
					typeName = "支出"
				case 2:
					typeName = "收入"
				case 3:
					typeName = "不计入收入"
				}
				categoryDesc.WriteString(fmt.Sprintf("%s(ID:%v,类型:%s)", catMap["name"], catMap["id"], typeName))
			}
		}
	}

	// 构建标签选项描述
	var tagDesc strings.Builder
	tagDesc.WriteString("可选标签：")

	// 处理标签数据
	if tagsData := config["tags"]; tagsData != nil {
		slice := gconv.SliceAny(tagsData)
		for i, item := range slice {
			if tagMap := gconv.Map(item); len(tagMap) > 0 {
				if i > 0 {
					tagDesc.WriteString("、")
				}
				typeName := ""
				switch gconv.Int(tagMap["type"]) {
				case 1:
					typeName = "支出"
				case 2:
					typeName = "收入"
				case 3:
					typeName = "不计入收入"
				}
				tagDesc.WriteString(fmt.Sprintf("%s(类型:%s)", tagMap["name"], typeName))
			}
		}
	}

	// 生成专门的新增记账记录工具定义
	tools := []g.Map{
		{
			"type": "function",
			"function": g.Map{
				"name":        "addRecords",
				"description": fmt.Sprintf("解析用户输入，提取记账记录信息，返回结构化数据供用户确认。支持批量提取多条记录。当前系统时间：%s。%s。%s。记账类型说明：1=支出，2=收入，3=不计入收入。", dateNow, categoryDesc.String(), tagDesc.String()),
				"parameters": g.Map{
					"type": "object",
					"properties": g.Map{
						"records": g.Map{
							"type": "array",
							"items": g.Map{
								"type": "object",
								"properties": g.Map{
									"type": g.Map{
										"type":        "integer",
										"description": "记账类型：1=支出，2=收入，3=不计入收入",
									},
									"category_id": g.Map{
										"type":        "integer",
										"description": "记账分类ID，从可选分类中选择对应的ID，必须指定一个有效的分类ID，注意分类类型要与记账类型匹配",
									},
									"amount": g.Map{
										"type":        "number",
										"description": "记账金额，必须大于0",
									},
									"description": g.Map{
										"type":        "string",
										"description": "记账描述，详细说明这笔记录的内容",
									},
									"record_time": g.Map{
										"type":        "string",
										"description": "记账时间，格式：yyyy-MM-dd HH:mm:ss，例如：2024-01-15 14:30:00。如果用户只说了日期没说时间，默认设置为当天12:00",
									},
									"tags": g.Map{
										"type": "array",
										"items": g.Map{
											"type": "string",
										},
										"description": "记账标签数组，用于分类和标记记录。可以选择已有标签，也可以创建新标签，标签类型要与记账类型匹配",
									},
								},
								"required": []string{
									"type",
									"category_id",
									"amount",
									"description",
									"record_time",
								},
							},
							"description": "记账记录数组，支持一次性提取多条记录",
						},
					},
					"required": []string{
						"records",
					},
				},
			},
		},
	}

	return tools
}

// GenerateSearchRecordsPrompt 生成专门的搜索记账记录AI工具定义
func (a *AI) GenerateSearchRecordsPrompt(userId interface{}) []g.Map {

	// 生成专门的搜索记账记录工具定义
	tools := []g.Map{
		{
			"type": "function",
			"function": g.Map{
				"name":        "searchRecords",
				"description": "搜索和查询记账记录，用于查找特定的记录或获取记录列表",
				"parameters": g.Map{
					"type": "object",
					"properties": g.Map{
						"keyword": g.Map{
							"type":        "string",
							"description": "搜索关键词，用于匹配记录描述",
						},
						"type": g.Map{
							"type":        "integer",
							"description": "按记账类型筛选：1=支出，2=收入，3=不计入收入",
						},
						"category_id": g.Map{
							"type":        "integer",
							"description": "按分类ID筛选",
						},
						"start_time": g.Map{
							"type":        "string",
							"description": "开始时间，格式：yyyy-MM-dd HH:mm:ss",
						},
						"end_time": g.Map{
							"type":        "string",
							"description": "结束时间，格式：yyyy-MM-dd HH:mm:ss",
						},
						"tag_names": g.Map{
							"type": "array",
							"items": g.Map{
								"type": "string",
							},
							"description": "按标签名称筛选",
						},
						"limit": g.Map{
							"type":        "integer",
							"description": "返回结果数量限制，默认为10",
						},
					},
					"required": []string{},
				},
			},
		},
	}

	return tools
}

// GeneratePrompt 生成大模型对话的prompt（复用新增和搜索方法）
func (a *AI) GeneratePrompt(userId interface{}) []g.Map {
	// 复用调用新增和搜索的专门方法
	addRecordsTools := a.GenerateAddRecordsPrompt(userId)
	searchRecordsTools := a.GenerateSearchRecordsPrompt(userId)

	// 合并工具定义
	tools := make([]g.Map, 0)
	tools = append(tools, addRecordsTools...)
	tools = append(tools, searchRecordsTools...)

	return tools
}

// ProcessRecordByAI 处理AI生成的记账操作（只支持addRecords和searchRecords）
func (a *AI) ProcessRecordByAI(userId interface{}, functionName string, params g.Map) (interface{}, string) {
	switch functionName {
	case "addRecords":
		return a._processAddRecords(userId, params)
	case "searchRecords":
		return a._processSearchRecords(userId, params)
	default:
		return false, "不支持的函数调用，当前只支持addRecords和searchRecords"
	}
}

// _processAddRecords 解析记账记录信息，返回结构化数据（不保存到数据库）
func (a *AI) _processAddRecords(userId interface{}, params g.Map) (interface{}, string) {
	// 获取records数组
	recordsData, ok := params["records"]
	if !ok {
		return false, "缺少records参数"
	}

	recordsArray, ok := recordsData.([]interface{})
	if !ok {
		return false, "records参数格式错误，应为数组"
	}

	if len(recordsArray) == 0 {
		return false, "records数组不能为空"
	}

	var processedRecords []g.Map

	for i, recordData := range recordsArray {
		recordMap := gconv.Map(recordData)
		if recordMap == nil {
			continue
		}

		// 验证必填字段
		recordType := gconv.Int(recordMap["type"])
		if recordType <= 0 || recordType > 3 {
			return false, fmt.Sprintf("第%d条记录：记账类型无效，必须为1(支出)、2(收入)或3(不计入收入)", i+1)
		}

		categoryId := gconv.Int(recordMap["category_id"])
		if categoryId <= 0 {
			return false, fmt.Sprintf("第%d条记录：分类ID无效", i+1)
		}

		amount := gconv.Float64(recordMap["amount"])
		if amount <= 0 {
			return false, fmt.Sprintf("第%d条记录：金额必须大于0", i+1)
		}

		description := gconv.String(recordMap["description"])
		if description == "" {
			return false, fmt.Sprintf("第%d条记录：描述不能为空", i+1)
		}

		// 验证分类是否存在且类型匹配
		categoryExists := vant2.DB("accounting_categories").
			Where("id", categoryId).
			Where("user_id", userId).
			Where("type", recordType).
			Where("is_delete", 0).
			Count()
		if categoryExists == 0 {
			return false, fmt.Sprintf("第%d条记录：指定的分类不存在或类型不匹配", i+1)
		}

		// 处理记账时间
		var recordTime int64
		recordTimeStr := gconv.String(recordMap["record_time"])
		if recordTimeStr != "" {
			if t, err := time.Parse("2006-01-02 15:04:05", recordTimeStr); err == nil {
				recordTime = t.Unix()
			} else {
				return false, fmt.Sprintf("第%d条记录：时间格式错误", i+1)
			}
		} else {
			recordTime = time.Now().Unix()
		}

		// 处理标签
		var tags []string
		if tagArray, ok := recordMap["tags"].([]interface{}); ok {
			for _, tag := range tagArray {
				if tagStr := gconv.String(tag); tagStr != "" {
					tags = append(tags, tagStr)
				}
			}
		}

		// 构建处理后的记录
		processedRecord := g.Map{
			"type":        recordType,
			"category_id": categoryId,
			"amount":      amount,
			"description": description,
			"record_time": recordTime,
			"tags":        tags,
		}

		processedRecords = append(processedRecords, processedRecord)
	}

	return g.Map{
		"records": processedRecords,
		"count":   len(processedRecords),
	}, fmt.Sprintf("成功解析%d条记账记录", len(processedRecords))
}

// _processSearchRecords 处理搜索记账记录
func (a *AI) _processSearchRecords(userId interface{}, params g.Map) (interface{}, string) {
	keyword := gconv.String(params["keyword"])
	recordType := gconv.Int(params["type"])
	categoryId := gconv.Int(params["category_id"])
	startTimeStr := gconv.String(params["start_time"])
	endTimeStr := gconv.String(params["end_time"])
	limit := gconv.Int(params["limit"])
	if limit == 0 {
		limit = 10
	}

	// 构建查询
	db := vant2.DB("accounting_records r").
		LeftJoin("accounting_categories c", "r.category_id = c.id").
		Where("r.user_id", userId).
		Where("r.is_delete", 0)

	// 关键词搜索
	if keyword != "" {
		db = db.Where("r.description LIKE ?", "%"+keyword+"%")
	}

	// 类型筛选
	if recordType > 0 && recordType <= 3 {
		db = db.Where("r.type", recordType)
	}

	// 分类筛选
	if categoryId > 0 {
		db = db.Where("r.category_id", categoryId)
	}

	// 时间筛选
	if startTimeStr != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr); err == nil {
			db = db.Where("r.record_time >=", startTime.Unix())
		}
	}
	if endTimeStr != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr); err == nil {
			db = db.Where("r.record_time <=", endTime.Unix())
		}
	}

	// 标签筛选
	if tagArray, ok := params["tag_names"].([]interface{}); ok && len(tagArray) > 0 {
		var tagNames []string
		for _, tag := range tagArray {
			if tagStr := gconv.String(tag); tagStr != "" {
				tagNames = append(tagNames, tagStr)
			}
		}
		if len(tagNames) > 0 {
			db = db.LeftJoin("accounting_record_tags rt", "r.id = rt.record_id").
				LeftJoin("accounting_tags t", "rt.tag_id = t.id").
				Where("t.name", tagNames).
				Group("r.id")
		}
	}

	// 查询结果
	records, _ := db.Fields("r.id,r.type,r.category_id,r.amount,r.description,r.record_time,r.create_time,c.name as category_name,c.emoji as category_emoji").
		Order("r.record_time DESC, r.id DESC").
		Limit(limit).
		All()

	return records, "搜索完成"
}
