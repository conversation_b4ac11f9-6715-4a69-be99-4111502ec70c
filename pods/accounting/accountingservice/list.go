package accountingservice

import (
	"strings"
	"time"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type List struct{}

// GetRecordList 获取记账记录列表
// 支持按分类筛选（收入/支出/全部）和日期筛选
func (l *List) GetRecordList(userId int64, recordType string, dates []string, pageNum, pageSize int) (g.Map, error) {
	db := vant2.DB("accounting_records r").
		LeftJoin("accounting_categories c", "r.category_id = c.id").
		Where("r.user_id", userId).
		Where("r.is_delete", 0).
		Fields("r.id, r.type, r.category_id, r.amount, r.description, r.images, r.record_time, r.create_time, c.name as category_name, c.emoji as category_emoji, c.color as category_color")

	// 按类型筛选
	if recordType == "income" {
		db = db.Where("r.type", 2) // 收入
	} else if recordType == "expense" {
		db = db.Where("r.type", 1) // 支出
	}
	// recordType == "all" 时不添加类型条件，查询全部

	// 按日期筛选
	if len(dates) > 0 {
		// 将日期字符串转换为时间戳范围
		var timeConditions []string
		var timeArgs []interface{}

		for _, dateStr := range dates {
			if dateStr != "" {
				// 解析日期 2024-11-11
				startTime, err := time.Parse("2006-01-02", dateStr)
				if err != nil {
					continue
				}
				// 当天开始时间戳
				startTimestamp := startTime.Unix()
				// 当天结束时间戳
				endTimestamp := startTime.AddDate(0, 0, 1).Unix() - 1

				timeConditions = append(timeConditions, "(r.record_time >= ? AND r.record_time <= ?)")
				timeArgs = append(timeArgs, startTimestamp, endTimestamp)
			}
		}

		if len(timeConditions) > 0 {
			whereCondition := "(" + strings.Join(timeConditions, " OR ") + ")"
			db = db.Where(whereCondition, timeArgs...)
		}
	}

	// 按记账时间倒序排列
	db = db.Order("r.record_time DESC, r.id DESC")

	// 分页查询
	records, total := db.Paginate(pageNum, pageSize)

	// 批量获取所有记录的标签信息 - 修复N+1查询问题
	var recordIds []int64
	for _, record := range records {
		recordIds = append(recordIds, record["id"].Int64())
	}

	// 一次性查询所有标签
	tagsMap := l._getRecordTagsBatch(recordIds)

	// 处理返回数据
	var recordList []g.Map
	for _, record := range records {
		recordId := record["id"].Int64()

		item := g.Map{
			"id":            recordId,
			"type":          record["type"].Int(),
			"categoryId":    record["category_id"].Int64(),
			"categoryName":  record["category_name"].String(),
			"categoryEmoji": record["category_emoji"].String(),
			"categoryColor": record["category_color"].String(),
			"amount":        record["amount"].String(),
			"description":   record["description"].String(),
			"recordTime":    record["record_time"].Int64(),
			"createTime":    record["create_time"].Int64(),
		}

		// 处理图片数据
		imagesStr := record["images"].String()
		if imagesStr != "" && imagesStr != "null" {
			var images []string
			err := gconv.Struct(imagesStr, &images)
			if err != nil {
				item["images"] = []string{}
			} else {
				item["images"] = images
			}
		} else {
			item["images"] = []string{}
		}

		// 添加标签信息
		if tags, exists := tagsMap[recordId]; exists {
			item["tags"] = tags
		} else {
			item["tags"] = []g.Map{}
		}

		recordList = append(recordList, item)
	}

	return g.Map{
		"list":  recordList,
		"total": total,
	}, nil
}

// _getRecordTags 获取记录关联的标签信息
func (l *List) _getRecordTags(recordId int64) []g.Map {
	// 关联查询记录标签
	tagRecords := vant2.DB("accounting_record_tags rt").
		LeftJoin("accounting_tags t", "rt.tag_id = t.id").
		Where("rt.record_id", recordId).
		Where("t.is_delete", 0).
		Fields("t.id, t.name, t.color").
		Select()

	var tags []g.Map
	for _, tagRecord := range tagRecords {
		tag := g.Map{
			"id":    tagRecord["id"].Int64(),
			"name":  tagRecord["name"].String(),
			"color": tagRecord["color"].String(),
		}
		tags = append(tags, tag)
	}

	return tags
}

// _getRecordTagsBatch 批量获取多个记录的标签信息
func (l *List) _getRecordTagsBatch(recordIds []int64) map[int64][]g.Map {
	if len(recordIds) == 0 {
		return make(map[int64][]g.Map)
	}

	// 一次性查询所有标签
	tags := vant2.DB("accounting_record_tags rt").
		LeftJoin("accounting_tags t", "rt.tag_id = t.id").
		Where("rt.record_id", recordIds).
		Where("t.is_delete", 0).
		Fields("rt.record_id, t.id, t.name, t.color").
		Select()

	// 按record_id分组
	tagsMap := make(map[int64][]g.Map)
	for _, tag := range tags {
		recordId := tag["record_id"].Int64()
		if tagsMap[recordId] == nil {
			tagsMap[recordId] = []g.Map{}
		}
		tagsMap[recordId] = append(tagsMap[recordId], g.Map{
			"id":    tag["id"].Int64(),
			"name":  tag["name"].String(),
			"color": tag["color"].String(),
		})
	}

	return tagsMap
}

// GetMonthDates 获取指定月份有记录的日期列表
// 参数：userId 用户ID，month 月份格式如 2024-05
// 返回：该月份有记录的日期数组，格式如 ["2024-05-01", "2024-05-03"]
func (l *List) GetMonthDates(userId int64, month string) ([]string, error) {
	// 解析月份参数
	monthTime, err := time.Parse("2006-01", month)
	if err != nil {
		return nil, err
	}

	// 计算月份开始和结束时间戳
	startTime := monthTime
	endTime := monthTime.AddDate(0, 1, 0).Add(-time.Second)
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	// 查询该月份的记录，按日期分组
	// 使用 FROM_UNIXTIME 将时间戳转换为日期格式进行分组
	records := vant2.DB("accounting_records").
		Where("user_id", userId).
		Where("is_delete", 0).
		Where("record_time >= ?", startTimestamp).
		Where("record_time <= ?", endTimestamp).
		Fields("DATE(FROM_UNIXTIME(record_time)) as record_date").
		Group("DATE(FROM_UNIXTIME(record_time))").
		Order("record_date ASC").
		Select()

	var dates []string
	for _, record := range records {
		if dateStr := record["record_date"].String(); dateStr != "" {
			dates = append(dates, dateStr)
		}
	}

	return dates, nil
}
