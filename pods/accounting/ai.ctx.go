package accounting

import (
	"assistant/pods/accounting/accountingservice"
	"assistant/pods/base"
	"assistant/rag/openai"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// AIGetConfig 获取AI配置信息
func AIGetConfig(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	aiService := &accountingservice.AI{}
	config := aiService.GetAllConfig(_base.UserId)
	_base.Back.ApiSuccess("获取AI配置成功", _base.Back.ToSnake(config))
}

// AIExtract 提取AI配置信息
func AIExtract(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	_content := gconv.String(_base.Input.Value("content"))
	if _content == "" {
		_base.Back.ApiError(-1, "参数错误：内容不能为空")
		return
	}

	aiService := &accountingservice.AI{}
	tools := aiService.GenerateAddRecordsPrompt(_base.UserId)

	_llm := openai.New(openai.LLMKey("deepseek-v3-sop")).Tools(tools)

	_result := _llm.Chat(`用户输入内容是【` + _content + `】，请将用户输入内容转化成记账记录`)
	_func := vant2.LodashGetMap(_result.Tools, "0.function")
	if _func != nil {
		_func["arguments"] = vant2.JsonDecodeMap(vant2.LodashGetString(_func, "arguments"))
		_base.Back.ApiSuccess("AI识别成功", _func)
	} else {
		_base.Back.ApiError(-1, "抱歉，AI无法将您的信息转化成记账记录")
	}
}

// AIGeneratePrompt 生成AI对话工具定义
func AIGeneratePrompt(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	aiService := &accountingservice.AI{}
	tools := aiService.GeneratePrompt(_base.UserId)
	_base.Back.ApiSuccess("生成AI工具定义成功", tools)
}

// AIGenerateAddRecordsPrompt 生成专门的新增记账记录AI工具定义
func AIGenerateAddRecordsPrompt(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	aiService := &accountingservice.AI{}
	tools := aiService.GenerateAddRecordsPrompt(_base.UserId)
	_base.Back.ApiSuccess("生成新增记账记录AI工具定义成功", tools)
}

// AIGenerateSearchRecordsPrompt 生成专门的搜索记账记录AI工具定义
func AIGenerateSearchRecordsPrompt(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	aiService := &accountingservice.AI{}
	tools := aiService.GenerateSearchRecordsPrompt(_base.UserId)
	_base.Back.ApiSuccess("生成搜索记账记录AI工具定义成功", tools)
}

// AIProcessRecord 处理AI记账操作
func AIProcessRecord(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	functionName := gconv.String(_base.Input.Value("functionName"))
	if functionName == "" {
		_base.Back.ApiError(-1, "参数错误：函数名不能为空")
		return
	}

	// 获取参数
	params := g.Map{}
	paramsStr := gconv.String(_base.Input.Value("params"))
	if paramsStr != "" {
		if err := gconv.Struct(paramsStr, &params); err != nil {
			_base.Back.ApiError(-2, "参数格式错误")
			return
		}
	}

	aiService := &accountingservice.AI{}
	result, msg := aiService.ProcessRecordByAI(_base.UserId, functionName, params)

	if result == false {
		_base.Back.ApiError(-3, msg)
		return
	}

	_base.Back.ApiSuccess(msg, _base.Back.ToSnake(result))
}
