package accounting

import (
	"assistant/pods/accounting/accountingservice"
	"assistant/pods/base"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// GetCategories 获取分类列表
func GetCategories(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取分类类型参数，不传则获取所有
	categoryType := _base.Input.Value("type")

	categoryService := &accountingservice.Category{}
	result := categoryService.GetList(categoryType, _base.UserId)

	_base.Back.ApiSuccess("获取成功", _base.Back.ToSnake(result))
}

// SaveCategory 保存分类（新增/编辑）
func SaveCategory(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	data := g.Map{
		"id":     _base.Input.Value("id"),
		"name":   _base.Input.Value("name"),
		"type":   _base.Input.Value("type"),
		"color":  _base.Input.Value("color"),
		"emoji":  _base.Input.Value("emoji"),
		"weight": _base.Input.Value("weight"),
	}

	categoryService := &accountingservice.Category{}
	result := categoryService.Save(data, _base.UserId)

	_base.Back.ApiBack("保存分类", result)
}

// DeleteCategory 删除分类
func DeleteCategory(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	categoryService := &accountingservice.Category{}
	result := categoryService.Delete(_base.Id, _base.UserId)

	_base.Back.ApiBack("删除分类", result)
}

// GetTags 获取标签列表
func GetTags(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取标签类型参数，不传则获取所有
	tagType := _base.Input.Value("type")

	tagService := &accountingservice.Tag{}
	result := tagService.GetList(tagType, _base.UserId)

	_base.Back.ApiSuccess("获取成功", _base.Back.ToSnake(result))
}

// SaveTag 保存标签（新增/编辑）
func SaveTag(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	data := g.Map{
		"id":    _base.Input.Value("id"),
		"name":  _base.Input.Value("name"),
		"type":  _base.Input.Value("type"),
		"color": _base.Input.Value("color"),
	}

	tagService := &accountingservice.Tag{}
	result := tagService.Save(data, _base.UserId)

	_base.Back.ApiBack("保存标签", result)
}

// DeleteTag 删除标签
func DeleteTag(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	tagService := &accountingservice.Tag{}
	result := tagService.Delete(_base.Id, _base.UserId)

	_base.Back.ApiBack("删除标签", result)
}

// GetRecordDetail 获取单笔记录详情
func GetRecordDetail(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	recordService := &accountingservice.Record{}
	result := recordService.GetDetail(_base.Id, _base.UserId)

	if result == nil {
		_base.Back.ApiError(-1, "记录不存在或无权限访问")
		return
	}

	_base.Back.ApiSuccess("获取成功", _base.Back.ToSnake(result))
}

// GetRecords 获取记账记录列表
func GetRecords(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	params := g.Map{
		"type":       _base.Input.Value("type"),
		"categoryId": _base.Input.Value("categoryId"),
		"startTime":  _base.Input.Value("startTime"),
		"endTime":    _base.Input.Value("endTime"),
		"tags":       _base.Input.Value("tags"),
		"pageNum":    _base.PageNum,
		"pageSize":   _base.PageSize,
	}

	recordService := &accountingservice.Record{}
	result, total := recordService.GetList(params, _base.UserId)

	_base.Back.ApiRows(_base.Back.ToSnake(result), total)
}

// SaveRecord 保存记账记录（新增/编辑）
func SaveRecord(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	data := g.Map{
		"id":          _base.Input.Value("id"),
		"type":        _base.Input.Value("type"),
		"categoryId":  _base.Input.Value("categoryId"),
		"amount":      _base.Input.Value("amount"),
		"description": _base.Input.Value("description"),
		"images":      _base.Input.Value("images"),
		"recordTime":  _base.Input.Value("recordTime"),
		"tags":        _base.Input.Value("tags"), // 标签名称数组
	}

	recordService := &accountingservice.Record{}
	result := recordService.Save(data, _base.UserId)

	_base.Back.ApiBack("保存记账记录", result)
}

// DeleteRecord 删除记账记录
func DeleteRecord(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	recordService := &accountingservice.Record{}
	result := recordService.Delete(_base.Id, _base.UserId)

	_base.Back.ApiBack("删除记账记录", result)
}

// GetStatistics 获取统计数据
func GetStatistics(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	params := g.Map{
		"startTime": _base.Input.Value("startTime"),
		"endTime":   _base.Input.Value("endTime"),
	}

	recordService := &accountingservice.Record{}
	result := recordService.GetStatistics(params, _base.UserId)

	_base.Back.ApiSuccess("获取统计数据成功", _base.Back.ToSnake(result))
}

// GetRecordList 记账记录列表查询（新版本）
// 支持按分类筛选（收入/支出/全部）和多日期筛选
func GetRecordList(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取查询参数 - 使用GetString方法避免类型转换错误
	recordType := _base.Input.GetString("type") // 类型：income收入 expense支出 all全部，默认all
	datesStr := _base.Input.GetString("dates")  // 日期数组，逗号分隔，如："2024-11-11,2024-11-12,2024-11-13"

	// 处理日期参数
	var dates []string
	if datesStr != "" {
		dates = strings.Split(datesStr, ",")
	}

	// 调用服务层方法
	listService := &accountingservice.List{}
	result, err := listService.GetRecordList(_base.UserId, recordType, dates, _base.PageNum, _base.PageSize)
	if err != nil {
		_base.Back.ApiError(-1, "查询失败", err.Error())
		return
	}

	// 返回结果
	_base.Back.ApiSuccess("查询成功", _base.Back.ToSnake(result["list"]), nil, result["total"])
}

// GetMonthDates 获取指定月份有记录的日期列表
func GetMonthDates(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	// 获取月份参数 - 使用GetString方法避免类型转换错误
	month := _base.Input.GetString("month") // 月份格式：2024-05

	if month == "" {
		_base.Back.ApiError(-1, "月份参数不能为空")
		return
	}

	// 调用服务层方法
	listService := &accountingservice.List{}
	dates, err := listService.GetMonthDates(_base.UserId, month)
	if err != nil {
		_base.Back.ApiError(-1, "查询失败", err.Error())
		return
	}

	// 返回结果
	_base.Back.ApiSuccess("查询成功", dates)
}
