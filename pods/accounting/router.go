package accounting

import (
	"github.com/gogf/gf/v2/net/ghttp"
)

// InitRouter 初始化记账模块路由
func InitRouter(s *ghttp.Server) {
	group := s.Group("/accounting")

	// 分类管理
	group.GET("/categories", GetCategories)        // 获取分类列表
	group.POST("/category/save", SaveCategory)     // 保存分类（新增/编辑）
	group.POST("/category/delete", DeleteCategory) // 删除分类

	// 标签管理
	group.GET("/tags", GetTags)          // 获取标签列表
	group.POST("/tag/save", SaveTag)     // 保存标签（新增/编辑）
	group.POST("/tag/delete", DeleteTag) // 删除标签

	// 记账记录管理
	group.ALL("/records", GetRecords)            // 获取记账记录列表（旧版本）
	group.GET("/record/list", GetRecordList)     // 获取记账记录列表（新版本，支持多日期筛选）
	group.GET("/record/detail", GetRecordDetail) // 获取单笔记录详情
	group.POST("/record/save", SaveRecord)       // 保存记账记录（新增/编辑）
	group.POST("/record/delete", DeleteRecord)   // 删除记账记录

	// 日期相关
	group.GET("/month/dates", GetMonthDates) // 获取指定月份有记录的日期列表

	// 统计相关
	group.GET("/statistics", GetStatistics) // 获取统计数据

	// AI相关
	group.GET("/ai/config", AIGetConfig)                          // 获取AI配置信息
	group.POST("/ai/extract", AIExtract)                          // AI抽取记账记录
	group.GET("/ai/prompt", AIGeneratePrompt)                     // 生成AI对话工具定义
	group.GET("/ai/prompt/add", AIGenerateAddRecordsPrompt)       // 生成新增记账记录AI工具定义
	group.GET("/ai/prompt/search", AIGenerateSearchRecordsPrompt) // 生成搜索记账记录AI工具定义
	group.POST("/ai/process", AIProcessRecord)                    // 处理AI记账操作
}
