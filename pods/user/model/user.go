package userm

// CREATE TABLE `dede_user` (
//   `id` int(11) NOT NULL AUTO_INCREMENT,
//   `mobile` varchar(11) NOT NULL DEFAULT '',
//   `user_name` varchar(64) NOT NULL DEFAULT '',
//   `user_pwd` varchar(32) NOT NULL DEFAULT '',
//   `user_nick` varchar(200) NOT NULL DEFAULT '',
//   `email` varchar(64) NOT NULL DEFAULT '',
//   `ip_last` varchar(64) NOT NULL DEFAULT '',
//   `ip_create` varchar(64) NOT NULL DEFAULT '',
//   `time_last` int(11) NOT NULL DEFAULT '0',
//   `time_create` int(11) NOT NULL DEFAULT '0',
//   `avatar` varchar(255) NOT NULL DEFAULT '',
//   `is_delete` tinyint(4) NOT NULL DEFAULT '0',
//   `money` double(11,4) NOT NULL DEFAULT '0.0000',
//   `score` int(11) NOT NULL DEFAULT '0',
//   PRIMARY KEY (`id`,`mobile`) USING BTREE
// ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='用户表'

type UserModel struct {
	Id         int64   `json:"id"`
	Mobile     string  `json:"mobile"`
	UserName   string  `json:"user_name"`
	UserPwd    string  `json:"user_pwd"`
	UserNick   string  `json:"user_nick"`
	Email      string  `json:"email"`
	Avatar     string  `json:"avatar"`
	IsDelete   int     `json:"is_delete"`
	Money      float64 `json:"money"`
	Score      int     `json:"score"`
	IpLast     string  `json:"ip_last"`
	IpCreate   string  `json:"ip_create"`
	TimeLast   int64   `json:"time_last"`
	TimeCreate int64   `json:"time_create"`
}
