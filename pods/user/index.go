package user

import (
	"assistant/pods/base"
	"vant2"
	"vant2/tool/w"

	auths "assistant/pods/auth/service"
	users "assistant/pods/user/service"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

var UserCtx = func() *_user {
	return &_user{}
}()

type _user struct {
	*base.BaseCtx
}

// 加载用户基本信息
func (d *_user) ApiUser(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	d.UserInfo = users.UserInfoFromUserId(d.UserId)
	if d.UserInfo != nil {
		d.Back.ApiSuccess("ok", d.UserInfo)
	} else {
		d.Back.ApiError(407, "用户信息加载失败")
	}
}

// 退出登录
func (d *_user) Logout(r *ghttp.Request) {
	_b := base.InitBaseCtx(r)
	// 清除Token
	_b.Back.ApiBack("退出", auths.ClearToken(_b.Token))
}

// 修改用户头像和昵称
func (d *_user) ModifyInformation(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	_res := users.SaveNickAvatar(d.UserId, d.Input.GetString("user_nick"), d.Input.GetString("avatar"))
	d.Back.ApiBack("保存", _res)
}

// 修改密码
func (d *_user) ModifyPassword(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	_pwd := w.Map{
		"user_pwd":  d.Input.GetString("user_pwd", "required|password", "密码"),
		"check_pwd": d.Input.GetString("check_pwd", "required|password", "确认密码"),
	}
	if _pwd["user_pwd"] != _pwd["check_pwd"] {
		d.Back.ApiError(407, "两次密码不一致")
	}
	_psd := vant2.Password(_pwd["user_pwd"])
	_res := vant2.DB("user").Where("id", d.UserId).Data(w.Map{
		"user_pwd": _psd,
	}).Update()
	d.Back.ApiBack("保存", _res)
}

// 换绑手机的3个流程
// 1. 旧手机号发送验证码
func (d *_user) SendCodeOld(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	d.UserInfo = users.UserInfoFromUserId(d.UserId)
	if d.UserInfo == nil {
		d.Back.ApiError(407, "用户信息加载失败")
	}
	// 发送验证码
	_res := vant2.AliCode.Send(gconv.String(d.UserInfo.Mobile))
	d.Back.ApiBack("发送", _res)
}

// 2. 验证验证码
func (d *_user) VerifyCode(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	d.UserInfo = users.UserInfoFromUserId(d.UserId)
	if d.UserInfo == nil {
		d.Back.ApiError(407, "用户信息加载失败")
	}
	_code := d.Input.GetString("code", "required|code", "验证码")
	_res := vant2.AliCode.Check(gconv.String(d.UserInfo.Mobile), _code)
	if _res {
		d.Back.ApiSuccess("验证成功", w.Map{
			"mobile": gconv.String(d.UserInfo.Mobile),
		})
	} else {
		d.Back.ApiError(407, "验证码错误")
	}
}

// 3. 新手机号发送验证码
func (d *_user) SendCodeNew(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	d.UserInfo = users.UserInfoFromUserId(d.UserId)
	if d.UserInfo == nil {
		d.Back.ApiError(407, "用户信息加载失败")
	}
	_mobile := d.Input.GetString("mobile", "required|phone", "新手机号")
	if _mobile == gconv.String(d.UserInfo.Mobile) {
		d.Back.ApiError(407, "新手机号不能与旧手机号相同")
	}
	// 发送验证码
	_res := vant2.AliCode.Send(_mobile)
	d.Back.ApiBack("发送", _res)
}

// 4. 换绑手机
func (d *_user) ChangePhone(r *ghttp.Request) {
	d.BaseCtx = base.InitBaseCtx(r)
	d.UserInfo = users.UserInfoFromUserId(d.UserId)
	if d.UserInfo == nil {
		d.Back.ApiError(407, "用户信息加载失败")
	}
	_code := d.Input.GetString("code", "required|code", "验证码")
	_mobile := d.Input.GetString("mobile", "required|phone", "新手机号")
	if _mobile == gconv.String(d.UserInfo.Mobile) {
		d.Back.ApiError(407, "新手机号不能与旧手机号相同")
	}
	// 需要检查当前手机号是否已绑定其他账户
	_count := vant2.DB("user").Where("mobile", _mobile).Count()
	if _count > 0 {
		d.Back.ApiError(407, "当前手机号已绑定其他账户")
	}
	_res := vant2.AliCode.Check(_mobile, _code)
	if _res {
		// 切换手机号
		_res := vant2.DB("user").Where("id", d.UserId).Data(w.Map{
			"mobile": _mobile,
		}).Update()
		d.Back.ApiBack("换绑手机号", _res)
	} else {
		d.Back.ApiError(407, "验证码错误")
	}
}
