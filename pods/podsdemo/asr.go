package podsdemo

import (
	"assistant/plus"
	"assistant/plus/qcloud"
	"assistant/plus/wechatpay"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

var AsrCtx = func() *_asr {
	return &_asr{}
}()

type _asr struct {
}

func (c *_asr) Transfer(r *ghttp.Request) {
	transferReq := plus.TransferRequest{
		OpenID:      "orZnM6SueMYbTB6AhQbr2UHKbq68",
		RealName:    "徐静",       // 可选，如果CheckName为FORCE_CHECK则必填
		Amount:      0.1,        // 转账金额，单位：元
		Description: "奖励金发放",    // 转账备注
		CheckName:   "NO_CHECK", // NO_CHECK：不校验姓名，FORCE_CHECK：强校验姓名
		OutTradeNo:  "",         // 可选，不填则自动生成
	}
	transferRes := wechatpay.Transfer(transferReq)
	r.Response.WriteJson(g.Map{
		"code": 0,
		"msg":  "success",
		"data": transferRes,
	})
}

func (c *_asr) Index(r *ghttp.Request) {
	// 测试1: 识别本地音频文件
	fmt.Println("📁 测试1: 识别本地音频文件")
	fmt.Println("文件路径: static/demo.mp3")

	asr := qcloud.NewASR()
	result1, err1 := asr.RecognizeFromFile("static/demo.mp3")
	if err1 != nil {
		fmt.Printf("❌ 识别失败: %v\n", err1)
	} else {
		fmt.Printf("✅ 识别成功: %s\n", result1)
	}
	fmt.Println()

	r.Response.WriteJson(g.Map{
		"code": 0,
		"msg":  "success",
		"data": result1,
	})
}
