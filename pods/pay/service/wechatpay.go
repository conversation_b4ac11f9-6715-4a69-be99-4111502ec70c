package pays

import (
	"vant2"
	"vant2/tool/w"

	"github.com/go-pay/gopay/wechat/v3"
	"github.com/gogf/gf/v2/net/ghttp"
)

// WechatPayService 微信支付服务结构体
type WechatPayService struct{}

// NewWechatPayService 创建微信支付服务实例
func NewWechatPayService() *WechatPayService {
	return &WechatPayService{}
}

// CreateOrder 创建微信支付订单 (JSAPI)
func (s *WechatPayService) CreateOrder(orderInfo w.Map) *wechat.JSAPIPayParams {
	// 暂时保留原有方法，稍后迁移到 plus 目录
	vant2.Console("CreateOrder 方法将被迁移到 plus/wechatpay")
	return nil
}

// HandleCallback 处理微信支付回调
func (s *WechatPayService) HandleCallback(r *ghttp.Request) w.Map {
	// 暂时保留原有方法，稍后迁移到 plus 目录
	vant2.Console("HandleCallback 方法将被迁移到 plus/wechatpay")
	return w.Map{}
}

// 基于wechat jsapi 创建订单 - 兼容性保留
func CreateOrder(r *ghttp.Request, order w.Map) *wechat.JSAPIPayParams {
	service := NewWechatPayService()
	return service.CreateOrder(order)
}

// PayBack 微信支付回调 - 兼容性保留
func PayBack(r *ghttp.Request) w.Map {
	service := NewWechatPayService()
	return service.HandleCallback(r)
}
