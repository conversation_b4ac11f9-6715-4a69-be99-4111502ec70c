package pays

// 公众号的
var (
	// 微信AppId
	appID = "wx11d53dcac7bb3357"
	// appSecret
	appSecret = "2d692c019f0e13ad8982c810383117ae" //

)

// 支付的
var (
	mchID                      string = "1646855390"                               // 商户号
	mchCertificateSerialNumber string = "3B3E95D6C8C1012CC8AD21BB8F4CB9DDCA929DCB" // 商户证书序列号
	mchAPIv3Key                string = "C44942584F62AD4FF8773CD830D907BA"         // 商户APIv3密钥

******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
)
