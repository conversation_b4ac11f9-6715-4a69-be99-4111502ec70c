package pays

// 支付相关的 服务

import (
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/net/ghttp"
)

type AlipayWebService struct{}

// CreateOrder 创建支付宝支付订单
func (d *AlipayWebService) CreateOrder(r *ghttp.Request) w.Map {
	// 创建订单信息
	// _orderService := chatgpts.OrderInit(r)
	// _orderInfo := _orderService.CreateOrder("web")
	_orderInfo := w.Map{
		"order_id": "1234567890",
		"item_id":  1,
		"price":    100,
	}

	// TODO: 待迁移到 plus/alipay 服务
	vant2.Console("CreateOrder 方法将被迁移到 plus/alipay", _orderInfo)
	return w.Map{
		"message": "支付宝支付功能正在迁移中",
		"data":    _orderInfo,
	}
}

// PayBack 支付宝支付回调
func (d *AlipayWebService) PayBack(r *ghttp.Request) w.Map {
	// TODO: 待迁移到 plus/alipay 服务
	input := vant2.Input(r)
	all := input.GetParamsAll()
	vant2.Console("PayBack 方法将被迁移到 plus/alipay", all)

	r.Response.RedirectTo("http://chat.rxecs.com/pc/#/order")
	return w.Map{
		"message": "支付宝回调功能正在迁移中",
		"body":    all,
	}
}

// 支付宝支付
var (
	// appid
	AlipayWebId        string = "2021004103674085"
	AlipayWebPublicKey string = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAh0y5CsXnJYQ7/afP2DdxiNQl+11REzAbzSZ4mZuhC4d+DUGclWPIzMblwvO13DvE3xDpWG51faLT/RbLl2Tja4L9YMzF379cRL9wn04e53hoaaWPZZq+yJonKdXTqdBI5fbTS0SM0dG4HNe1dWRsVTU+vt0GY/KkmuCuUSRKvmVKaD5lrkJbK9/9pG2+u87CCrfVjJtppX3WLSJ0AFxo0rv0Mrr5knq61blt5sv842z7vOa97pK3x1w+3cdPWDNQzwvI0bvyvjXy2j/B4JzPgR3pfZQh1bH119RhVGQ/J2TKE1EL/CvMge6ZcV2xT7J2MfN5xJ8qMfxoaHoqCmfnNQIDAQAB"
	// 私钥
	AlipayWebPrivateKey string = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCHTLkKxeclhDv9p8/YN3GI1CX7XVETMBvNJniZm6ELh34NQZyVY8jMxuXC87XcO8TfEOlYbnV9otP9FsuXZONrgv1gzMXfv1xEv3CfTh7neGhppY9lmr7Imicp1dOp0Ejl9tNLRIzR0bgc17V1ZGxVNT6+3QZj8qSa4K5RJEq+ZUpoPmWuQlsr3/2kbb67zsIKt9WMm2mlfdYtInQAXGjSu/QyuvmSerrVuW3my/zjbPu85r3ukrfHXD7dx09YM1DPC8jRu/K+NfLaP8HgnM+BHel9lCHVsfXX1GFUZD8nZMoTUQv8K8yB7plxXbFPsnYx83nEnyox/GhoeioKZ+c1AgMBAAECggEAFVtmBi5RG1nxQkwAAghWcpvSVwhW6Ol6Kfr9pHmthS1hF2SY+/fhVXm8APcJaPdCT1fTDbN59lgCkHvNrVsujuUBbAtG3NfPD8cMVwk01aLrEGoi5Y30uaOjtPXc0HxhVXONzN7IP8KUBnGCYkJ020ppWiV3gcVmeFApFAPfjIgjx7hJFxpK+BiwnwrFq85Exg9yU/sa8o1betOW95t+hzrpIhNGtYQJ1pJvD+4v1SrqD/AXi3etFLBEitgV/yGkthCrS1UUgAQBx3vPfgKpIBf4AexHHseuAyMyoGKEktDGYvB+hPF6e+JvCv4OThCfFvJAOgDI6ELI0G6HjGrCAQKBgQDFfrYYsYOZrLb47K9usxI2a87coHEckoPYPM0YvQ9MTIFPvpbZVIjwBM1LoA9JIpOGOLKCA5/nOb5JlJSFTJK83I5JNpRJlR07RIdaU4lALWQJH9VFsWq6xBhAikwH6FORz+N7NcaAOe6iaypzK/yn4kOfTZEnhZI7JwZMpL0WtQKBgQCvYVonVrA0CZll7XuHkz4m5TVtvS+l5ZvuPsCpTsWlpnAV12Nh+SGkixTsvbxWOnvJ7fc4+96VWeODn6VxTMsFiizkGb9cBirDIROeqljx7BYgwjVRtbEzCgEgYQsNRLm8nQmBTDk23ePUisnET4mhSKaLZP1bwkX4nXNe0kVegQKBgB6MN+xf4MQvY+ni6kvMJQAH8upLYtORkhUramnuLGSX3NSR3VFmcvv1MjAFZPfiAkGbmXjkyKICW32UHtjzfCr8ToHikFNFXk0hj3IYQ72Kh9Oled6az7BtznaFLbd3Zx3BsyUGr1p8G35B50IDRsRNCjqky9P6JCFEKwRF4v2VAoGBAIgsaNm+n5XoFilkWdQM6ec9dVy91m9Tdt1n8CQFu8VBFCqBUPNTg2HFc/vV3pR9WHyTvq+KMWt5ngQnQ0hTvheREAYUT9beGvnjht5dmi5Nh2jsIoZNM0dT70f60Z/9ytG+hRDV5F7Lrwjqsnsw3sZSKK53PZmvoLDHYDsxvRcBAoGBALRE+LKb02yeiPvqd1dr7thfhErx1lbCL55Ycfp6SBmuvr0WbboH1FKuZuTzU+3jOxBBwIXVSNNwJT4OZZEtAsCNzHz8QHwVq1I7blwYJnC+KhISL7F3EwZwQuW/2kSbxAMEas9y3hyO3dEilBM+8fW1uizO0NfwlybXvR2aj98M"
)
