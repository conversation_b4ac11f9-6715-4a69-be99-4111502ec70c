package base

import (
	authM "assistant/pods/auth/model"
	userm "assistant/pods/user/model"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
)

type BaseCtx struct {
	Input    *vant2.InputModel // 输入
	Back     *vant2.Back       // 输出
	UserId   int64             // 用户id
	Id       int64             //
	PageNum  int               // 页码
	PageSize int               // 每页条数
	UserInfo *userm.UserModel  // 用户信息
	Token    string            // 令牌
}

func InitBaseCtx(r *ghttp.Request) *BaseCtx {
	d := &BaseCtx{}
	d.Input = vant2.Input(r)
	d.Back = vant2.InitBack(r)
	d.Id = d.Input.GetInt64("id")

	// token 鉴权
	_token := gconv.String(d.Input.Header("Authorization"))
	_debug, _ := g.Cfg().Get(gctx.New(), "server.debug") // 获取配置
	if _token == "" && gconv.Bool(_debug) {
		_token = d.Input.GetString("token")
	}
	if _token == "" {
		d.Back.ApiError(401, "您尚未登录，请登录后再试")
	}
	d.Token = _token
	// 检查Token
	_tokenService := &authM.TokenModel{}
	_userId := _tokenService.CheckToken(_token)
	if _userId == 0 {
		d.Back.ApiError(401, "登录已过期，请重新登录")
	}

	d.UserId = _userId // 当前用户Id

	// 页码
	d.PageNum = d.Input.GetInt("page")
	if d.PageNum < 1 {
		d.PageNum = 1
	}
	d.PageSize = d.Input.GetInt("pageSize")
	// 每页条数
	if d.PageSize < 1 || d.PageSize > 100 {
		d.PageSize = 10
	}

	return d
}
