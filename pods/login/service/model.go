package logins

import "vant2"

// 账号密码登录
type MobilePwdLoginModel struct {
	Mobile  string `json:"mobile"`
	UserPwd string `json:"user_pwd"`
	Code    string `json:"code"` // 可能存在验证码
}

// 加载
func (d *MobilePwdLoginModel) Get() map[string]interface{} {
	return map[string]interface{}{
		"mobile":   d.Mobile,
		"user_pwd": d.UserPwd,
		"code":     d.Code,
	}
}

// 写入
func (d *MobilePwdLoginModel) Set(input *vant2.InputModel) {
	d.Mobile = input.GetString("mobile", "require|mobile", "手机号")
	_pwd := input.GetString("user_pwd", "require|password", "密码")
	d.Code = input.GetString("code")
	d.UserPwd = vant2.Password(_pwd)
}

// 邮箱密码登录模型
type EmailPwdLoginModel struct {
	Email   string `json:"email"`
	UserPwd string `json:"user_pwd"`
	Code    string `json:"code"` // 可能存在验证码
}

// 加载
func (d *EmailPwdLoginModel) Get() map[string]interface{} {
	return map[string]interface{}{
		"email":    d.Email,
		"user_pwd": d.UserPwd,
		"code":     d.Code,
	}
}

// 写入
func (d *EmailPwdLoginModel) Set(input *vant2.InputModel) {
	d.Email = input.GetString("email", "require|email", "邮箱")
	_pwd := input.GetString("user_pwd", "require|password", "密码")
	d.Code = input.GetString("code")
	d.UserPwd = vant2.Password(_pwd)
}
