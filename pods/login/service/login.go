package logins

import (
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/util/gconv"
)

// 手机号 + 密码登录
func MobileLoginWithPwd(args *MobilePwdLoginModel) (string, int64) {
	// 检查手机号
	_info := _GetStatusFromMobile(args.Mobile)
	if _info == nil {
		return "该账户不存在，请注册后登录", 0
	}

	_isDelete := gconv.Int(_info["is_delete"])
	if _isDelete != 0 {
		return "该账户已被禁用，请联系管理员", 0
	}

	// 手机号加验证码登录
	_where := w.Map{
		"mobile":   args.Mobile,
		"user_pwd": args.UserPwd,
	}
	_user := vant2.DB("user").Fields("id").Where(_where).One()
	if _user == nil {
		return "手机号或密码错误", 0
	}

	_userId := gconv.Int64(_user["id"])
	return "", _userId
}

// 手机+验证码实现登录（暂时用不到）
func MobileLoginWithCode(args *MobilePwdLoginModel) (string, int64) {
	// 检查短信验证码
	vant2.AliCode.Check(args.Mobile, args.Code, 0)
	// 检查手机号
	_info := _GetStatusFromMobile(args.Mobile)
	// 账号存在和账号不存在的情况
	if _info == nil {
		// 不存在账号 快速创建
	} else {
		// 存在账号 直接登录
	}

	return "", 0
}

// 检查状态
func _GetStatusFromMobile(mobile string) gdb.Record {
	_where := w.Map{"mobile": mobile}
	_info := vant2.DB("user").Where(_where).Fields("is_delete").One()
	return _info
}

// 检查邮箱状态
func _GetStatusFromEmail(email string) gdb.Record {
	_where := w.Map{"email": email}
	_info := vant2.DB("user").Where(_where).Fields("is_delete").One()
	return _info
}

// 邮箱 + 密码登录
func EmailLoginWithPwd(args *EmailPwdLoginModel) (string, int64) {
	// 检查邮箱
	_info := _GetStatusFromEmail(args.Email)
	if _info == nil {
		return "该邮箱账户不存在，请注册后登录", 0
	}

	_isDelete := gconv.Int(_info["is_delete"])
	if _isDelete != 0 {
		return "该账户已被禁用，请联系管理员", 0
	}

	// 邮箱加密码登录
	_where := w.Map{
		"email":    args.Email,
		"user_pwd": args.UserPwd,
	}
	_user := vant2.DB("user").Fields("id").Where(_where).One()
	if _user == nil {
		return "邮箱或密码错误", 0
	}

	_userId := gconv.Int64(_user["id"])
	return "", _userId
}

// 邮箱+验证码实现登录
func EmailLoginWithCode(args *EmailPwdLoginModel) (string, int64) {
	// 检查邮箱验证码
	if !vant2.Exmail.Check(args.Email, args.Code) {
		return "验证码错误", 0
	}

	// 检查邮箱
	_info := _GetStatusFromEmail(args.Email)
	// 账号存在和账号不存在的情况
	if _info == nil {
		// 不存在账号 快速创建
		_data := w.Map{
			"email":       args.Email,
			"time_create": vant2.Time(),
			"user_nick":   "用户" + vant2.Captcha(5),
		}
		_userId := vant2.DB("user").Data(_data).AddGetId()
		if _userId > 0 {
			return "", gconv.Int64(_userId)
		} else {
			return "账户创建失败", 0
		}
	} else {
		// 存在账号 直接登录
		_isDelete := gconv.Int(_info["is_delete"])
		if _isDelete != 0 {
			return "该账户已被禁用，请联系管理员", 0
		}
		_userId := gconv.Int64(_info["id"])
		return "", _userId
	}
}
