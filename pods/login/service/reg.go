package logins

import (
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

type RegModel struct {
	Mobile   string `json:"mobile"`    // 手机号
	Code     string `json:"code"`      // 验证码
	UserPwd  string `json:"user_pwd"`  // 密码
	CheckPwd string `json:"check_pwd"` // 确认密码
	IP       string `json:"ip"`        //
}

// 邮箱注册模型
type EmailRegModel struct {
	Email    string `json:"email"`     // 邮箱
	Code     string `json:"code"`      // 验证码
	UserPwd  string `json:"user_pwd"`  // 密码
	CheckPwd string `json:"check_pwd"` // 确认密码
	IP       string `json:"ip"`        //
}

// 注册
func (d *RegModel) RegMobileCode() (string, int64) {
	// 先检查手机号
	_v := vant2.VantVali
	if !_v.CheckMobile(d.Mobile) {
		return "手机号码格式错误", 0
	}
	// 检查验证码
	if !_v.CheckCode(d.Code) {
		return "验证码格式错误", 0
	}

	// 检查密码
	if !_v.CheckPassword(d.UserPwd) {
		return "密码格式错误", 0
	}

	// 检查确认密码
	if d.UserPwd != d.CheckPwd {
		return "两次密码不一致", 0
	}

	if !vant2.AliCode.Check(d.Mobile, d.Code, 0) {
		return "验证码错误", 0
	}

	// 检查手机号是否存在
	_where := w.Map{
		"mobile": d.Mobile,
	}
	_info := vant2.DB("user").Where(_where).Fields("id,is_delete").One()
	_pwd := vant2.Password(d.UserPwd)
	if _info != nil { // 已有数据 直接修改密码
		if gconv.Int(_info["is_delete"]) < 0 {
			return "账户异常，请联系管理人员", 0
		}
		_er := vant2.DB("user").Where(_where).Data(w.Map{"user_pwd": _pwd}).Update()
		if _er {
			return "", gconv.Int64(_info["id"])
		} else {
			return "重置密码失败", 0
		}
	} else { // 未有数据 创建账户并设置密码
		_data := w.Map{
			"mobile":      d.Mobile,
			"user_pwd":    _pwd,
			"time_create": vant2.Time(),
			"ip_create":   d.IP,
			"user_nick":   "用户" + vant2.Captcha(5),
		}
		_userId := vant2.DB("user").Data(_data).AddGetId()
		if _userId > 0 {
			return "", gconv.Int64(_userId)
		} else {
			return "注册失败", 0
		}
	}
}

// 邮箱注册
func (d *EmailRegModel) RegEmailCode() (string, int64) {
	// 先检查邮箱
	_v := vant2.VantVali
	if !_v.CheckEmail(d.Email) {
		return "邮箱格式错误", 0
	}
	// 检查验证码
	if !_v.CheckCode(d.Code) {
		return "验证码格式错误", 0
	}

	// 检查密码
	if !_v.CheckPassword(d.UserPwd) {
		return "密码格式错误", 0
	}

	// 检查确认密码
	if d.UserPwd != d.CheckPwd {
		return "两次密码不一致", 0
	}

	if !vant2.Exmail.Check(d.Email, d.Code, 1) {
		return "验证码错误", 0
	}

	// 检查邮箱是否存在
	_where := w.Map{
		"email": d.Email,
	}
	_info := vant2.DB("user").Where(_where).Fields("id,is_delete").One()
	_pwd := vant2.Password(d.UserPwd)
	if _info != nil { // 已有数据 直接修改密码
		if gconv.Int(_info["is_delete"]) < 0 {
			return "账户异常，请联系管理人员", 0
		}
		_er := vant2.DB("user").Where(_where).Data(w.Map{"user_pwd": _pwd}).Update()
		if _er {
			return "", gconv.Int64(_info["id"])
		} else {
			return "重置密码失败", 0
		}
	} else { // 未有数据 创建账户并设置密码
		_data := w.Map{
			"email":       d.Email,
			"user_pwd":    _pwd,
			"time_create": vant2.Time(),
			"ip_create":   d.IP,
			"user_nick":   "用户" + vant2.Captcha(5),
		}
		_userId := vant2.DB("user").Data(_data).AddGetId()
		if _userId > 0 {
			return "", gconv.Int64(_userId)
		} else {
			return "注册失败", 0
		}
	}
}
