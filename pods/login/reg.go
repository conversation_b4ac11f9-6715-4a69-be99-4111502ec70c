package login

import (
	loginm "assistant/pods/login/model"
	logins "assistant/pods/login/service"
	"vant2"

	"github.com/gogf/gf/v2/net/ghttp"
)

// 短信验证码实现登录
// 针对aigc的demo演示区域
var RegCtx = func() *_reg {
	return &_reg{}
}()

type _reg struct {
	Input                 *vant2.InputModel
	Back                  *vant2.Back
	SMSVerificationCode   int64  // 短信验证码
	ImageVerificationCode string // 图片验证码
	Mobile                string // 手机号

}

// 注册初始化
func (d *_reg) _baseModel(r *ghttp.Request) {
	d.Input = vant2.Input(r)
	d.Back = vant2.InitBack(r)
}

// 用户注册/找回密码 - 根据字段判断邮箱或手机号
func (d *_reg) UserReg(r *ghttp.Request) {
	d._baseModel(r) // 初始化

	_code := d.Input.GetString("code", "required", "验证码")
	_pwd := d.Input.GetString("user_pwd", "required", "密码")
	_checkPwd := d.Input.GetString("check_pwd", "required", "确认密码")
	_ip := d.Input.IP()

	var _er string
	// 根据提交的字段判断是邮箱还是手机号
	_email := d.Input.GetString("email")
	_mobile := d.Input.GetString("mobile")

	if _email != "" {
		// 邮箱注册
		_s := &logins.EmailRegModel{
			Email:    _email,
			Code:     _code,
			UserPwd:  _pwd,
			CheckPwd: _checkPwd,
			IP:       _ip,
		}
		_er, _ = _s.RegEmailCode()
	} else if _mobile != "" {
		// 手机号注册
		_s := &loginm.RegModel{
			Mobile:   _mobile,
			Code:     _code,
			UserPwd:  _pwd,
			CheckPwd: _checkPwd,
			IP:       _ip,
		}
		_er, _ = _s.RegMobileCode()
	} else {
		d.Back.ApiError(-507, "请提供手机号或邮箱")
		return
	}

	if _er != "" {
		d.Back.ApiError(-507, _er)
	} else {
		d.Back.ApiSuccess("注册/重置成功，请重新登录")
	}
}
