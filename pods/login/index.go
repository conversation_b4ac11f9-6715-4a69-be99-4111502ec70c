package login

import (
	auths "assistant/pods/auth/service"
	logins "assistant/pods/login/service"
	userm "assistant/pods/user/model"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/net/ghttp"
)

var LoginCtx = func() *_login {
	return &_login{}
}()

type _login struct {
	// *base.BaseCtx
	UserId   int64
	Input    *vant2.InputModel
	Back     *vant2.Back
	UserInfo *userm.UserModel
	Token    string
}

func (d *_login) _baseModel(r *ghttp.Request) {
	d.Input = vant2.Input(r)
	d.Back = vant2.InitBack(r)
}

// 用户登录 - 根据字段判断邮箱或手机号
func (d *_login) UserLogin(r *ghttp.Request) {
	d._baseModel(r)

	_pwd := d.Input.GetString("user_pwd", "required|password", "密码")
	_deviceType := d.Input.GetString("device_type") // 获取设备类型参数，可选
	var _error string
	var _userId int64

	// 根据提交的字段判断是邮箱还是手机号
	_email := d.Input.GetString("email")
	_mobile := d.Input.GetString("mobile")

	if _email != "" {
		// 邮箱登录
		_model := &logins.EmailPwdLoginModel{
			Email:   _email,
			UserPwd: vant2.Password(_pwd),
		}
		_error, _userId = logins.EmailLoginWithPwd(_model)
	} else if _mobile != "" {
		// 手机号登录
		_model := &logins.MobilePwdLoginModel{
			Mobile:  _mobile,
			UserPwd: vant2.Password(_pwd),
		}
		_error, _userId = logins.MobileLoginWithPwd(_model)
	} else {
		d.Back.ApiError(1, "请提供手机号或邮箱")
		return
	}

	if _error != "" {
		d.Back.ApiError(1, _error)
	} else {
		_token := auths.TokenFromUserId(_userId, _deviceType)
		d.Back.ApiSuccess("登录成功", w.Map{
			"token": _token,
		})
	}
}

// 发送验证码
func (d *_login) SendCode(r *ghttp.Request) {
	d._baseModel(r)
	_mobile := d.Input.GetString("mobile", "required|mobile", "手机号")
	d.Input.CheckHeadAuth() //  检查header 防止瘪犊子
	// 检查来路ip
	_ip := r.GetClientIp()
	vant2.Console(_ip)
	// 必须本地ip
	if _ip != "127.0.0.1" && _ip != "::1" && _ip != "**************" {
		d.Back.ApiError(-1, "很抱歉，项目正在开发中，暂时非本地用户注册")
		return
	}

	if vant2.AliCode.Send(_mobile) {
		d.Back.ApiSuccess("验证码发送成功")
	} else {
		d.Back.ApiError(-1, "验证码发送失败")
	}
}

// 发送验证码
func (d *_login) SendEmailCode(r *ghttp.Request) {
	d._baseModel(r)
	_email := d.Input.GetString("email", "required|email", "邮箱")
	// d.Input.CheckHeadAuth() //  检查header 防止瘪犊子
	// 检查来路ip
	_ip := r.GetClientIp()
	vant2.Console(_ip)
	// 必须本地ip
	if _ip != "127.0.0.1" && _ip != "::1" && _ip != "**************" {
		d.Back.ApiError(-1, "很抱歉，项目正在开发中，暂时非本地用户注册")
		return
	}

	if vant2.Exmail.Send(_email) {
		d.Back.ApiSuccess("验证码发送成功")
	} else {
		d.Back.ApiError(-1, "验证码发送失败")
	}
}

// 检查验证码 - 根据字段判断邮箱或手机号
func (d *_login) CheckCode(r *ghttp.Request) {
	d._baseModel(r)
	_code := d.Input.GetString("code", "required", "验证码")

	var _result bool
	// 根据提交的字段判断是邮箱还是手机号
	_email := d.Input.GetString("email")
	_mobile := d.Input.GetString("mobile")

	if _email != "" {
		// 邮箱验证码
		_result = vant2.Exmail.Check(_email, _code)
	} else if _mobile != "" {
		// 手机验证码
		_result = vant2.AliCode.Check(_mobile, _code)
	} else {
		d.Back.ApiError(-1, "请提供手机号或邮箱")
		return
	}

	if _result {
		d.Back.ApiSuccess("验证码验证成功")
	} else {
		d.Back.ApiError(-1, "验证码验证失败")
	}
}
