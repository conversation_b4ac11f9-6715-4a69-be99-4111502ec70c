package memory

import (
	"assistant/pods/base"
	"assistant/pods/memory/memoryservice"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// DataSave 保存记忆碎片（新增或编辑）
func DataSave(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	content := gconv.String(_base.Input.Value("content"))
	categoryId := _base.Input.Value("categoryId")
	tagsStr := gconv.String(_base.Input.Value("tags"))
	imagesStr := gconv.String(_base.Input.Value("images"))

	if content == "" {
		_base.Back.ApiError(-1, "参数错误")
		return
	}

	// 处理标签数组
	var tags []string
	if tagsStr != "" {
		tags = strings.Split(tagsStr, ",")
	}

	// 处理图片数组
	var images []string
	if imagesStr != "" {
		images = strings.Split(imagesStr, ",")
		// 去除空字符串
		var validImages []string
		for _, img := range images {
			if strings.TrimSpace(img) != "" {
				validImages = append(validImages, strings.TrimSpace(img))
			}
		}
		images = validImages
	}

	memoryDataService := &memoryservice.MemoryData{}
	result := memoryDataService.Save(_base.UserId, _base.Id, content, categoryId, tags, images)
	_base.Back.ApiBack("保存记忆碎片", result)
}

// DataDelete 删除记忆碎片
func DataDelete(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误")
		return
	}

	memoryDataService := &memoryservice.MemoryData{}
	result := memoryDataService.Delete(_base.UserId, _base.Id)
	_base.Back.ApiBack("删除记忆碎片", result)
}

// DataGet 获取记忆碎片详情
func DataGet(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误")
		return
	}

	memoryDataService := &memoryservice.MemoryData{}
	data := memoryDataService.Get(_base.UserId, _base.Id)
	if len(data) == 0 {
		_base.Back.ApiError(-2, "记忆碎片不存在")
		return
	}

	_base.Back.ApiSuccess("获取记忆碎片详情成功", _base.Back.ToSnake(data))
}

// DataList 获取记忆碎片列表
func DataList(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	categoryId := _base.Input.Value("categoryId")
	keyword := gconv.String(_base.Input.Value("keyword"))
	tagsStr := gconv.String(_base.Input.Value("tags"))

	// 处理标签字符串数组，转换为tag ID数组
	var tagIds []interface{}
	if tagsStr != "" {
		tagStrs := strings.Split(tagsStr, ",")
		var tagNames []string

		for _, tagStr := range tagStrs {
			tagName := strings.TrimSpace(tagStr)
			if tagName != "" {
				tagNames = append(tagNames, tagName)
			}
		}

		if len(tagNames) > 0 {
			tagService := &memoryservice.Tag{}
			tagIds = tagService.GetIdsByNames(_base.UserId, tagNames)
		}
	}

	memoryDataService := &memoryservice.MemoryData{}
	list, total := memoryDataService.List(_base.UserId, _base.PageNum, _base.PageSize, categoryId, tagIds, keyword)
	_base.Back.ApiRows(_base.Back.ToSnake(list), total)
}

// Statistics 获取统计信息
func Statistics(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	categoryService := &memoryservice.Category{}
	tagService := &memoryservice.Tag{}
	memoryDataService := &memoryservice.MemoryData{}

	categoryCount := categoryService.Count(_base.UserId)
	tagCount := tagService.Count(_base.UserId)
	dataCount := memoryDataService.Count(_base.UserId, 0, []interface{}{})

	stats := g.Map{
		"categoryCount": categoryCount,
		"tagCount":      tagCount,
		"dataCount":     dataCount,
	}

	_base.Back.ApiSuccess("获取统计信息成功", stats)
}
