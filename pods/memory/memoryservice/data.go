package memoryservice

import (
	"encoding/json"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// MemoryData 记忆数据服务
type MemoryData struct{}

// Save 保存记忆碎片（新增或编辑）
func (md *MemoryData) Save(userId interface{}, dataId interface{}, content string, categoryId interface{}, tags []string, images []string) interface{} {
	content = strings.TrimSpace(content)

	if content == "" {
		return false
	}

	id := gconv.Int(dataId)

	// 处理分类ID
	finalCategoryId := gconv.Int(categoryId)
	if finalCategoryId <= 0 {
		// 获取或创建默认分类
		categoryService := &Category{}
		finalCategoryId = categoryService.GetDefaultId(userId)
	}

	// 处理图片数组，转换为JSON字符串
	imagesJson := ""
	if len(images) > 0 {
		if imagesBytes, err := json.Marshal(images); err == nil {
			imagesJson = string(imagesBytes)
		}
	}

	// 新增
	if id == 0 {
		// 添加记忆碎片数据
		newDataId := vant2.DB("memory_data").AddGetId(g.Map{
			"user_id":     userId,
			"category_id": finalCategoryId,
			"content":     content,
			"images":      imagesJson,
			"time_create": vant2.Time(),
			"time_update": vant2.Time(),
			"is_delete":   0,
		})

		if newDataId > 0 {
			// 处理标签关联
			md._saveTags(newDataId, userId, tags)
		}

		return newDataId
	}

	// 编辑
	result := vant2.DB("memory_data").
		Where("id", id).
		Where("user_id", userId).
		Data(g.Map{
			"category_id": finalCategoryId,
			"content":     content,
			"images":      imagesJson,
			"time_update": vant2.Time(),
		}).Update()

	if result {
		// 重新处理标签关联
		md._saveTags(id, userId, tags)
	}

	return result
}

// Delete 删除记忆碎片（软删除）
func (md *MemoryData) Delete(userId interface{}, dataId interface{}) bool {
	return vant2.DB("memory_data").
		Where("id", dataId).
		Where("user_id", userId).
		Data(g.Map{
			"is_delete":   1,
			"time_update": vant2.Time(),
		}).Update()
}

// _saveTags 保存记忆碎片的标签关联（私有方法）
func (md *MemoryData) _saveTags(dataId interface{}, userId interface{}, tags []string) {
	// 先删除原有关联（使用硬删除，因为memory_data_tag表没有is_delete字段）
	vant2.DB("memory_data_tag").Where("data_id", dataId).Delete()

	if len(tags) == 0 {
		return
	}

	// 处理新标签
	tagService := &Tag{}
	for _, tagName := range tags {
		tagName = strings.TrimSpace(tagName)
		if tagName == "" {
			continue
		}

		// 获取或创建标签
		tagId := tagService.GetOrCreate(userId, tagName)
		if tagId > 0 {
			// 创建关联
			vant2.DB("memory_data_tag").Add(g.Map{
				"data_id":     dataId,
				"tag_id":      tagId,
				"time_create": vant2.Time(),
			})
		}
	}
}
