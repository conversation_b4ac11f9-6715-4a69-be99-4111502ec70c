package memoryservice

import (
	"encoding/json"
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Get 获取单个记忆碎片详情
func (md *MemoryData) Get(userId interface{}, dataId interface{}) g.Map {
	// 获取基础数据
	data := vant2.DB("memory_data d").
		LeftJoin("memory_category c", "d.category_id = c.id").
		Where("d.id", dataId).
		Where("d.user_id", userId).
		Where("d.is_delete", 0).
		Fields("d.id,d.title,d.content,d.images,d.category_id,d.time_create,d.time_update,c.name as category_name,c.color as category_color,c.icon as category_icon").
		One()

	if len(data) == 0 {
		return g.Map{}
	}

	// 获取标签信息
	tags := md._getTags(dataId)
	result := gconv.Map(data)
	result["tags"] = tags

	// 处理图片数组
	if imagesStr := gconv.String(result["images"]); imagesStr != "" {
		var images []string
		if err := json.Unmarshal([]byte(imagesStr), &images); err == nil {
			result["images"] = images
		} else {
			result["images"] = []string{}
		}
	} else {
		result["images"] = []string{}
	}

	return result
}

// List 获取记忆碎片列表
func (md *MemoryData) List(userId interface{}, page int, pageSize int, categoryId interface{}, tagIds []interface{}, keyword string) ([]g.Map, int) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 构建基础查询
	db := vant2.DB("memory_data d")

	// 如果没有传入分类ID，需要左连接分类表获取分类名称
	if gconv.Int(categoryId) <= 0 {
		db = db.LeftJoin("memory_category c", "d.category_id = c.id AND c.is_delete = 0").
			Fields("d.id,d.title,d.content,d.images,d.category_id,d.time_create,d.time_update,c.name as category_name,c.color as category_color,c.icon as category_icon")
	} else {
		db = db.Fields("d.id,d.title,d.content,d.images,d.category_id,d.time_create,d.time_update")
	}

	// 基础条件
	db = db.Where("d.user_id", userId).Where("d.is_delete", 0)

	// 分类筛选
	if gconv.Int(categoryId) > 0 {
		db = db.Where("d.category_id", categoryId)
	}

	// 标签筛选
	if len(tagIds) > 0 {
		db = db.InnerJoin("memory_data_tag dt", "d.id = dt.data_id").
			Where("dt.tag_id", tagIds).
			Group("d.id")
	}

	// 关键词搜索
	if strings.TrimSpace(keyword) != "" {
		db = db.WhereLike("d.title|d.content", strings.TrimSpace(keyword))
	}

	// 排序和分页
	db = db.Order("d.id DESC")

	// 获取数据和总数
	rawList, total := db.Paginate(page, pageSize)

	// 转换为 []g.Map 格式
	var list []g.Map
	for _, item := range rawList {
		record := gconv.Map(item)
		list = append(list, record)
	}

	// 如果传入了分类ID，需要补充分类名称
	if gconv.Int(categoryId) > 0 && len(list) > 0 {
		categoryService := &Category{}
		categoryInfo := categoryService.Get(userId, categoryId)
		categoryName := ""
		categoryColor := ""
		categoryIcon := ""
		if len(gconv.Map(categoryInfo)) > 0 {
			categoryName = gconv.String(gconv.Map(categoryInfo)["name"])
			categoryColor = gconv.String(gconv.Map(categoryInfo)["color"])
			categoryIcon = gconv.String(gconv.Map(categoryInfo)["icon"])
		}

		for i := range list {
			list[i]["category_name"] = categoryName
			list[i]["category_color"] = categoryColor
			list[i]["category_icon"] = categoryIcon
		}
	}

	// 补充标签信息和处理图片数组
	if len(list) > 0 {
		var dataIds []interface{}
		for _, item := range list {
			dataIds = append(dataIds, item["id"])
		}

		tagsMap := md._getTagsMap(dataIds)
		for i := range list {
			dataId := gconv.String(list[i]["id"])
			if tags, exists := tagsMap[dataId]; exists {
				list[i]["tags"] = tags
			} else {
				list[i]["tags"] = []interface{}{}
			}

			// 处理图片数组
			if imagesStr := gconv.String(list[i]["images"]); imagesStr != "" {
				var images []string
				if err := json.Unmarshal([]byte(imagesStr), &images); err == nil {
					list[i]["images"] = images
				} else {
					list[i]["images"] = []string{}
				}
			} else {
				list[i]["images"] = []string{}
			}
		}
	}

	return list, total
}

// _getTags 获取记忆碎片的标签列表（私有方法）
func (md *MemoryData) _getTags(dataId interface{}) []g.Map {
	tags := vant2.DB("memory_data_tag dt").
		InnerJoin("memory_tag t", "dt.tag_id = t.id AND t.is_delete = 0").
		Where("dt.data_id", dataId).
		Fields("t.id,t.name,t.color").
		Order("dt.id ASC").
		Select()

	result := make([]g.Map, 0)
	for _, tag := range tags {
		result = append(result, g.Map{
			"id":    tag["id"],
			"name":  tag["name"],
			"color": tag["color"],
		})
	}

	return result
}

// _getTagsMap 批量获取记忆碎片的标签映射（私有方法）
func (md *MemoryData) _getTagsMap(dataIds []interface{}) map[string][]g.Map {
	if len(dataIds) == 0 {
		return map[string][]g.Map{}
	}

	tags := vant2.DB("memory_data_tag dt").
		InnerJoin("memory_tag t", "dt.tag_id = t.id AND t.is_delete = 0").
		Where("dt.data_id", dataIds).
		Fields("dt.data_id,t.id,t.name,t.color").
		Order("dt.data_id ASC,dt.id ASC").
		Select()

	result := make(map[string][]g.Map)
	for _, tag := range tags {
		dataId := gconv.String(tag["data_id"])
		if result[dataId] == nil {
			result[dataId] = make([]g.Map, 0)
		}
		result[dataId] = append(result[dataId], g.Map{
			"id":    tag["id"],
			"name":  tag["name"],
			"color": tag["color"],
		})
	}

	return result
}

// Count 获取记忆碎片数量
func (md *MemoryData) Count(userId interface{}, categoryId interface{}, tagIds []interface{}) int {
	db := vant2.DB("memory_data d").
		Where("d.user_id", userId).
		Where("d.is_delete", 0)

	// 分类筛选
	if gconv.Int(categoryId) > 0 {
		db = db.Where("d.category_id", categoryId)
	}

	// 标签筛选
	if len(tagIds) > 0 {
		db = db.InnerJoin("memory_data_tag dt", "d.id = dt.data_id").
			Where("dt.tag_id", tagIds).
			Group("d.id")
	}

	return db.Count()
}

// Search 高级搜索功能（扩展功能，为未来迭代准备）
func (md *MemoryData) Search(userId interface{}, searchParams g.Map) ([]g.Map, int) {
	page := gconv.Int(searchParams["page"])
	pageSize := gconv.Int(searchParams["page_size"])
	categoryId := searchParams["category_id"]
	keyword := gconv.String(searchParams["keyword"])
	tagIds := gconv.SliceAny(searchParams["tag_ids"])
	startTime := gconv.Int(searchParams["start_time"])
	endTime := gconv.Int(searchParams["end_time"])
	orderBy := gconv.String(searchParams["order_by"])

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 构建查询
	db := vant2.DB("memory_data d").
		LeftJoin("memory_category c", "d.category_id = c.id AND c.is_delete = 0").
		Fields("d.id,d.title,d.content,d.images,d.category_id,d.time_create,d.time_update,c.name as category_name,c.color as category_color,c.icon as category_icon").
		Where("d.user_id", userId).
		Where("d.is_delete", 0)

	// 分类筛选
	if gconv.Int(categoryId) > 0 {
		db = db.Where("d.category_id", categoryId)
	}

	// 标签筛选
	if len(tagIds) > 0 {
		db = db.InnerJoin("memory_data_tag dt", "d.id = dt.data_id").
			Where("dt.tag_id", tagIds).
			Group("d.id")
	}

	// 关键词搜索
	if strings.TrimSpace(keyword) != "" {
		db = db.WhereLike("d.title|d.content", strings.TrimSpace(keyword))
	}

	// 时间范围筛选
	if startTime > 0 {
		db = db.Where("d.time_create >= ?", startTime)
	}
	if endTime > 0 {
		db = db.Where("d.time_create <= ?", endTime)
	}

	// 排序
	switch orderBy {
	case "time_asc":
		db = db.Order("d.time_create ASC")
	case "time_desc":
		db = db.Order("d.time_create DESC")
	case "update_asc":
		db = db.Order("d.time_update ASC")
	case "update_desc":
		db = db.Order("d.time_update DESC")
	default:
		db = db.Order("d.id DESC")
	}

	// 获取数据和总数
	rawList, total := db.Paginate(page, pageSize)

	// 转换格式并补充标签信息
	var list []g.Map
	for _, item := range rawList {
		record := gconv.Map(item)
		list = append(list, record)
	}

	// 补充标签信息和处理图片数组
	if len(list) > 0 {
		var dataIds []interface{}
		for _, item := range list {
			dataIds = append(dataIds, item["id"])
		}

		tagsMap := md._getTagsMap(dataIds)
		for i := range list {
			dataId := gconv.String(list[i]["id"])
			if tags, exists := tagsMap[dataId]; exists {
				list[i]["tags"] = tags
			} else {
				list[i]["tags"] = []interface{}{}
			}

			// 处理图片数组
			if imagesStr := gconv.String(list[i]["images"]); imagesStr != "" {
				var images []string
				if err := json.Unmarshal([]byte(imagesStr), &images); err == nil {
					list[i]["images"] = images
				} else {
					list[i]["images"] = []string{}
				}
			} else {
				list[i]["images"] = []string{}
			}
		}
	}

	return list, total
}

// GetRecentList 获取最近的记忆碎片（扩展功能）
func (md *MemoryData) GetRecentList(userId interface{}, limit int) []g.Map {
	if limit <= 0 {
		limit = 10
	}

	rawList := vant2.DB("memory_data d").
		LeftJoin("memory_category c", "d.category_id = c.id AND c.is_delete = 0").
		Where("d.user_id", userId).
		Where("d.is_delete", 0).
		Fields("d.id,d.title,d.images,d.category_id,d.time_create,c.name as category_name,c.color as category_color,c.icon as category_icon").
		Order("d.time_create DESC").
		Select()

	var list []g.Map
	for _, item := range rawList {
		if len(list) >= limit {
			break
		}
		record := gconv.Map(item)

		// 处理图片数组
		if imagesStr := gconv.String(record["images"]); imagesStr != "" {
			var images []string
			if err := json.Unmarshal([]byte(imagesStr), &images); err == nil {
				record["images"] = images
			} else {
				record["images"] = []string{}
			}
		} else {
			record["images"] = []string{}
		}

		list = append(list, record)
	}

	return list
}
