package memoryservice

import (
	"strings"
	"vant2"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

// Tag 标签服务
type Tag struct{}

// Save 保存标签（新增或编辑）
func (t *Tag) Save(userId interface{}, tagId interface{}, name string, color string) interface{} {
	name = strings.TrimSpace(name)
	color = strings.TrimSpace(color)

	if name == "" {
		return false
	}

	// 设置默认颜色
	if color == "" {
		color = "success"
	}

	id := gconv.Int(tagId)

	// 检查标签名称是否重复
	db := vant2.DB("memory_tag").
		Where("user_id", userId).
		Where("name", name).
		Where("is_delete", 0)

	// 如果是编辑，排除自己
	if id > 0 {
		db = db.Where("id !=", id)
	}

	exists := db.Count()
	if exists > 0 {
		return false
	}

	// 新增
	if id == 0 {
		return vant2.DB("memory_tag").AddGetId(g.Map{
			"user_id":     userId,
			"name":        name,
			"color":       color,
			"time_create": vant2.Time(),
			"time_update": vant2.Time(),
			"is_delete":   0,
		})
	}

	// 编辑
	result := vant2.DB("memory_tag").
		Where("id", id).
		Where("user_id", userId).
		Data(g.Map{
			"name":        name,
			"color":       color,
			"time_update": vant2.Time(),
		}).Update()

	return result
}

// Delete 删除标签（软删除）
func (t *Tag) Delete(userId interface{}, tagId interface{}) bool {
	return vant2.DB("memory_tag").
		Where("id", tagId).
		Where("user_id", userId).
		Data(g.Map{
			"is_delete":   1,
			"time_update": vant2.Time(),
		}).Update()
}

// List 获取标签列表
func (t *Tag) List(userId interface{}) interface{} {
	return vant2.DB("memory_tag").
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("id,name,color,time_create").
		Order("id DESC").
		Select()
}

// Get 获取单个标签信息
func (t *Tag) Get(userId interface{}, tagId interface{}) interface{} {
	return vant2.DB("memory_tag").
		Where("id", tagId).
		Where("user_id", userId).
		Where("is_delete", 0).
		Fields("id,name,color,time_create").
		One()
}

// GetOrCreate 获取或创建标签
func (t *Tag) GetOrCreate(userId interface{}, name string) int {
	name = strings.TrimSpace(name)
	if name == "" {
		return 0
	}

	// 先检查是否已存在
	tag := vant2.DB("memory_tag").
		Where("user_id", userId).
		Where("name", name).
		Where("is_delete", 0).
		One()

	if len(tag) > 0 {
		return gconv.Int(tag["id"])
	}

	// 创建新标签（使用默认颜色）
	tagId := vant2.DB("memory_tag").AddGetId(g.Map{
		"user_id":     userId,
		"name":        name,
		"color":       "success",
		"time_create": vant2.Time(),
		"time_update": vant2.Time(),
		"is_delete":   0,
	})

	return tagId
}

// GetByIds 根据ID数组获取标签列表
func (t *Tag) GetByIds(userId interface{}, tagIds []interface{}) interface{} {
	if len(tagIds) == 0 {
		return g.Map{}
	}

	return vant2.DB("memory_tag").
		Where("user_id", userId).
		Where("id", tagIds).
		Where("is_delete", 0).
		Fields("id,name,color").
		Select()
}

// Count 获取标签数量
func (t *Tag) Count(userId interface{}) int {
	return vant2.DB("memory_tag").
		Where("user_id", userId).
		Where("is_delete", 0).
		Count()
}

// GetIdsByNames 根据标签名称数组获取对应的ID数组
func (t *Tag) GetIdsByNames(userId interface{}, tagNames []string) []interface{} {
	if len(tagNames) == 0 {
		return []interface{}{}
	}

	// 查询匹配的标签
	tags := vant2.DB("memory_tag").
		Where("user_id", userId).
		Where("name", tagNames).
		Where("is_delete", 0).
		Fields("id").
		Select()

	var tagIds []interface{}
	for _, tag := range tags {
		tagIds = append(tagIds, tag["id"])
	}

	return tagIds
}

// Search 搜索标签
func (t *Tag) Search(userId interface{}, keyword string) interface{} {
	keyword = strings.TrimSpace(keyword)
	if keyword == "" {
		return t.List(userId)
	}

	return vant2.DB("memory_tag").
		Where("user_id", userId).
		WhereLike("name", keyword).
		Where("is_delete", 0).
		Fields("id,name,color,time_create").
		Order("id DESC").
		Select()
}
