package memory

import (
	"assistant/pods/base"
	"assistant/pods/memory/memoryservice"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/util/gconv"
)

// TagSave 保存标签（新增或编辑）
func TagSave(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	name := _base.Input.Value("name")
	color := gconv.String(_base.Input.Value("color"))

	if name == "" {
		_base.Back.ApiError(-1, "参数错误")
		return
	}

	tagService := &memoryservice.Tag{}
	result := tagService.Save(_base.UserId, _base.Id, gconv.String(name), color)
	_base.Back.ApiBack("保存标签", result)
}

// TagDelete 删除标签
func TagDelete(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	if _base.Id == 0 {
		_base.Back.ApiError(-1, "参数错误")
		return
	}

	tagService := &memoryservice.Tag{}
	result := tagService.Delete(_base.UserId, _base.Id)
	_base.Back.ApiBack("删除标签", result)
}

// TagList 获取标签列表
func TagList(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	tagService := &memoryservice.Tag{}
	list := tagService.List(_base.UserId)
	_base.Back.ApiSuccess("获取标签列表成功", _base.Back.ToSnake(list))
}

// TagSearch 搜索标签
func TagSearch(r *ghttp.Request) {
	_base := base.InitBaseCtx(r)

	keyword := gconv.String(_base.Input.Value("keyword"))

	tagService := &memoryservice.Tag{}
	list := tagService.Search(_base.UserId, keyword)
	_base.Back.ApiSuccess("搜索标签成功", _base.Back.ToSnake(list))
}
