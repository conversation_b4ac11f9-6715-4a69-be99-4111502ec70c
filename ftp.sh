#!/usr/bin/expect -f
###
 # @Description: 项目特定部署配置文件
 # @Author: 徐静(<EMAIL>)
 # @Date: 2025-07-31
 # @LastEditTime: 2025-08-01 16:14:44
 # @Copyright: (c) 2011-2025 http://www.winksoft.net All rights reserved.
###

# ========================================
# 项目特定配置参数 - 请根据实际项目修改
# ========================================

# 项目基本信息
set PROJECT_NAME "assistant"                           ; # 项目名称
set FILENAME "assistant"                               ; # 服务名称（可执行文件名）
set LOCAL_FILE "./linux_amd64/assistant"               ; # 本地打包文件路径
set TARGET_PORT "8808"                              ; # 目标端口号

# SSH 服务器信息
set SERVER "*************"                        ; # 服务器地址
set USERNAME "root"                                 ; # 用户名
set PASSWORD "Xuweizhi520@"                       ; # 密码
set REMOTE_DIR "/www/wwwroot/assistant"                ; # 远程服务器目标路径

# 额外上传目录列表（可选）
set EXTRA_DIRS [list "manifest" "resource" "runtime" "static" "public" "prompt"]

# 超时时间设置
set timeout 600                                     ; # 超时时间（秒）

# 统计变量初始化
set start_time [clock seconds]
set transfer_start_time 0
set peak_speed 0
set peak_speed_unit "MB/s"

# ========================================
# 引入核心部署脚本
# ========================================

# 尝试多种方式找到并引入 goftp.sh 脚本
proc find_and_source_goftp {} {
    # 方法1: 尝试使用写死的路径
    set fixed_path "/Users/<USER>/Desktop/static/rules/goftp.sh"
    if {[file exists $fixed_path] && [file readable $fixed_path]} {
        puts ">> 使用固定路径: $fixed_path"
        source $fixed_path
        return 1
    }

    # 如果都找不到，输出错误信息
    puts "❌ 错误: 无法找到 goftp.sh 脚本文件"
    puts "请确保文件存在于以下任一位置："
    puts "1. /Users/<USER>/Desktop/static/rules/goftp.sh"
    puts "2. 当前脚本目录"
    puts "3. ./ftp/ 目录"
    exit 1
}

# 执行查找和引入
find_and_source_goftp

# ========================================
# 执行部署流程
# ========================================

# 启动部署流程
main_deploy
