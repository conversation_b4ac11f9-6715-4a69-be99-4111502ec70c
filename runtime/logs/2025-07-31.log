2025-07-31T09:58:52.538+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=中等
2025-07-31T09:58:52.539+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T09:58:52.567+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T09:59:40.752+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=中等
2025-07-31T09:59:40.752+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T09:59:40.783+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T09:59:56.402+08:00 [DEBU] Task.Save - 接收参数: status=处理中, priority=中等
2025-07-31T09:59:56.402+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T09:59:56.440+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T10:03:13.740+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=中等
2025-07-31T10:03:13.740+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T10:03:13.790+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T10:04:56.233+08:00 [DEBU] Task.Save - 接收参数: status=搁置中, priority=中等
2025-07-31T10:04:56.233+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T10:04:56.261+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T10:15:35.690+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=中等
2025-07-31T10:15:35.690+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T10:15:35.721+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T10:23:33.790+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=中等
2025-07-31T10:23:33.790+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T10:23:33.818+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:11:27.575+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=普通
2025-07-31T11:11:27.575+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:11:27.621+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:12:37.168+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-31T11:12:37.168+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:12:37.198+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:13:31.708+08:00 [DEBU] Task.Save - 接收参数: status=处理中, priority=普通
2025-07-31T11:13:31.708+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:13:31.959+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:14:15.217+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=普通
2025-07-31T11:14:15.218+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:14:15.276+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:14:23.840+08:00 [DEBU] Task.Save - 接收参数: status=处理中, priority=普通
2025-07-31T11:14:23.841+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:14:23.885+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:17:06.561+08:00 [DEBU] Task.Save - 接收参数: status=处理中, priority=低
2025-07-31T11:17:06.561+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:17:06.589+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:20:59.678+08:00 [DEBU] Task.Save - 接收参数: status=处理中, priority=中等
2025-07-31T11:20:59.678+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:20:59.708+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:20:59.921+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T11:21:05.552+08:00 [DEBU] Task.Save - 接收参数: status=处理中, priority=普通
2025-07-31T11:21:05.552+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:21:05.581+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:21:05.761+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T11:29:24.789+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-31T11:29:24.789+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:29:24.815+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:29:25.004+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T11:40:07.581+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-31T11:40:07.582+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:40:07.611+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:40:07.825+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T11:40:14.940+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-31T11:40:14.940+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:40:14.970+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:40:15.165+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T11:41:02.248+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-31T11:41:02.248+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:41:02.275+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:41:02.449+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T11:41:08.886+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-31T11:41:08.886+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T11:41:08.915+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T11:41:09.092+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T12:39:23.473+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=低
2025-07-31T12:39:23.473+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T12:39:23.502+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T12:39:23.795+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T12:39:29.937+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=低
2025-07-31T12:39:29.937+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T12:39:29.969+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T12:39:30.152+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T12:39:36.311+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=低
2025-07-31T12:39:36.311+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T12:39:36.342+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T12:39:36.497+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=12, 原标签=[测试22 测试 aaaaa], 新标签=[测试22 测试 aaaaa as]
2025-07-31T12:39:45.195+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=低
2025-07-31T12:39:45.195+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T12:39:45.224+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T12:39:45.384+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=12, 原标签=[测试22 测试 aaaaa as], 新标签=[测试22 测试 aaaaa as asp]
2025-07-31T13:03:30.299+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=低
2025-07-31T13:03:30.299+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T13:03:30.330+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T13:03:30.519+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=12, 原标签=[], 新标签=[测试22 测试 测试3 测试2]
2025-07-31T13:04:06.594+08:00 [DEBU] Task.Save - 接收参数: status=已完成, priority=中等
2025-07-31T13:04:06.594+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T13:04:06.624+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T13:04:06.815+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T13:04:13.172+08:00 [DEBU] Task.Save - 接收参数: status=已关闭, priority=中等
2025-07-31T13:04:13.172+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-31T13:04:13.200+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-31T13:04:13.377+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=12
2025-07-31T14:18:23.778+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:18:23.779+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:18:55.426+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:18:55.426+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:21:09.721+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:21:09.721+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:21:48.294+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:21:48.295+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:28:12.240+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:28:12.240+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:28:55.514+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:28:55.514+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:29:57.050+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:29:57.050+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#722ed1 id:27 name:设计 time_create:1753943371] map[color:#eb2f96 id:26 name:研发 time_create:1753943365] map[color:#13c2c2 id:25 name:安全 time_create:1753943356] map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T14:31:40.575+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:4 name:测试1111 time_create:1753856283 weight:0] map[color:#ff4d4f icon:🎯 id:3 name:测试2 time_create:1753856271 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T14:31:40.575+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#722ed1 id:27 name:设计 time_create:1753943371] map[color:#eb2f96 id:26 name:研发 time_create:1753943365] map[color:#13c2c2 id:25 name:安全 time_create:1753943356] map[color:#1890ff id:11 name:aa time_create:1753858142] map[color:#52c41a id:10 name:ces time_create:1753858092] map[color:#f5222d id:9 name:aaaaa time_create:1753858044] map[color:#52c41a id:8 name:asp time_create:1753858017] map[color:#eb2f96 id:7 name:as time_create:1753857942] map[color:#faad14 id:6 name:测试2 time_create:1753857800] map[color:#722ed1 id:5 name:测试3 time_create:1753857769] map[color:#eb2f96 id:4 name:测试 time_create:1753857401] map[color:#1890ff id:3 name:测试22 time_create:1753855856]]
2025-07-31T16:04:08.840+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:6 name:口腔医院 time_create:1753949000 weight:0] map[color:#722ed1 icon:📁 id:5 name:城市生命线 time_create:1753948993 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T16:04:08.841+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:#1890ff id:31 name:功能问题 time_create:1753948811] map[color:#1890ff id:30 name:测试 time_create:1753948803] map[color:#2f54eb id:29 name:开发 time_create:1753948797] map[color:#722ed1 id:27 name:设计 time_create:1753943371]]
2025-07-31T16:04:23.231+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[], 新标签=[开发]
2025-07-31T16:32:09.484+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:#1890ff icon:📁 id:6 name:口腔医院 time_create:1753949000 weight:0] map[color:#722ed1 icon:📁 id:5 name:城市生命线 time_create:1753948993 weight:0] map[color:primary icon:📝 id:1 name:默认分类 time_create:1753854819 weight:0]]
2025-07-31T16:32:09.485+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: [map[color:default id:32 name:开发 time_create:1753949063] map[color:#1890ff id:31 name:功能问题 time_create:1753948811] map[color:#1890ff id:30 name:测试 time_create:1753948803] map[color:#2f54eb id:29 name:开发 time_create:1753948797] map[color:#722ed1 id:27 name:设计 time_create:1753943371]]
2025-07-31T16:32:22.272+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=14, 原标签=[], 新标签=[测试]
2025-07-31T16:57:19.790+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=14
2025-07-31T16:57:25.007+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=14
2025-07-31T16:57:37.817+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=14, 原标签=[], 新标签=[测试 功能问题]
2025-07-31T16:59:41.908+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[], 新标签=[测试 功能问题 开发]
2025-07-31T16:59:41.937+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[测试 功能问题 开发]
2025-07-31T16:59:41.952+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[]
2025-07-31T16:59:41.952+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[]
2025-07-31T16:59:41.952+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 测试
2025-07-31T16:59:41.981+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 测试, ID=36
2025-07-31T16:59:41.981+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 功能问题
2025-07-31T16:59:42.011+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 功能问题, ID=37
2025-07-31T16:59:42.011+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 开发
2025-07-31T16:59:42.041+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 开发, ID=38
2025-07-31T16:59:42.041+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[36 37 38]
2025-07-31T17:00:23.846+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[测试 功能问题 开发], 新标签=[开发 设计 测试]
2025-07-31T17:00:23.882+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[开发 设计 测试]
2025-07-31T17:00:23.900+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[]
2025-07-31T17:00:23.900+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[]
2025-07-31T17:00:23.900+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 开发
2025-07-31T17:00:23.942+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 开发, ID=39
2025-07-31T17:00:23.942+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 设计
2025-07-31T17:00:23.993+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 设计, ID=40
2025-07-31T17:00:23.993+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 测试
2025-07-31T17:00:24.044+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 测试, ID=41
2025-07-31T17:00:24.044+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[39 40 41]
2025-07-31T17:03:25.210+08:00 [DEBU] _updateTaskTags: 当前标签=[], 新标签=[功能问题 测试 设计]
2025-07-31T17:03:25.210+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[], 新标签=[功能问题 测试 设计]
2025-07-31T17:03:25.242+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[功能问题 测试 设计]
2025-07-31T17:03:25.259+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[]
2025-07-31T17:03:25.259+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[]
2025-07-31T17:03:25.259+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 功能问题
2025-07-31T17:03:25.297+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 功能问题, ID=42
2025-07-31T17:03:25.298+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 测试
2025-07-31T17:03:25.330+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 测试, ID=43
2025-07-31T17:03:25.330+08:00 [DEBU] _getOrCreateTagIds: 创建新标签 设计
2025-07-31T17:03:25.364+08:00 [DEBU] _getOrCreateTagIds: 新标签创建成功 设计, ID=44
2025-07-31T17:03:25.364+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[42 43 44]
2025-07-31T17:05:51.783+08:00 [DEBU] _updateTaskTags: 当前标签=[], 新标签=[设计 开发]
2025-07-31T17:05:51.783+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[], 新标签=[设计 开发]
2025-07-31T17:05:51.815+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[设计 开发]
2025-07-31T17:05:51.845+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[map[id:27 name:设计] map[id:29 name:开发]]
2025-07-31T17:05:51.845+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[开发:29 设计:27]
2025-07-31T17:05:51.845+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 设计, ID=27
2025-07-31T17:05:51.845+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 开发, ID=29
2025-07-31T17:05:51.845+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[27 29]
2025-07-31T17:06:03.063+08:00 [DEBU] _updateTaskTags: 当前标签=[设计 开发], 新标签=[设计 开发 功能问题]
2025-07-31T17:06:03.063+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[设计 开发], 新标签=[设计 开发 功能问题]
2025-07-31T17:06:03.090+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[设计 开发 功能问题]
2025-07-31T17:06:03.117+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[map[id:27 name:设计] map[id:29 name:开发] map[id:31 name:功能问题]]
2025-07-31T17:06:03.117+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[功能问题:31 开发:29 设计:27]
2025-07-31T17:06:03.117+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 设计, ID=27
2025-07-31T17:06:03.117+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 开发, ID=29
2025-07-31T17:06:03.117+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 功能问题, ID=31
2025-07-31T17:06:03.117+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[27 29 31]
2025-07-31T17:06:19.058+08:00 [DEBU] _updateTaskTags: 当前标签=[设计 开发 功能问题], 新标签=[设计 开发 功能问题]
2025-07-31T17:06:19.058+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=13
2025-07-31T17:14:30.967+08:00 [DEBU] _updateTaskTags: 当前标签=[设计 开发 功能问题], 新标签=[设计 开发 功能问题 测试]
2025-07-31T17:14:30.967+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[设计 开发 功能问题], 新标签=[设计 开发 功能问题 测试]
2025-07-31T17:14:30.996+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[设计 开发 功能问题 测试]
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[map[id:27 name:设计] map[id:29 name:开发] map[id:30 name:测试] map[id:31 name:功能问题]]
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[功能问题:31 开发:29 测试:30 设计:27]
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 设计, ID=27
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 开发, ID=29
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 功能问题, ID=31
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 测试, ID=30
2025-07-31T17:14:31.025+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[27 29 31 30]
2025-07-31T17:14:44.241+08:00 [DEBU] _updateTaskTags: 当前标签=[设计 开发 测试 功能问题], 新标签=[设计 开发 功能问题 测试2]
2025-07-31T17:14:44.241+08:00 [DEBU] _updateTaskTags: 检测到标签变化, taskId=13, 原标签=[设计 开发 测试 功能问题], 新标签=[设计 开发 功能问题 测试2]
2025-07-31T17:14:44.273+08:00 [DEBU] _getOrCreateTagIds: 开始处理标签, userId=1, tagNames=[设计 开发 功能问题 测试2]
2025-07-31T17:14:44.309+08:00 [DEBU] _getOrCreateTagIds: 查询到已存在标签=[map[id:27 name:设计] map[id:29 name:开发] map[id:31 name:功能问题] map[id:45 name:测试2]]
2025-07-31T17:14:44.309+08:00 [DEBU] _getOrCreateTagIds: 已存在标签映射=map[功能问题:31 开发:29 测试2:45 设计:27]
2025-07-31T17:14:44.310+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 设计, ID=27
2025-07-31T17:14:44.310+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 开发, ID=29
2025-07-31T17:14:44.310+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 功能问题, ID=31
2025-07-31T17:14:44.310+08:00 [DEBU] _getOrCreateTagIds: 使用已存在标签 测试2, ID=45
2025-07-31T17:14:44.310+08:00 [DEBU] _getOrCreateTagIds: 最终返回标签IDs=[27 29 31 45]
2025-07-31T17:16:50.925+08:00 [DEBU] _updateTaskTags: 当前标签=[设计 开发 功能问题], 新标签=[设计 开发 功能问题]
2025-07-31T17:16:50.925+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=13
2025-07-31T17:16:59.977+08:00 [DEBU] _updateTaskTags: 当前标签=[设计 开发 功能问题], 新标签=[设计 开发 功能问题]
2025-07-31T17:16:59.977+08:00 [DEBU] _updateTaskTags: 标签无变化，跳过更新, taskId=13
2025-07-31T19:20:21.832+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:20:21.832+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:21:51.662+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:21:51.662+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:22:07.259+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:22:07.259+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:23:03.495+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:23:03.496+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:23:41.533+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:23:41.533+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:24:12.094+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:24:12.094+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:24:20.398+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:24:20.399+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:24:38.704+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:24:38.704+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:25:14.502+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:25:14.503+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:25:57.779+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:25:57.779+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:33:04.327+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:33:04.327+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:38:31.098+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:38:31.098+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:38:38.580+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:38:38.580+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:40:42.409+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:40:42.409+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:40:59.199+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:40:59.200+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:42:12.653+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:42:12.653+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:45:22.006+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:45:22.006+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T19:45:48.404+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T19:45:48.404+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T20:02:50.590+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T20:02:50.591+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
2025-07-31T20:10:41.702+08:00 [DEBU] AI.GenerateAddTaskPrompt - categories type: gdb.Result, value: [map[color:primary icon:📝 id:8 name:默认分类 time_create:1753960427 weight:0]]
2025-07-31T20:10:41.702+08:00 [DEBU] AI.GenerateAddTaskPrompt - tags type: gdb.Result, value: []
