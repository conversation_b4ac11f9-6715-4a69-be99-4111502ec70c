2025-07-30T15:02:29.839+08:00 [DEBU] {0058601c6cf5561820a6b80c9e939f2a} TaskSave - 参数: userId=1, id=0, categoryId=3, content=123123, status=进行中, priority=中等, deadline=1753859100, tags=[aa aaaaa 测试]
2025-07-30T15:02:29.839+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=中等
2025-07-30T15:02:29.839+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T15:02:29.873+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T15:02:30.481+08:00 [DEBU] {0058601c6cf5561820a6b80c9e939f2a} TaskSave - 结果: result=1, msg=待办事项创建成功
2025-07-30T15:03:23.400+08:00 [DEBU] {985939a278f5561845a6b80c39b4444c} TaskSave - 参数: userId=1, id=0, categoryId=3, content=21123123, status=进行中, priority=普通, deadline=1753859280, tags=[测试2]
2025-07-30T15:03:23.400+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-30T15:03:23.400+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T15:03:23.427+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T15:03:23.569+08:00 [DEBU] {985939a278f5561845a6b80c39b4444c} TaskSave - 结果: result=2, msg=待办事项创建成功
2025-07-30T15:04:16.128+08:00 [DEBU] {60353ee384f556186d069c40a66b7fda} TaskSave - 参数: userId=1, id=0, categoryId=3, content=4444, status=未开始, priority=普通, deadline=1753880580, tags=[测试22]
2025-07-30T15:04:16.128+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:04:16.128+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T15:04:16.174+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T15:04:16.402+08:00 [DEBU] {60353ee384f556186d069c40a66b7fda} TaskSave - 结果: result=3, msg=待办事项创建成功
2025-07-30T15:05:03.937+08:00 [DEBU] {1023840390f55618df721b699afdf585} TaskSave - 参数: userId=1, id=0, categoryId=0, content=3123, status=未开始, priority=普通, deadline=0, tags=[]
2025-07-30T15:05:03.937+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:05:03.937+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:05:03.987+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:05:04.090+08:00 [DEBU] {1023840390f55618df721b699afdf585} TaskSave - 结果: result=4, msg=待办事项创建成功
2025-07-30T15:05:34.277+08:00 [DEBU] {c8212d1597f5561828704b3072bb708c} TaskSave - 参数: userId=1, id=0, categoryId=0, content=123213213, status=未开始, priority=普通, deadline=0, tags=[]
2025-07-30T15:05:34.277+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:05:34.277+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:05:34.324+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:05:34.423+08:00 [DEBU] {c8212d1597f5561828704b3072bb708c} TaskSave - 结果: result=5, msg=待办事项创建成功
2025-07-30T15:06:04.032+08:00 [DEBU] {58389b089ef55618c5cce74ade64c5c5} TaskSave - 参数: userId=1, id=0, categoryId=0, content=123123123, status=未开始, priority=普通, deadline=0, tags=[]
2025-07-30T15:06:04.032+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:06:04.032+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:06:04.060+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:06:04.149+08:00 [DEBU] {58389b089ef55618c5cce74ade64c5c5} TaskSave - 结果: result=6, msg=待办事项创建成功
2025-07-30T15:06:26.483+08:00 [DEBU] {403fbd42a3f55618a65acf4df7a8795d} TaskSave - 参数: userId=1, id=0, categoryId=0, content=312213, status=未开始, priority=普通, deadline=0, tags=[]
2025-07-30T15:06:26.484+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:06:26.484+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:06:26.513+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:06:26.604+08:00 [DEBU] {403fbd42a3f55618a65acf4df7a8795d} TaskSave - 结果: result=7, msg=待办事项创建成功
2025-07-30T15:06:55.857+08:00 [DEBU] {08940513aaf55618254f1302b1f07cad} TaskSave - 参数: userId=1, id=0, categoryId=0, content=123123, status=未开始, priority=普通, deadline=0, tags=[]
2025-07-30T15:06:55.857+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:06:55.857+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:06:55.904+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:07:34.355+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:07:34.355+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:07:34.405+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:07:53.980+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:07:53.980+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:07:54.013+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T15:08:01.865+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T15:08:01.865+08:00 [DEBU] Task.Save - 验证分类: categoryId=0
2025-07-30T15:08:01.898+08:00 [DEBU] Task.Save - 使用默认分类: categoryId=1
2025-07-30T16:29:16.380+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T16:29:16.381+08:00 [DEBU] Task.Save - 验证分类: categoryId=1
2025-07-30T16:29:16.409+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T16:29:44.581+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T16:29:44.581+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T16:29:44.615+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T16:29:51.851+08:00 [DEBU] Task.Save - 接收参数: status=未开始, priority=普通
2025-07-30T16:29:51.851+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T16:29:51.880+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T16:51:11.265+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-30T16:51:11.265+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T16:51:11.292+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T16:56:38.447+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-30T16:56:38.447+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T16:56:38.476+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T16:59:08.297+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=普通
2025-07-30T16:59:08.297+08:00 [DEBU] Task.Save - 验证分类: categoryId=3
2025-07-30T16:59:08.328+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
2025-07-30T18:36:31.626+08:00 [DEBU] Task.Save - 接收参数: status=进行中, priority=中等
2025-07-30T18:36:31.626+08:00 [DEBU] Task.Save - 验证分类: categoryId=4
2025-07-30T18:36:31.655+08:00 [DEBU] Task.Save - 分类验证结果: categoryExists=1
