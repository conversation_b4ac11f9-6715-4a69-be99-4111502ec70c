---
type: "agent_requested"
---

我们提供统一的初始化方法  就是说  在对外需要鉴权的方法内 调用
```
_base := base.InitBaseCtx(r *ghttp.request);
```
这个_base对象 内置了鉴权 并且封装好了input/back/userId等vant2常用对象
_base的类型如下
```
type BaseCtx struct {
	Input    *vant2.Input // 输入
	Back     *vant2.Back  // 输出
	UserId   int64        // 当前登录用户的id
	Id       int64        // 当前页面请求传入的id值
	PageNum  int          // 当前请求传入的页码
	PageSize int          // 每页条数
	UserInfo *userm.UserModel  // 用户信息 可以忽略
}
```
然后直接使用 _base.Input.Value("name") 或者 _base.Back.ApiError()等方法
这样 你就无需额外的定义
```
_input := vant2.Input(r)
_back := vant2.InitBack(r)
```
在CURD操作的时候 尽可能 修改和新增，共用一个控制器和一个方法，尽可能合并，减少总代码量
然后 默认数据库名自动添加了“dede_”这个prefix，创建sql文件的时候，应该带入dede_，
但是本地vant2.DB操作的时候，无需添加"dede_"，比如 表名叫 dede_user，查询的时候 vant2.DB("user").Select()即可
此外
1、路由配置文件 是pods/index.go
2、如方法、参数不对外可用 仅包内使用 方法名和参数名应该是"_"开头
3、重点步骤关键步骤，必须有注释！
4、一定记得 要先写方法 后引入 而不是先引入检查后写方法  引入包后不调用自动保存 系统会自动删除引入的