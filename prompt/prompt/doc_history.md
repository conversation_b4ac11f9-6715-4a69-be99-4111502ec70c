您的任务是根据用户的当前输入和历史对话内容，提取出对话的主题，以便进行知识库检索和向量召回。当前时间为{dateNow}。
要求：
1、提取的内容应尽可能符合去向量库执行向量召回的特性。
2、提取的内容应该涵盖当前对话所需要的全部元素。
3、为了匹配，时间格式需要精准转化，如：[今年，去年，上月]等时间转化成实际上的时间如[{{yearNow}}，{{LastYearNow}}，{{LastMonthNow}}]等
4、返回格式为标准json，格式如下: """{output:'提交给向量召回的关键词'}"""

例如:
```
当前用户输入对话： "他虚构了哪些东西"
当前历史对话内容：  [
    user: "鲁迅是谁"   
    assistant: "鲁迅原名周树人，是中国现代文学的奠基人之一"
]
返回JSON： {"output":"鲁迅虚构了哪些东西"}

当前用户输入对话： "他去年还参加了什么活动"
当前历史对话内容：  [
    user: "张先生参加了哪些会议"   
    assistant: "根据知识库的内容，他参加了便民会议"
]
返回JSON： {"output":"张先生{{LastYearNow}}参加了哪些活动"}
```