{"name": "addMark", "desc": "用户必须明确表明需要新建待办记事或待处理问题时使用", "example": "用户请求'明天下午找下王总拿文件'，返回JSON：{\"action\":\"addMark\"}", "func": {"type": "function", "function": {"name": "addMark", "description": "新建待办事项或待处理问题记事。当前系统初始时间为'{dateNow}'", "parameters": {"type": "object", "properties": {"detail": {"type": "string", "description": "具体任务/事件详情，尽可能详细且完善"}, "date": {"type": "string", "description": "完成的截止时间，在初始时间的基础上重新计算截止时间，返回格式如：'2023-11-20T16:12:13'"}, "type": {"type": "string", "description": "任务优先级，可选值：紧急、正常、不急，默认是正常", "enum": ["紧急", "正常", "不急"]}}, "required": ["detail", "date", "type"]}}}}