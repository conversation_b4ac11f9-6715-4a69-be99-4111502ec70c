{"name": "task", "desc": "用户当前明确需要添加一个待办/待完成的任务，或者查询一个已有任务，则调用本方法", "example": "\n当前输入对话： \"\"\"明天下午给张老师打电话\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"task\"}\n \"\"\"我现在还有那些事情没有做完的？\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"task\"}\n", "func": [{"type": "function", "function": {"name": "addTask", "description": "创建新的待办记事，用于添加需要完成的任务。当前系统时间：{dateNow}", "parameters": {"type": "object", "properties": {"content": {"type": "string", "description": "待办事项的详细内容描述"}, "time_end": {"type": "string", "description": "任务完成截止日期时间，格式：yyyy-MM-dd HH:mm:ss，例如：2024-01-15 14:30:00"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "任务标签数组，用于分类和标记任务"}, "group": {"type": "string", "description": "任务所属分组，用于组织和管理相关任务"}}, "required": ["content", "time_end"]}}}, {"type": "function", "function": {"name": "queryTask", "description": "查询待办记事，支持关键词搜索、日期范围筛选和完成状态过滤", "parameters": {"type": "object", "properties": {"keyword": {"type": "string", "description": "查询关键词，可以为空。用于在任务内容中搜索匹配的文本"}, "time_start": {"type": "integer", "description": "查询日期范围的开始时间，10位时间戳格式。例如：今天开始 -> 当天00:00:00的时间戳"}, "time_end": {"type": "integer", "description": "查询日期范围的结束时间，10位时间戳格式。例如：今天结束 -> 当天23:59:59的时间戳"}, "only_unfinished": {"type": "boolean", "description": "是否仅查询未完成的任务。true=仅查询未完成，false=查询所有任务", "default": false}}, "required": []}}}]}