{"name": "addTask", "desc": "用户必须明确表明需要新建待办记事、安排同事或他人协助或者做某件事时使用", "example": "用户请求'明天下午找下王总拿文件'，返回JSON：{\"action\":\"addTask\"}", "func": {"type": "function", "function": {"name": "addTask", "description": "仅用于安排其他人在指定时间内完成特定的任务，或记录自己的待办事项，当前系统初始时间为'{dateNow}'", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "接收任务的人的姓名或标识，如果是自己，则留空"}, "detail": {"type": "string", "description": "具体任务/事件详情，尽可能详细且完善"}, "date": {"type": "string", "description": "完成的截止时间，返回格式如：'2022-11-20T16:12:13'"}, "type": {"type": "string", "description": "任务优先级，可选值：紧急、正常、不急", "enum": ["紧急", "正常", "不急"]}}, "required": ["detail", "date", "type"]}}}}