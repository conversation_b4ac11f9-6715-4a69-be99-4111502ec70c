{"name": "sendMsg", "desc": "用户明确表示需要发送短信消息，需要明确包含接收人【11位手机号或联系人或部门】和发送的消息详情", "example": "用户请求'明天下午找下王总拿文件'，返回JSON：{\"action\":\"addMark\"}", "func": {"type": "function", "function": {"name": "sendMsg", "description": "这是一个用于向指定联系人或者指定部门发送短信的工具，需要提供接收人和短信内容", "parameters": {"type": "object", "properties": {"user": {"type": "string", "description": "接收短信消息的人，可以是11位数字的手机号或特定的人名或公司部门"}, "message": {"type": "string", "description": "消息内容，必须是简洁的，限制了最大字符数为50个，确保符合短信服务的要求。"}}, "required": ["user", "message"]}}}}