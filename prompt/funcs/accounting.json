{"name": "accounting", "desc": "用户需要记录收支账目、查询账目统计或管理财务记录时使用", "example": "\n当前输入对话： \"\"\"今天买菜花了50块钱\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"accounting\"}\n \"\"\"查看这个月的支出情况\"\"\"\n当前历史对话内容： 无\n返回JSON： {\"action\":\"accounting\"}\n", "func": [{"type": "function", "function": {"name": "addRecord", "description": "新增记账记录，用于记录收入、支出等财务信息。当前系统时间：{dateNow}", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "金额，必填字段，支持小数点，例如：50.00、100.5"}, "type": {"type": "string", "description": "记账类型", "enum": ["支出", "入账", "不计入收入"]}, "category": {"type": "string", "description": "支出/收入分类", "enum": ["餐饮", "交通", "服饰", "购物", "服务", "教育", "娱乐", "运动", "生活缴费", "旅行", "宠物", "医疗", "其他", "自定义"]}, "date": {"type": "string", "description": "记账日期，格式：yyyy-MM-dd，例如：2024-01-15。如果用户说今天、明天等相对时间，需要转换为具体日期"}, "note": {"type": "string", "description": "备注信息，记录详细说明或补充信息"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "标签数组，用于更细致的分类和标记"}}, "required": ["amount", "type", "category", "date"]}}}, {"type": "function", "function": {"name": "queryRecords", "description": "查询和统计记账记录，支持按时间范围、类型、分类等条件筛选", "parameters": {"type": "object", "properties": {"start_date": {"type": "string", "description": "查询开始日期，格式：yyyy-MM-dd。例如：本月 -> 当月1号，今年 -> 当年1月1日"}, "end_date": {"type": "string", "description": "查询结束日期，格式：yyyy-MM-dd。例如：本月 -> 当月最后一天，今年 -> 当年12月31日"}, "type": {"type": "string", "description": "筛选记账类型，可选值：支出、入账、不计入收入。为空则查询所有类型"}, "category": {"type": "string", "description": "筛选分类，例如：餐饮、交通等。为空则查询所有分类"}, "keyword": {"type": "string", "description": "关键词搜索，在备注和标签中搜索匹配内容"}, "statistics": {"type": "boolean", "description": "是否返回统计信息（总收入、总支出、净收入、各分类占比等）", "default": true}}, "required": []}}}]}