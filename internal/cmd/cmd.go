package cmd

import (
	"assistant/pods"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "aigc-task server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {

			// 创建服务器实例
			s := g.Server()

			// 初始化全局日志配置
			// logic.SetGlobalLogConfig()
			// 注册全局中间件，记录HTTP请求日志
			// s.Use(logic.MiddlewareHandlerLog)

			// 注册路由（委托给专门的路由注册函数）
			pods.RegisterRoutes(s)

			// 启动服务器
			s.Run()
			return nil
		},
	}
)
