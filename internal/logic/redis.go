package logic

import (
	"sync"
	"time"

	// 导入Redis适配器，这是必须的
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gctx"

	"vant2"
)

var (
	ctx           = gctx.New()
	redisClient   *gredis.Redis
	redisCache    *gcache.Cache
	redisOnce     sync.Once
	Redis         = &ExRedis{}
	isInitialized bool // 标记是否成功初始化
)

// 初始化Redis客户端和缓存
func InitRedis() bool {
	if isInitialized {
		return true
	}

	redisOnce.Do(func() {
		_addr, err := g.Cfg().Get(ctx, "redis.addr")
		if err != nil || _addr.IsEmpty() {
			vant2.Error("Redis配置获取失败: redis.addr未配置或为空")
			return
		}

		// 创建基础配置
		redisConfig := &gredis.Config{
			Address: _addr.String(),
			Db:      0, // 默认使用0号数据库
		}

		// 可选配置 - 密码
		_pwd, err := g.Cfg().Get(ctx, "redis.password")
		if err == nil && !_pwd.IsEmpty() {
			redisConfig.Pass = _pwd.String()
			vant2.Primary("Redis使用密码认证")
		} else {
			vant2.Primary("Redis无密码模式")
		}

		// 可选配置 - 数据库
		_db, err := g.Cfg().Get(ctx, "redis.db")
		if err == nil && !_db.IsEmpty() && _db.Int() > 0 {
			redisConfig.Db = _db.Int()
		}

		vant2.Primary("Redis配置:", redisConfig)

		// 创建Redis客户端
		var err2 error
		redisClient, err2 = gredis.New(redisConfig)
		if err2 != nil {
			vant2.Error("Redis连接创建失败:", err2)
			return
		}

		// 创建缓存对象并设置Redis适配器
		redisCache = gcache.New()
		redisCache.SetAdapter(gcache.NewAdapterRedis(redisClient))

		// 测试连接
		pingResult, err2 := redisClient.Do(ctx, "PING")
		if err2 != nil {
			vant2.Error("Redis连接测试失败:", err2)

			// 尝试无密码模式重连
			if redisConfig.Pass != "" {
				redisConfig.Pass = ""
				redisClient, err2 = gredis.New(redisConfig)
				if err2 != nil {
					return
				}

				// 重新设置适配器
				redisCache.SetAdapter(gcache.NewAdapterRedis(redisClient))

				// 再次测试
				pingResult, err2 = redisClient.Do(ctx, "PING")
				if err2 != nil {
					return
				}
			} else {
				return
			}
		}

		vant2.Primary("Redis连接成功，PING返回:", pingResult)
		isInitialized = true
	})

	return isInitialized
}

type ExRedis struct{}

// RedisInit 初始化Redis连接
func (d *ExRedis) RedisInit() bool {
	return InitRedis()
}

// Get 获取字符串值
func (d *ExRedis) Get(key string) string {
	if !InitRedis() {
		return ""
	}

	value, err := redisCache.Get(ctx, key)
	if err != nil {
		vant2.Warning("Redis获取数据失败:", key, err)
		return ""
	}
	return value.String()
}

// GetMap 获取g.Map格式的结果
func (d *ExRedis) GetMap(key string) g.Map {
	if !InitRedis() {
		return g.Map{}
	}

	value, err := redisCache.Get(ctx, key)
	if err != nil {
		return g.Map{}
	}

	if value.IsNil() {
		return g.Map{}
	}

	var result g.Map
	if err = value.Scan(&result); err != nil {
		return g.Map{}
	}
	return result
}

// Set 设置值，可选过期时间（秒）
func (d *ExRedis) Set(key string, value interface{}, ttl ...int) bool {
	if !InitRedis() {
		return false
	}

	_ttl := 0
	if len(ttl) > 0 {
		_ttl = ttl[0]
	}

	var duration time.Duration
	if _ttl > 0 {
		duration = time.Duration(_ttl) * time.Second
	}

	err := redisCache.Set(ctx, key, value, duration)
	if err != nil {
		vant2.Warning("Redis设置数据失败:", key, err)
		return false
	}
	return true
}

// Delete 删除键
func (d *ExRedis) Delete(key string) bool {
	if !InitRedis() {
		return false
	}

	_, err := redisCache.Remove(ctx, key)
	if err != nil {
		return false
	}
	return true
}
