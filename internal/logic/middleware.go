package logic

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
)

// MiddlewareHandlerLog HTTP请求日志中间件
func MiddlewareHandlerLog(r *ghttp.Request) {
	// 开始时间
	startTime := time.Now()

	// 获取客户端IP
	clientIP := r.GetClientIp()

	// 继续请求
	r.Middleware.Next()

	// 结束时间
	endTime := time.Now()
	// 执行时间
	execTime := endTime.Sub(startTime)
	// URI
	requestUri := r.Request.RequestURI
	// 请求方式
	method := r.Request.Method
	// 响应状态码
	statusCode := r.Response.Status

	// 使用g.Log()方法记录日志，这样会使用配置文件中的配置
	g.Log().Infof(gctx.New(), "[HTTP] %s | %d | %s | %s | %.3fms",
		method, statusCode, clientIP, requestUri, execTime.Seconds()*1000)
}

// SetGlobalLogConfig 设置全局日志配置
func SetGlobalLogConfig() {
	ctx := gctx.New()
	// 获取配置中的日志路径
	logPath, err := g.Cfg().Get(ctx, "logger.path")
	if err != nil || logPath.IsEmpty() {
		g.Log().Warning(ctx, "无法读取日志路径配置，使用默认路径")
		return
	}

	// 设置glog包默认的Logger配置，影响所有通过glog包函数打印的日志
	m := map[string]interface{}{
		"path":   logPath.String(), // 使用配置文件中的路径
		"level":  "all",            // 记录所有级别的日志
		"stdout": true,             // 同时输出到终端
	}

	// 设置全局默认处理器，这样所有使用glog包函数的日志都会受此影响
	glog.SetDefaultHandler(func(ctx context.Context, in *glog.HandlerInput) {
		// 设置日志配置
		in.Logger.SetConfigWithMap(m)
		// 调用下一个处理器
		in.Next(ctx)
	})
}
