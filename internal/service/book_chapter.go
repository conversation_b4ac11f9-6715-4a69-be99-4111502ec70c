package service

import (
	"strings"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// BookChapterService 章节服务
type BookChapterService struct{}

// ChapterListParam 章节列表参数
type ChapterListParam struct {
	BookId   int64 `json:"book_id"`
	UserId   int64 `json:"user_id"`
	PageNum  int   `json:"page_num"`
	PageSize int   `json:"page_size"`
}

// ChapterSaveParam 章节保存参数
type ChapterSaveParam struct {
	Id                  int64  `json:"id"`
	BookId              int64  `json:"book_id"`
	UserId              int64  `json:"user_id"`
	ChapterNum          int    `json:"chapter_num"`
	Title               string `json:"title"`
	Outline             string `json:"outline"`
	Content             string `json:"content"`
	GenerateRequirement string `json:"generate_requirement"`
}

// BookChapter 获取章节服务实例
func BookChapter() *BookChapterService {
	return &BookChapterService{}
}

// List 获取章节列表
func (s *BookChapterService) List(param *ChapterListParam) (w.SliceMap, int64) {
	_where := w.Map{
		"book_id":   param.BookId,
		"user_id":   param.UserId,
		"is_delete": 0,
	}

	_db := vant2.DB("book_chapter").Where(_where)

	// 获取总数
	_count, _ := _db.Clone().Count()

	if _count > 0 {
		// 按章节序号排序
		_db = _db.Order("chapter_num ASC")

		// 分页
		if param.PageSize <= 0 {
			param.PageSize = 50
		}
		if param.PageNum <= 0 {
			param.PageNum = 1
		}

		_res, _ := _db.Paginate(param.PageNum, param.PageSize)
		return gconv.SliceMap(_res), int64(_count)
	}

	return nil, 0
}

// Detail 获取章节详情
func (s *BookChapterService) Detail(chapterId, userId int64) w.Map {
	_where := w.Map{
		"id":        chapterId,
		"user_id":   userId,
		"is_delete": 0,
	}

	_res := vant2.DB("book_chapter").Where(_where).One()
	if _res == nil {
		return nil
	}

	return _res.Map()
}

// Save 保存章节
func (s *BookChapterService) Save(param *ChapterSaveParam) (int64, bool) {
	_currentTime := vant2.Time()

	// 计算字数
	_wordCount := s.countWords(param.Content)

	_data := w.Map{
		"title":       param.Title,
		"outline":     param.Outline,
		"content":     param.Content,
		"word_count":  _wordCount,
		"time_update": _currentTime,
	}

	// 设置内容状态
	if param.Content != "" {
		_data["has_content"] = 1
		_data["status"] = "已生成"
	} else {
		_data["has_content"] = 0
		_data["status"] = "未生成"
	}

	if param.Id > 0 {
		// 更新章节
		_where := w.Map{
			"id":        param.Id,
			"user_id":   param.UserId,
			"is_delete": 0,
		}

		// 保存历史记录
		s.saveHistory(param.Id, param.UserId, "save", "手动保存")

		_res := vant2.DB("book_chapter").Where(_where).Data(_data).Update()

		if _res {
			// 更新小说字数统计
			Book().UpdateWordCount(param.BookId)
		}

		return param.Id, _res
	} else {
		// 新建章节
		_data["book_id"] = param.BookId
		_data["user_id"] = param.UserId
		_data["chapter_num"] = param.ChapterNum
		_data["time_create"] = _currentTime

		_chapterId := vant2.DB("book_chapter").Data(_data).AddGetId()

		if _chapterId > 0 {
			// 更新小说字数统计和大纲章节数
			s.updateBookOutlineChapters(param.BookId)
			Book().UpdateWordCount(param.BookId)
		}

		return gconv.Int64(_chapterId), _chapterId > 0
	}
}

// Delete 删除章节
func (s *BookChapterService) Delete(chapterId, userId int64) bool {
	_where := w.Map{
		"id":        chapterId,
		"user_id":   userId,
		"is_delete": 0,
	}

	// 获取章节信息用于更新小说统计
	_chapter := vant2.DB("book_chapter").Where(_where).One()
	if _chapter == nil {
		return false
	}
	_bookId := gconv.Int64(_chapter["book_id"])

	_res := vant2.DB("book_chapter").Where(_where).Data(w.Map{
		"is_delete":   1,
		"time_update": vant2.Time(),
	}).Update()

	if _res {
		// 更新小说统计
		s.updateBookOutlineChapters(_bookId)
		Book().UpdateWordCount(_bookId)
	}

	return _res
}

// GenerateOutline AI生成大纲
func (s *BookChapterService) GenerateOutline(bookId, userId int64, plannedChapters int) bool {
	// 获取小说信息
	_book := vant2.DB("book").Where(w.Map{
		"id":        bookId,
		"user_id":   userId,
		"is_delete": 0,
	}).One()

	if _book == nil {
		return false
	}

	_currentTime := vant2.Time()

	// 清空现有大纲
	vant2.DB("book_chapter").Where(w.Map{
		"book_id":   bookId,
		"user_id":   userId,
		"is_delete": 0,
	}).Data(w.Map{
		"is_delete":   1,
		"time_update": _currentTime,
	}).Update()

	// 生成新的章节大纲
	for i := 1; i <= plannedChapters; i++ {
		_data := w.Map{
			"book_id":      bookId,
			"user_id":      userId,
			"chapter_num":  i,
			"title":        "第" + gconv.String(i) + "章",
			"outline":      "",
			"content":      "",
			"word_count":   0,
			"status":       "未生成",
			"has_content":  0,
			"ai_generated": 1,
			"time_create":  _currentTime,
			"time_update":  _currentTime,
		}
		vant2.DB("book_chapter").Data(_data).Add()
	}

	// 更新小说的大纲章节数
	vant2.DB("book").Where("id", bookId).Data(w.Map{
		"outline_chapters": plannedChapters,
		"time_update":      _currentTime,
	}).Update()

	return true
}

// AddChapter 手动添加章节
func (s *BookChapterService) AddChapter(bookId, userId int64, insertAfter int) (int64, bool) {
	// 获取插入位置后的所有章节，序号+1
	if insertAfter > 0 {
		vant2.DB("book_chapter").Where(w.Map{
			"book_id":       bookId,
			"chapter_num >": insertAfter,
			"is_delete":     0,
		}).Data(w.Map{
			"chapter_num": "chapter_num + 1",
		}).Update()
	}

	// 插入新章节
	_newChapterNum := insertAfter + 1
	_currentTime := vant2.Time()

	_data := w.Map{
		"book_id":      bookId,
		"user_id":      userId,
		"chapter_num":  _newChapterNum,
		"title":        "第" + gconv.String(_newChapterNum) + "章",
		"outline":      "",
		"content":      "",
		"word_count":   0,
		"status":       "未生成",
		"has_content":  0,
		"ai_generated": 0,
		"time_create":  _currentTime,
		"time_update":  _currentTime,
	}

	_chapterId := vant2.DB("book_chapter").Data(_data).AddGetId()

	if _chapterId > 0 {
		// 更新小说大纲章节数
		s.updateBookOutlineChapters(bookId)
	}

	return gconv.Int64(_chapterId), _chapterId > 0
}

// saveHistory 保存章节历史记录
func (s *BookChapterService) saveHistory(chapterId, userId int64, operationType, versionNote string) {
	// 获取当前章节内容
	_chapter := vant2.DB("book_chapter").Where("id", chapterId).One()
	if _chapter == nil {
		return
	}

	_data := w.Map{
		"chapter_id":     chapterId,
		"book_id":        _chapter["book_id"],
		"user_id":        userId,
		"title":          _chapter["title"],
		"content":        _chapter["content"],
		"word_count":     _chapter["word_count"],
		"version_note":   versionNote,
		"operation_type": operationType,
		"time_create":    vant2.Time(),
	}

	vant2.DB("book_chapter_history").Data(_data).Add()
}

// GetHistory 获取章节历史记录
func (s *BookChapterService) GetHistory(chapterId, userId int64) w.SliceMap {
	_where := w.Map{
		"chapter_id": chapterId,
		"user_id":    userId,
	}

	_res, _ := vant2.DB("book_chapter_history").Where(_where).Order("time_create DESC").All()
	return gconv.SliceMap(_res)
}

// countWords 计算字数（简单的中文字数统计）
func (s *BookChapterService) countWords(content string) int {
	if content == "" {
		return 0
	}

	// 移除空白字符
	content = strings.TrimSpace(content)
	content = strings.ReplaceAll(content, " ", "")
	content = strings.ReplaceAll(content, "\n", "")
	content = strings.ReplaceAll(content, "\r", "")
	content = strings.ReplaceAll(content, "\t", "")

	return len([]rune(content))
}

// updateBookOutlineChapters 更新小说的大纲章节数
func (s *BookChapterService) updateBookOutlineChapters(bookId int64) {
	_count := vant2.DB("book_chapter").Where(w.Map{
		"book_id":   bookId,
		"is_delete": 0,
	}).Count()

	vant2.DB("book").Where("id", bookId).Data(w.Map{
		"outline_chapters": _count,
		"time_update":      vant2.Time(),
	}).Update()
}

// GenerateContent AI生成章节内容
func (s *BookChapterService) GenerateContent(chapterId, userId int64, requirement string) bool {
	_where := w.Map{
		"id":        chapterId,
		"user_id":   userId,
		"is_delete": 0,
	}

	// 这里应该调用AI服务生成内容，暂时先保存生成要求
	_data := w.Map{
		"generate_requirement": requirement,
		"ai_generated":         1,
		"time_update":          vant2.Time(),
	}

	// 保存历史记录
	s.saveHistory(chapterId, userId, "ai_generate", "AI生成内容："+requirement)

	return vant2.DB("book_chapter").Where(_where).Data(_data).Update()
}

// RewriteContent AI重写章节内容
func (s *BookChapterService) RewriteContent(chapterId, userId int64, requirement string) bool {
	_where := w.Map{
		"id":        chapterId,
		"user_id":   userId,
		"is_delete": 0,
	}

	// 保存历史记录
	s.saveHistory(chapterId, userId, "ai_rewrite", "AI重写内容："+requirement)

	// 这里应该调用AI服务重写内容，暂时先保存重写要求
	_data := w.Map{
		"generate_requirement": requirement,
		"time_update":          vant2.Time(),
	}

	return vant2.DB("book_chapter").Where(_where).Data(_data).Update()
}
