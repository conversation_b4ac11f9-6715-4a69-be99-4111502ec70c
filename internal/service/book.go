package service

import (
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// BookService 小说服务
type BookService struct{}

// BookListParam 小说列表参数
type BookListParam struct {
	UserId    int64  `json:"user_id"`
	Status    string `json:"status"`
	Keyword   string `json:"keyword"`
	Genre     string `json:"genre"`
	PageNum   int    `json:"page_num"`
	PageSize  int    `json:"page_size"`
	SortOrder string `json:"sort_order"`
}

// BookSaveParam 小说保存参数
type BookSaveParam struct {
	Id              int64  `json:"id"`
	UserId          int64  `json:"user_id"`
	Title           string `json:"title"`
	Author          string `json:"author"`
	Description     string `json:"description"`
	Protagonist     string `json:"protagonist"`
	Characters      string `json:"characters"`
	Worldview       string `json:"worldview"`
	Genre           string `json:"genre"`
	PlannedChapters int    `json:"planned_chapters"`
	WordsPerChapter int    `json:"words_per_chapter"`
	CoverImage      string `json:"cover_image"`
	Tags            string `json:"tags"`
	Private         int    `json:"private"`
}

// Book 获取小说服务实例
func Book() *BookService {
	return &BookService{}
}

// List 获取小说列表
func (s *BookService) List(param *BookListParam) (w.SliceMap, int64) {
	_where := w.Map{
		"user_id":   param.UserId,
		"is_delete": 0,
	}

	// 状态筛选
	if param.Status != "" && param.Status != "all" {
		_where["status"] = param.Status
	}

	// 体裁筛选
	if param.Genre != "" && param.Genre != "all" {
		_where["genre"] = param.Genre
	}

	_db := vant2.DB("book").Where(_where)

	// 关键词搜索
	if param.Keyword != "" {
		_db = _db.Where("MATCH(title,description) AGAINST(? IN BOOLEAN MODE)", "*"+param.Keyword+"*")
	}

	// 排序
	switch param.SortOrder {
	case "title_asc":
		_db = _db.Order("title ASC")
	case "title_desc":
		_db = _db.Order("title DESC")
	case "created_asc":
		_db = _db.Order("time_create ASC")
	case "updated_desc":
		_db = _db.Order("time_update DESC")
	default:
		_db = _db.Order("time_update DESC, id DESC")
	}

	// 分页
	if param.PageSize <= 0 {
		param.PageSize = 10
	}
	if param.PageNum <= 0 {
		param.PageNum = 1
	}

	_res, _count := _db.Paginate(param.PageNum, param.PageSize)
	return gconv.SliceMap(_res), int64(_count)
}

// Detail 获取小说详情
func (s *BookService) Detail(bookId, userId int64) w.Map {
	_where := w.Map{
		"id":        bookId,
		"user_id":   userId,
		"is_delete": 0,
	}

	_res := vant2.DB("book").Where(_where).One()
	if _res == nil {
		return nil
	}

	_data := _res.Map()

	// 获取已完成章节数
	_completedChapters := vant2.DB("book_chapter").Where(w.Map{
		"book_id":     bookId,
		"has_content": 1,
		"is_delete":   0,
	}).Count()
	_data["completed_chapters"] = _completedChapters

	return _data
}

// Save 保存小说
func (s *BookService) Save(param *BookSaveParam) (int64, bool) {
	_currentTime := vant2.Time()
	_data := w.Map{
		"title":             param.Title,
		"author":            param.Author,
		"description":       param.Description,
		"protagonist":       param.Protagonist,
		"characters":        param.Characters,
		"worldview":         param.Worldview,
		"genre":             param.Genre,
		"planned_chapters":  param.PlannedChapters,
		"words_per_chapter": param.WordsPerChapter,
		"cover_image":       param.CoverImage,
		"tags":              param.Tags,
		"private":           param.Private,
		"time_update":       _currentTime,
	}

	if param.Id > 0 {
		// 更新
		_where := w.Map{
			"id":        param.Id,
			"user_id":   param.UserId,
			"is_delete": 0,
		}
		_res := vant2.DB("book").Where(_where).Data(_data).Update()
		return param.Id, _res
	} else {
		// 新建
		_data["user_id"] = param.UserId
		_data["time_create"] = _currentTime
		_data["status"] = "创作中"
		_bookId := vant2.DB("book").Data(_data).AddGetId()
		return gconv.Int64(_bookId), _bookId > 0
	}
}

// Delete 删除小说
func (s *BookService) Delete(bookId, userId int64) bool {
	_where := w.Map{
		"id":        bookId,
		"user_id":   userId,
		"is_delete": 0,
	}

	_res := vant2.DB("book").Where(_where).Data(w.Map{
		"is_delete":   1,
		"time_update": vant2.Time(),
	}).Update()

	if _res {
		// 同时删除相关章节
		vant2.DB("book_chapter").Where(w.Map{
			"book_id":   bookId,
			"user_id":   userId,
			"is_delete": 0,
		}).Data(w.Map{
			"is_delete":   1,
			"time_update": vant2.Time(),
		}).Update()
	}

	return _res
}

// UpdateStatus 更新小说状态
func (s *BookService) UpdateStatus(bookId, userId int64, status string) bool {
	_where := w.Map{
		"id":        bookId,
		"user_id":   userId,
		"is_delete": 0,
	}

	return vant2.DB("book").Where(_where).Data(w.Map{
		"status":      status,
		"time_update": vant2.Time(),
	}).Update()
}

// GetStatistics 获取小说统计信息
func (s *BookService) GetStatistics(userId int64) w.Map {
	_where := w.Map{
		"user_id":   userId,
		"is_delete": 0,
	}

	// 总小说数
	_totalBooks := vant2.DB("book").Where(_where).Count()

	// 总字数
	_totalWordsRes := vant2.DB("book").Where(_where).Fields("SUM(word_count) as total_words").One()
	_totalWords := gconv.Int64(_totalWordsRes["total_words"])

	// 今日新增字数
	_todayWordsRes := vant2.DB("book").Where(_where).Fields("SUM(today_words) as today_words").One()
	_todayWords := gconv.Int(_todayWordsRes["today_words"])

	// 状态统计
	_statusStats, _ := vant2.DB("book").Where(_where).Fields("status, COUNT(*) as count").Group("status").All()

	return w.Map{
		"total_books":  _totalBooks,
		"total_words":  _totalWords,
		"today_words":  _todayWords,
		"status_stats": gconv.SliceMap(_statusStats),
	}
}

// UpdateWordCount 更新小说字数统计
func (s *BookService) UpdateWordCount(bookId int64) bool {
	// 计算章节总字数
	_totalWordsRes := vant2.DB("book_chapter").Where(w.Map{
		"book_id":   bookId,
		"is_delete": 0,
	}).Fields("SUM(word_count) as total_words, COUNT(*) as total_chapters").One()

	_totalWords := gconv.Int64(_totalWordsRes["total_words"])
	_writtenChapters := gconv.Int(_totalWordsRes["total_chapters"])

	return vant2.DB("book").Where("id", bookId).Data(w.Map{
		"word_count":       _totalWords,
		"written_chapters": _writtenChapters,
		"time_update":      vant2.Time(),
	}).Update()
}
