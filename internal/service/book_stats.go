package service

import (
	"time"
	"vant2"
	"vant2/tool/w"

	"github.com/gogf/gf/v2/util/gconv"
)

// BookStatsService 写作统计服务
type BookStatsService struct{}

// DailyStatsParam 日统计参数
type DailyStatsParam struct {
	UserId int64  `json:"user_id"`
	BookId int64  `json:"book_id"`
	Date   string `json:"date"`
}

// BookStats 获取写作统计服务实例
func BookStats() *BookStatsService {
	return &BookStatsService{}
}

// RecordDailyStats 记录每日写作统计
func (s *BookStatsService) RecordDailyStats(userId, bookId int64, wordsAdded, chaptersAdded, timeSpent int) bool {
	_today := time.Now().Format("2006-01-02")
	_currentTime := vant2.Time()

	_where := w.Map{
		"user_id": userId,
		"book_id": bookId,
		"date":    _today,
	}

	// 检查今日是否已有记录
	_existing := vant2.DB("book_writing_stats").Where(_where).One()

	if _existing != nil {
		// 更新现有记录
		_data := w.Map{
			"words_added":    gconv.Int(_existing["words_added"]) + wordsAdded,
			"chapters_added": gconv.Int(_existing["chapters_added"]) + chaptersAdded,
			"time_spent":     gconv.Int(_existing["time_spent"]) + timeSpent,
		}
		return vant2.DB("book_writing_stats").Where(_where).Data(_data).Update()
	} else {
		// 新建记录
		_data := w.Map{
			"user_id":        userId,
			"book_id":        bookId,
			"date":           _today,
			"words_added":    wordsAdded,
			"chapters_added": chaptersAdded,
			"time_spent":     timeSpent,
			"time_create":    _currentTime,
		}
		return vant2.DB("book_writing_stats").Data(_data).Add()
	}
}

// GetDailyStats 获取每日统计
func (s *BookStatsService) GetDailyStats(param *DailyStatsParam) w.Map {
	_where := w.Map{
		"user_id": param.UserId,
		"date":    param.Date,
	}

	if param.BookId > 0 {
		_where["book_id"] = param.BookId
	}

	_res := vant2.DB("book_writing_stats").Where(_where).One()
	if _res == nil {
		return w.Map{
			"words_added":    0,
			"chapters_added": 0,
			"time_spent":     0,
		}
	}

	return _res.Map()
}

// GetWeeklyStats 获取周统计
func (s *BookStatsService) GetWeeklyStats(userId, bookId int64) w.SliceMap {
	// 获取最近7天的数据
	_endDate := time.Now()
	_startDate := _endDate.AddDate(0, 0, -6)

	_where := w.Map{
		"user_id": userId,
		"date >=": _startDate.Format("2006-01-02"),
		"date <=": _endDate.Format("2006-01-02"),
	}

	if bookId > 0 {
		_where["book_id"] = bookId
	}

	_res, _ := vant2.DB("book_writing_stats").Where(_where).Order("date ASC").All()
	return gconv.SliceMap(_res)
}

// GetMonthlyStats 获取月统计
func (s *BookStatsService) GetMonthlyStats(userId, bookId int64, year, month int) w.Map {
	_startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	_endDate := _startDate.AddDate(0, 1, -1)

	_where := w.Map{
		"user_id": userId,
		"date >=": _startDate.Format("2006-01-02"),
		"date <=": _endDate.Format("2006-01-02"),
	}

	if bookId > 0 {
		_where["book_id"] = bookId
	}

	// 聚合统计
	_fields := "SUM(words_added) as total_words, SUM(chapters_added) as total_chapters, SUM(time_spent) as total_time, COUNT(*) as writing_days"
	_res := vant2.DB("book_writing_stats").Where(_where).Fields(_fields).One()

	if _res == nil {
		return w.Map{
			"total_words":    0,
			"total_chapters": 0,
			"total_time":     0,
			"writing_days":   0,
		}
	}

	return _res.Map()
}

// GetYearlyStats 获取年统计
func (s *BookStatsService) GetYearlyStats(userId, bookId int64, year int) w.SliceMap {
	_startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	_endDate := time.Date(year, 12, 31, 23, 59, 59, 0, time.UTC)

	_where := w.Map{
		"user_id": userId,
		"date >=": _startDate.Format("2006-01-02"),
		"date <=": _endDate.Format("2006-01-02"),
	}

	if bookId > 0 {
		_where["book_id"] = bookId
	}

	// 按月聚合
	_fields := "YEAR(date) as year, MONTH(date) as month, SUM(words_added) as total_words, SUM(chapters_added) as total_chapters, SUM(time_spent) as total_time, COUNT(*) as writing_days"
	_res, _ := vant2.DB("book_writing_stats").Where(_where).Fields(_fields).Group("YEAR(date), MONTH(date)").Order("year, month").All()

	return gconv.SliceMap(_res)
}

// GetUserTotalStats 获取用户总体统计
func (s *BookStatsService) GetUserTotalStats(userId int64) w.Map {
	_where := w.Map{
		"user_id": userId,
	}

	// 总体统计
	_fields := "SUM(words_added) as total_words, SUM(chapters_added) as total_chapters, SUM(time_spent) as total_time, COUNT(DISTINCT date) as total_days, COUNT(DISTINCT book_id) as books_written"
	_res := vant2.DB("book_writing_stats").Where(_where).Fields(_fields).One()

	if _res == nil {
		return w.Map{
			"total_words":    0,
			"total_chapters": 0,
			"total_time":     0,
			"total_days":     0,
			"books_written":  0,
		}
	}

	_data := _res.Map()

	// 计算平均值
	_totalDays := gconv.Int(_data["total_days"])
	if _totalDays > 0 {
		_data["avg_words_per_day"] = gconv.Int(_data["total_words"]) / _totalDays
		_data["avg_time_per_day"] = gconv.Int(_data["total_time"]) / _totalDays
	} else {
		_data["avg_words_per_day"] = 0
		_data["avg_time_per_day"] = 0
	}

	return _data
}

// GetBookStats 获取单本小说的统计
func (s *BookStatsService) GetBookStats(userId, bookId int64) w.Map {
	_where := w.Map{
		"user_id": userId,
		"book_id": bookId,
	}

	// 小说统计
	_fields := "SUM(words_added) as total_words, SUM(chapters_added) as total_chapters, SUM(time_spent) as total_time, COUNT(*) as writing_days"
	_res := vant2.DB("book_writing_stats").Where(_where).Fields(_fields).One()

	if _res == nil {
		return w.Map{
			"total_words":    0,
			"total_chapters": 0,
			"total_time":     0,
			"writing_days":   0,
		}
	}

	_data := _res.Map()

	// 获取最早和最晚的写作日期
	_dateRange := vant2.DB("book_writing_stats").Where(_where).Fields("MIN(date) as start_date, MAX(date) as end_date").One()
	if _dateRange != nil {
		_data["start_date"] = _dateRange["start_date"]
		_data["end_date"] = _dateRange["end_date"]
	}

	return _data
}

// UpdateTodayWords 更新今日字数（用于小说表的today_words字段）
func (s *BookStatsService) UpdateTodayWords(bookId int64, wordsAdded int) bool {
	_today := time.Now().Format("2006-01-02")

	// 获取今日已记录的字数
	_todayStats := vant2.DB("book_writing_stats").Where(w.Map{
		"book_id": bookId,
		"date":    _today,
	}).One()

	_todayWords := wordsAdded
	if _todayStats != nil {
		_todayWords = gconv.Int(_todayStats["words_added"])
	}

	// 更新小说表的today_words字段
	return vant2.DB("book").Where("id", bookId).Data(w.Map{
		"today_words":     _todayWords,
		"last_write_time": vant2.Time(),
		"time_update":     vant2.Time(),
	}).Update()
}

// GetWritingHeatmap 获取写作热力图数据（一年的写作天数）
func (s *BookStatsService) GetWritingHeatmap(userId int64, year int) w.SliceMap {
	_startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	_endDate := time.Date(year, 12, 31, 23, 59, 59, 0, time.UTC)

	_where := w.Map{
		"user_id": userId,
		"date >=": _startDate.Format("2006-01-02"),
		"date <=": _endDate.Format("2006-01-02"),
	}

	_fields := "date, SUM(words_added) as words, SUM(time_spent) as time"
	_res, _ := vant2.DB("book_writing_stats").Where(_where).Fields(_fields).Group("date").Order("date").All()

	return gconv.SliceMap(_res)
}
